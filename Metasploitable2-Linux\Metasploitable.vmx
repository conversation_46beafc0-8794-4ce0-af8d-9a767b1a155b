#!/usr/bin/vmware
.encoding = "UTF-8"
config.version = "8"
virtualHW.version = "7"
numvcpus = "1"
maxvcpus = "4"
scsi0.present = "TRUE"
scsi0.virtualDev = "lsilogic"
memsize = "512"
ide1:0.present = "TRUE"
ide1:0.fileName = "auto detect"
ide1:0.deviceType = "cdrom-raw"
ethernet0.present = "TRUE"
ethernet0.connectionType = "nat"
ethernet0.wakeOnPcktRcv = "FALSE"
ethernet0.addressType = "generated"
usb.present = "TRUE"
ehci.present = "TRUE"
pciBridge0.present = "TRUE"
pciBridge4.present = "TRUE"
pciBridge4.virtualDev = "pcieRootPort"
pciBridge4.functions = "8"
pciBridge5.present = "TRUE"
pciBridge5.virtualDev = "pcieRootPort"
pciBridge5.functions = "8"
pciBridge6.present = "TRUE"
pciBridge6.virtualDev = "pcieRootPort"
pciBridge6.functions = "8"
pciBridge7.present = "TRUE"
pciBridge7.virtualDev = "pcieRootPort"
pciBridge7.functions = "8"
vmci0.present = "TRUE"
roamingVM.exitBehavior = "go"
displayName = "Metasploitable2-Linux"
guestOS = "ubuntu"
nvram = "Metasploitable.nvram"
virtualHW.productCompatibility = "hosted"
extendedConfigFile = "Metasploitable.vmxf"
ethernet0.generatedAddress = "00:0c:29:1a:02:47"
tools.syncTime = "FALSE"
uuid.location = "56 4d a7 b6 49 eb 2d eb-7b 81 20 17 1a 1a 02 47"
uuid.bios = "56 4d a7 b6 49 eb 2d eb-7b 81 20 17 1a 1a 02 47"
cleanShutdown = "FALSE"
replay.supported = "FALSE"
replay.filename = ""
pciBridge0.pciSlotNumber = "17"
pciBridge4.pciSlotNumber = "21"
pciBridge5.pciSlotNumber = "22"
pciBridge6.pciSlotNumber = "23"
pciBridge7.pciSlotNumber = "24"
scsi0.pciSlotNumber = "16"
usb.pciSlotNumber = "32"
ethernet0.pciSlotNumber = "33"
ehci.pciSlotNumber = "35"
vmci0.pciSlotNumber = "36"
vmotion.checkpointFBSize = "134217728"
ethernet0.generatedAddressOffset = "0"
vmci0.id = "363079114"
tools.remindInstall = "TRUE"
checkpoint.vmState = "Metasploitable-8f9ae34c.vmss"
annotation = "This is Metasploitable2 (Linux)|0A|0AMetasploitable is an intentionally vulnerable Linux virtual machine. This VM can be used to conduct security training, test security tools, and practice common penetration testing techniques. |0A|0AThe default login and password is msfadmin:msfadmin. |0A|0ANever expose this VM to an untrusted network (use NAT or Host-only mode if you have any questions what that means). |0A|0ATo contact the developers, please send <NAME_EMAIL>|0A|0A"
ide1:0.autodetect = "TRUE"
ide1:0.startConnected = "FALSE"
scsi0:0.present = "TRUE"
scsi0:0.fileName = "Metasploitable.vmdk"
scsi0:0.mode = "persistent"
scsi0:0.redo = ""
ethernet1.pciSlotNumber = "34"
ethernet1.present = "TRUE"
ethernet1.connectionType = "hostonly"
ethernet1.wakeOnPcktRcv = "FALSE"
ethernet1.addressType = "generated"
ethernet1.generatedAddress = "00:0c:29:1a:02:51"
ethernet1.generatedAddressOffset = "10"
sound.present = "FALSE"
scsi0:1.present = "FALSE"
floppy0.present = "FALSE"
vmxstats.filename = "Metasploitable.scoreboard"
numa.autosize.cookie = "10001"
numa.autosize.vcpu.maxPerVirtualNode = "1"
svga.vramSize = "134217728"
monitor.phys_bits_used = "40"
softPowerOff = "FALSE"
