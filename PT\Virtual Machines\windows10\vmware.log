2024-10-20T10:58:11.346Z In(05) vmx Log for VMware Workstation pid=10568 version=17.5.2 build=build-23775571 option=Release
2024-10-20T10:58:11.346Z In(05) vmx The host is x86_64.
2024-10-20T10:58:11.346Z In(05) vmx Host codepage=windows-1255 encoding=windows-1255
2024-10-20T10:58:11.346Z In(05) vmx Host is Windows 11 Pro, 64-bit (Build 22631.4317)
2024-10-20T10:58:11.346Z In(05) vmx Host offset from UTC is -02:00.
2024-10-20T10:58:11.323Z In(05) vmx VTHREAD 5940 "vmx"
2024-10-20T10:58:11.331Z In(05) vmx LOCALE windows-1255 -> NULL User=40d System=40d
2024-10-20T10:58:11.331Z In(05) vmx Msg_SetLocaleEx: HostLocale=windows-1255 UserLocale=NULL
2024-10-20T10:58:11.337Z In(05) vmx DictionaryLoad: Cannot open file "C:\Users\<USER>\AppData\Roaming\VMware\config.ini": The system cannot find the file specified.
2024-10-20T10:58:11.337Z In(05) vmx Msg_Reset:
2024-10-20T10:58:11.337Z In(05) vmx [msg.dictionary.load.openFailed] Cannot open file "C:\Users\<USER>\AppData\Roaming\VMware\config.ini": The system cannot find the file specified.
2024-10-20T10:58:11.337Z In(05) vmx ----------------------------------------
2024-10-20T10:58:11.337Z In(05) vmx ConfigDB: Failed to load C:\Users\<USER>\AppData\Roaming\VMware\config.ini
2024-10-20T10:58:11.337Z In(05) vmx Win32U_GetFileAttributes: GetFileAttributesExW("C:\Users\<USER>\Documents\Virtual Machines\windows10\windows10.vmpl", ...) failed, error: 2
2024-10-20T10:58:11.337Z In(05) vmx OBJLIB-LIB: Objlib initialized.
2024-10-20T10:58:11.337Z In(05) vmx DictionaryLoad: Cannot open file "C:\Users\<USER>\AppData\Roaming\VMware\config.ini": The system cannot find the file specified.
2024-10-20T10:58:11.337Z In(05) vmx [msg.dictionary.load.openFailed] Cannot open file "C:\Users\<USER>\AppData\Roaming\VMware\config.ini": The system cannot find the file specified.
2024-10-20T10:58:11.337Z In(05) vmx PREF Optional preferences file not found at C:\Users\<USER>\AppData\Roaming\VMware\config.ini. Using default values.
2024-10-20T10:58:11.341Z In(05) vmx SSL Error: error:80000002:system library::No such file or directory
2024-10-20T10:58:11.341Z In(05) vmx SSL Error: error:10000080:BIO routines::no such file
2024-10-20T10:58:11.341Z In(05) vmx SSL Error: error:07000072:configuration file routines::no such file
2024-10-20T10:58:11.341Z Wa(03) vmx SSLConfigLoad: Failed to load OpenSSL config file.
2024-10-20T10:58:11.341Z In(05) vmx lib/ssl: OpenSSL using default provider
2024-10-20T10:58:11.341Z In(05) vmx lib/ssl: Client usage
2024-10-20T10:58:11.341Z In(05) vmx lib/ssl: protocol list tls1.2
2024-10-20T10:58:11.341Z In(05) vmx lib/ssl: protocol min 0x303 max 0x303
2024-10-20T10:58:11.341Z In(05) vmx lib/ssl: protocol list tls1.2 (openssl flags 0x36000000)
2024-10-20T10:58:11.341Z In(05) vmx lib/ssl: cipher list ECDHE+AESGCM:RSA+AESGCM:ECDHE+AES:RSA+AES
2024-10-20T10:58:11.341Z In(05) vmx lib/ssl: cipher suites TLS_AES_128_GCM_SHA256:TLS_AES_256_GCM_SHA384
2024-10-20T10:58:11.341Z In(05) vmx lib/ssl: curves list prime256v1:secp384r1:secp521r1
2024-10-20T10:58:11.341Z In(05) vmx lib/ssl: Server usage
2024-10-20T10:58:11.341Z In(05) vmx lib/ssl: protocol list tls1.2
2024-10-20T10:58:11.341Z In(05) vmx lib/ssl: protocol min 0x303 max 0x303
2024-10-20T10:58:11.341Z In(05) vmx lib/ssl: protocol list tls1.2 (openssl flags 0x36000000)
2024-10-20T10:58:11.341Z In(05) vmx lib/ssl: cipher list ECDHE+AESGCM:RSA+AESGCM:ECDHE+AES:RSA+AES
2024-10-20T10:58:11.341Z In(05) vmx lib/ssl: cipher suites TLS_AES_128_GCM_SHA256:TLS_AES_256_GCM_SHA384
2024-10-20T10:58:11.341Z In(05) vmx lib/ssl: curves list prime256v1:secp384r1:secp521r1
2024-10-20T10:58:11.347Z In(05) vmx Hostname=DESKTOP-Q1DBRSH
2024-10-20T10:58:11.353Z In(05) vmx IP=fe80::f300:f556:1811:9be%17
2024-10-20T10:58:11.353Z In(05) vmx IP=fe80::a29d:2a26:193c:b90f%7
2024-10-20T10:58:11.353Z In(05) vmx IP=fe80::c36:873b:2a5f:d50d%4
2024-10-20T10:58:11.353Z In(05) vmx IP=************
2024-10-20T10:58:11.353Z In(05) vmx IP=************
2024-10-20T10:58:11.353Z In(05) vmx IP=************
2024-10-20T10:58:11.390Z In(05) vmx System uptime 679894928301 us
2024-10-20T10:58:11.390Z In(05) vmx Command line: "C:\Program Files (x86)\VMware\VMware Workstation\x64\vmware-vmx.exe" "-T" "querytoken" "-s" "vmx.stdio.keep=TRUE" "-#" "product=1;name=VMware Workstation;version=17.5.2;buildnumber=23775571;licensename=VMware Workstation;licenseversion=17.0;" "-@" "pipe=\\.\pipe\vmxaa309c6494e19543;msgs=ui" "C:\Users\<USER>\Documents\Virtual Machines\windows10\windows10.vmx"
2024-10-20T10:58:11.390Z In(05) vmx Msg_SetLocaleEx: HostLocale=windows-1255 UserLocale=NULL
2024-10-20T10:58:11.414Z In(05) vmx WQPoolAllocPoll : pollIx = 1, signalHandle = 856
2024-10-20T10:58:11.414Z In(05) vmx WQPoolAllocPoll : pollIx = 2, signalHandle = 864
2024-10-20T10:58:11.414Z In(05) vmx VigorTransport listening on fd 876
2024-10-20T10:58:11.414Z In(05) vmx Vigor_Init 1
2024-10-20T10:58:11.414Z In(05) vmx Connecting 'ui' to pipe '\\.\pipe\vmxaa309c6494e19543' with user '(null)'
2024-10-20T10:58:11.414Z In(05) vmx VMXVmdb: Local connection timeout: 60000 ms.
2024-10-20T10:58:11.498Z In(05) vmx VmdbAddConnection: cnxPath=/db/connection/#1/, cnxIx=1
2024-10-20T10:58:11.499Z In(05) vmx Vix: [mainDispatch.c:488]: VMAutomation: Initializing VMAutomation.
2024-10-20T10:58:11.499Z In(05) vmx Vix: [mainDispatch.c:740]: VMAutomationOpenListenerSocket() listening
2024-10-20T10:58:11.519Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=0, newAppState=1870, success=1 additionalError=0
2024-10-20T10:58:11.519Z In(05) vmx Transitioned vmx/execState/val to poweredOff
2024-10-20T10:58:11.519Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=1, newAppState=1873, success=1 additionalError=0
2024-10-20T10:58:11.519Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=2, newAppState=1877, success=1 additionalError=0
2024-10-20T10:58:11.519Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=3, newAppState=1881, success=1 additionalError=0
2024-10-20T10:58:11.572Z In(05) vmx IOPL_VBSRunning: VBS is set to 0
2024-10-20T10:58:11.574Z In(05) vmx IOCTL_VMX86_SET_MEMORY_PARAMS already set
2024-10-20T10:58:11.574Z In(05) vmx FeatureCompat: No EVC masks.
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID vendor: GenuineIntel
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID family: 0x6 model: 0x7e stepping: 0x5
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID codename: Ice Lake-U/Y
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID name: Intel(R) Core(TM) i3-1005G1 CPU @ 1.20GHz
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 00000000, 0: 0x0000001b 0x756e6547 0x6c65746e 0x49656e69
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 00000001, 0: 0x000706e5 0x00100800 0x7ffafbbf 0xbfebfbff
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 00000002, 0: 0x00feff01 0x000000f0 0x00000000 0x00000000
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 00000003, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 00000004, 0: 0x1c004121 0x02c0003f 0x0000003f 0x00000000
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 00000004, 1: 0x1c004122 0x01c0003f 0x0000003f 0x00000000
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 00000004, 2: 0x1c004143 0x01c0003f 0x000003ff 0x00000000
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 00000004, 3: 0x1c03c163 0x03c0003f 0x00000fff 0x00000006
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 00000004, 4: 0x00000000 0x00000000 0x00000000 0x00000000
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 00000005, 0: 0x00000040 0x00000040 0x00000003 0x11121020
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 00000006, 0: 0x0017aff7 0x00000002 0x00000009 0x00000000
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 00000007, 0: 0x00000000 0xf2bf27ef 0x40405f4e 0xbc000410
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 00000008, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 00000009, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 0000000a, 0: 0x08300805 0x00000000 0x0000000f 0x00008604
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 0000000b, 0: 0x00000001 0x00000002 0x00000100 0x00000000
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 0000000b, 1: 0x00000004 0x00000004 0x00000201 0x00000000
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 0000000b, 2: 0x00000000 0x00000000 0x00000002 0x00000000
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 0000000c, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 0000000d, 0: 0x000002e7 0x00000a80 0x00000a88 0x00000000
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 0000000d, 1: 0x0000000f 0x00000a00 0x00002100 0x00000000
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 0000000d, 2: 0x00000100 0x00000240 0x00000000 0x00000000
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 0000000d, 3: 0x00000000 0x00000000 0x00000000 0x00000000
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 0000000d, 4: 0x00000000 0x00000000 0x00000000 0x00000000
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 0000000d, 5: 0x00000040 0x00000440 0x00000000 0x00000000
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 0000000d, 6: 0x00000200 0x00000480 0x00000000 0x00000000
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 0000000d, 7: 0x00000400 0x00000680 0x00000000 0x00000000
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 0000000d, 8: 0x00000080 0x00000000 0x00000001 0x00000000
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 0000000d, 9: 0x00000008 0x00000a80 0x00000000 0x00000000
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 0000000d, a: 0x00000000 0x00000000 0x00000000 0x00000000
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 0000000d, b: 0x00000000 0x00000000 0x00000000 0x00000000
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 0000000d, c: 0x00000000 0x00000000 0x00000000 0x00000000
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 0000000d, d: 0x00000008 0x00000000 0x00000001 0x00000000
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 0000000e, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 0000000f, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 0000000f, 1: 0x00000000 0x00000000 0x00000000 0x00000000
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 00000010, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 00000010, 1: 0x00000000 0x00000000 0x00000000 0x00000000
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 00000010, 2: 0x00000000 0x00000000 0x00000000 0x00000000
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 00000010, 3: 0x00000000 0x00000000 0x00000000 0x00000000
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 00000011, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 00000012, 0: 0x00000063 0x00000001 0x00000000 0x00002f1f
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 00000012, 1: 0x000000b6 0x00000000 0x000002e7 0x00000000
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 00000012, 2: 0x50180001 0x00000000 0x0bc00001 0x00000000
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 00000012, 3: 0x00000000 0x00000000 0x00000000 0x00000000
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 00000013, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 00000014, 0: 0x00000001 0x0000000f 0x00000007 0x00000000
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 00000014, 1: 0x02490002 0x003f1fff 0x00000000 0x00000000
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 00000015, 0: 0x00000002 0x0000003e 0x0249f000 0x00000000
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 00000016, 0: 0x000004b0 0x00000d48 0x00000064 0x00000000
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 00000017, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 00000018, 0: 0x00000007 0x00000000 0x00000000 0x00000000
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 00000018, 1: 0x00000000 0x00080007 0x00000001 0x00004122
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 00000018, 2: 0x00000000 0x0010000f 0x00000001 0x00004125
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 00000018, 3: 0x00000000 0x00040001 0x00000010 0x00004024
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 00000018, 4: 0x00000000 0x00040006 0x00000008 0x00004024
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 00000018, 5: 0x00000000 0x00080008 0x00000001 0x00004124
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 00000018, 6: 0x00000000 0x00080007 0x00000080 0x00004043
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 00000018, 7: 0x00000000 0x00080009 0x00000080 0x00004043
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 00000019, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 0000001a, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 0000001b, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 80000000, 0: 0x80000008 0x00000000 0x00000000 0x00000000
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 80000001, 0: 0x00000000 0x00000000 0x00000121 0x2c100800
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 80000002, 0: 0x65746e49 0x2952286c 0x726f4320 0x4d542865
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 80000003, 0: 0x33692029 0x3030312d 0x20314735 0x20555043
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 80000004, 0: 0x2e312040 0x48473032 0x0000007a 0x00000000
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 80000005, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 80000006, 0: 0x00000000 0x00000000 0x01006040 0x00000000
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 80000007, 0: 0x00000000 0x00000000 0x00000000 0x00000100
2024-10-20T10:58:11.574Z In(05) vmx hostCPUID level 80000008, 0: 0x00003027 0x00000000 0x00000000 0x00000000
2024-10-20T10:58:11.574Z In(05) vmx CPUID differences from hostCPUID.
2024-10-20T10:58:11.574Z In(05) vmx Physical APIC IDs: 0-3
2024-10-20T10:58:11.574Z In(05) vmx Physical X2APIC IDs: 0-3
2024-10-20T10:58:11.574Z In(05) vmx Common: MSR       0x3a =            0x60005
2024-10-20T10:58:11.574Z In(05) vmx Common: MSR      0x480 =   0xda050000000013
2024-10-20T10:58:11.574Z In(05) vmx Common: MSR      0x481 =       0xff00000016
2024-10-20T10:58:11.574Z In(05) vmx Common: MSR      0x482 = 0xfff9fffe0401e172
2024-10-20T10:58:11.574Z In(05) vmx Common: MSR      0x483 =  0x37fffff00036dff
2024-10-20T10:58:11.574Z In(05) vmx Common: MSR      0x484 =    0x6ffff000011ff
2024-10-20T10:58:11.574Z In(05) vmx Common: MSR      0x485 =         0x7004c1e7
2024-10-20T10:58:11.574Z In(05) vmx Common: MSR      0x486 =         0x80000021
2024-10-20T10:58:11.574Z In(05) vmx Common: MSR      0x487 =         0xffffffff
2024-10-20T10:58:11.574Z In(05) vmx Common: MSR      0x488 =             0x2000
2024-10-20T10:58:11.574Z In(05) vmx Common: MSR      0x489 =           0x772fff
2024-10-20T10:58:11.574Z In(05) vmx Common: MSR      0x48a =               0x2e
2024-10-20T10:58:11.574Z In(05) vmx Common: MSR      0x48b = 0x335fbfff00000000
2024-10-20T10:58:11.574Z In(05) vmx Common: MSR      0x48c =      0xf0106734141
2024-10-20T10:58:11.574Z In(05) vmx Common: MSR      0x48d =       0xff00000016
2024-10-20T10:58:11.574Z In(05) vmx Common: MSR      0x48e = 0xfff9fffe04006172
2024-10-20T10:58:11.574Z In(05) vmx Common: MSR      0x48f =  0x37fffff00036dfb
2024-10-20T10:58:11.574Z In(05) vmx Common: MSR      0x490 =    0x6ffff000011fb
2024-10-20T10:58:11.574Z In(05) vmx Common: MSR      0x491 =                0x1
2024-10-20T10:58:11.574Z In(05) vmx Common: MSR      0x492 =                  0
2024-10-20T10:58:11.574Z In(05) vmx Common: MSR 0xc0010114 =                  0
2024-10-20T10:58:11.574Z In(05) vmx Common: MSR       0xce =         0x80000000
2024-10-20T10:58:11.574Z In(05) vmx Common: MSR      0x10a =               0x2b
2024-10-20T10:58:11.574Z In(05) vmx Common: MSR      0x122 =                  0
2024-10-20T10:58:11.574Z In(05) vmx VMMon_GetkHzEstimate: Calculated 1190389 kHz
2024-10-20T10:58:11.574Z In(05) vmx TSC Hz estimates: vmmon 1190389000, remembered 0, osReported 1190000000. Using 1190389000 Hz.
2024-10-20T10:58:11.574Z In(05) vmx TSC first measured delta 38
2024-10-20T10:58:11.574Z In(05) vmx TSC min delta 38
2024-10-20T10:58:11.574Z In(05) vmx PTSC: RefClockToPTSC 0 @ 10000000Hz -> 0 @ 1190389000Hz
2024-10-20T10:58:11.574Z In(05) vmx PTSC: RefClockToPTSC ((x * 3994282675) >> 25) + -61716101690620
2024-10-20T10:58:11.574Z In(05) vmx PTSC: tscOffset -61761276068386
2024-10-20T10:58:11.574Z In(05) vmx PTSC: using TSC
2024-10-20T10:58:11.574Z In(05) vmx PTSC: hardware TSCs are synchronized.
2024-10-20T10:58:11.574Z In(05) vmx PTSC: hardware TSCs may have been adjusted by the host.
2024-10-20T10:58:11.574Z In(05) vmx PTSC: current PTSC=48273
2024-10-20T10:58:11.583Z In(05) vmx WQPoolAllocPoll : pollIx = 3, signalHandle = 1100
2024-10-20T10:58:11.621Z In(05) vmx ConfigCheck: No rules file found. Checks are disabled.
2024-10-20T10:58:11.621Z In(05) vmx changing directory to C:\Users\<USER>\Documents\Virtual Machines\windows10\.
2024-10-20T10:58:11.621Z In(05) vmx Config file: C:\Users\<USER>\Documents\Virtual Machines\windows10\windows10.vmx
2024-10-20T10:58:11.621Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=1, newAppState=1873, success=1 additionalError=0
2024-10-20T10:58:11.621Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=2, newAppState=1878, success=1 additionalError=0
2024-10-20T10:58:11.624Z In(05) vmx LogRotation: Rotating to a new log file (keepOld 3) took 0.000080 seconds.
2024-10-20T10:58:11.629Z No(00) vmx LogVMXReplace: Successful switching from temporary to permanent log file took 0.008883 seconds.
2024-10-20T10:58:11.654Z Wa(03) vmx PowerOn
2024-10-20T10:58:11.654Z In(05) vmx VMX_PowerOn: VMX build 23775571, UI build 23775571
2024-10-20T10:58:11.654Z In(05) vmx HostWin32: WIN32 NUMA node 0, CPU mask 0x000000000000000f
2024-10-20T10:58:11.663Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=0, newAppState=1871, success=1 additionalError=0
2024-10-20T10:58:11.663Z No(00) vmx ConfigDB: Setting vmxstats.filename = "windows10.scoreboard"
2024-10-20T10:58:11.678Z In(05) vmx VMXSTATS: Successfully created stats file 'windows10.scoreboard'
2024-10-20T10:58:11.678Z In(05) vmx VMXSTATS: Update Product Information: VMware Workstation	17.5.2	build-23775571	Release  TotalBlockSize: 56
2024-10-20T10:58:11.678Z In(05) vmx HOST Windows version 10.0, build 22631, platform 2, ""
2024-10-20T10:58:11.678Z In(05) vmx DICT --- GLOBAL SETTINGS C:\ProgramData\VMware\VMware Workstation\settings.ini
2024-10-20T10:58:11.678Z In(05) vmx DICT          printers.enabled = "FALSE"
2024-10-20T10:58:11.678Z In(05) vmx DICT --- NON PERSISTENT (null)
2024-10-20T10:58:11.678Z In(05) vmx DICT --- USER PREFERENCES C:\Users\<USER>\AppData\Roaming\VMware\preferences.ini
2024-10-20T10:58:11.678Z In(05) vmx DICT pref.keyboardAndMouse.vmHotKey.enabled = "FALSE"
2024-10-20T10:58:11.678Z In(05) vmx DICT pref.keyboardAndMouse.vmHotKey.count = "0"
2024-10-20T10:58:11.678Z In(05) vmx DICT pref.wspro.firstRunDismissedVersion = "17.5.2"
2024-10-20T10:58:11.678Z In(05) vmx DICT pref.updatesVersionIgnore.numItems = "1"
2024-10-20T10:58:11.678Z In(05) vmx DICT pref.updatesVersionIgnore0.key = <not printed>
2024-10-20T10:58:11.678Z In(05) vmx DICT pref.updatesVersionIgnore0.value = "bb61d294-c7fd-4b93-bdb3-48a9b5b74f44"
2024-10-20T10:58:11.678Z In(05) vmx DICT   pref.lastUpdateCheckSec = "1729353589"
2024-10-20T10:58:11.678Z In(05) vmx DICT pref.ws.session.window.count = "1"
2024-10-20T10:58:11.678Z In(05) vmx DICT pref.ws.session.window0.tab.count = "2"
2024-10-20T10:58:11.678Z In(05) vmx DICT pref.ws.session.window0.tab0.cnxType = "vmdb"
2024-10-20T10:58:11.678Z In(05) vmx DICT pref.ws.session.window0.tab1.cnxType = "vmdb"
2024-10-20T10:58:11.678Z In(05) vmx DICT pref.ws.session.window0.sidebar = "TRUE"
2024-10-20T10:58:11.678Z In(05) vmx DICT pref.ws.session.window0.sidebar.width = "200"
2024-10-20T10:58:11.678Z In(05) vmx DICT pref.ws.session.window0.statusBar = "TRUE"
2024-10-20T10:58:11.678Z In(05) vmx DICT pref.ws.session.window0.tabs = "TRUE"
2024-10-20T10:58:11.678Z In(05) vmx DICT pref.ws.session.window0.thumbnailBar = "FALSE"
2024-10-20T10:58:11.678Z In(05) vmx DICT pref.ws.session.window0.thumbnailBar.size = "100"
2024-10-20T10:58:11.678Z In(05) vmx DICT pref.ws.session.window0.thumbnailBar.view = "opened-vms"
2024-10-20T10:58:11.678Z In(05) vmx DICT pref.ws.session.window0.placement.left = "128"
2024-10-20T10:58:11.678Z In(05) vmx DICT pref.ws.session.window0.placement.top = "128"
2024-10-20T10:58:11.678Z In(05) vmx DICT pref.ws.session.window0.placement.right = "972"
2024-10-20T10:58:11.678Z In(05) vmx DICT pref.ws.session.window0.placement.bottom = "751"
2024-10-20T10:58:11.678Z In(05) vmx DICT pref.ws.session.window0.maximized = "TRUE"
2024-10-20T10:58:11.678Z In(05) vmx DICT  pref.fullscreen.autohide = "TRUE"
2024-10-20T10:58:11.678Z In(05) vmx DICT pref.ws.session.window0.tab0.dest = ""
2024-10-20T10:58:11.678Z In(05) vmx DICT pref.ws.session.window0.tab0.file = "C:\Users\<USER>\Desktop\Metasploitable2-Linux\Metasploitable.vmx"
2024-10-20T10:58:11.678Z In(05) vmx DICT pref.ws.session.window0.tab0.type = "vm"
2024-10-20T10:58:11.678Z In(05) vmx DICT pref.ws.session.window0.tab0.focused = "FALSE"
2024-10-20T10:58:11.678Z In(05) vmx DICT pref.ws.session.window0.tab2.cnxType = "vmdb"
2024-10-20T10:58:11.678Z In(05) vmx DICT  pref.sharedFolder.maxNum = "1"
2024-10-20T10:58:11.678Z In(05) vmx DICT pref.sharedFolder0.vmPath = "/vm/#3babcb943734f8d6/"
2024-10-20T10:58:11.678Z In(05) vmx DICT pref.sharedFolder0.guestName = "Shared"
2024-10-20T10:58:11.678Z In(05) vmx DICT pref.sharedFolder0.hostPath = "/Volumes/Data/Shared"
2024-10-20T10:58:11.678Z In(05) vmx DICT pref.sharedFolder0.enabled = "FALSE"
2024-10-20T10:58:11.678Z In(05) vmx DICT  hint.vmui.showNewUSBDevs = "FALSE"
2024-10-20T10:58:11.678Z In(05) vmx DICT             hints.hideAll = "FALSE"
2024-10-20T10:58:11.678Z In(05) vmx DICT pref.ws.session.window0.tab1.dest = ""
2024-10-20T10:58:11.678Z In(05) vmx DICT pref.ws.session.window0.tab1.file = "C:\Users\<USER>\Documents\Virtual Machines\windows10\windows10.vmx"
2024-10-20T10:58:11.678Z In(05) vmx DICT pref.ws.session.window0.tab1.type = "vm"
2024-10-20T10:58:11.678Z In(05) vmx DICT pref.ws.session.window0.tab1.focused = "TRUE"
2024-10-20T10:58:11.678Z In(05) vmx DICT --- USER DEFAULTS C:\Users\<USER>\AppData\Roaming\VMware\config.ini
2024-10-20T10:58:11.678Z In(05) vmx DICT --- HOST DEFAULTS C:\ProgramData\VMware\VMware Workstation\config.ini
2024-10-20T10:58:11.678Z In(05) vmx DICT         authd.client.port = "902"
2024-10-20T10:58:11.678Z In(05) vmx DICT           authd.proxy.nfc = "vmware-hostd:ha-nfc"
2024-10-20T10:58:11.678Z In(05) vmx DICT installerDefaults.autoSoftwareUpdateEnabled = "yes"
2024-10-20T10:58:11.678Z In(05) vmx DICT installerDefaults.autoSoftwareUpdateEnabled.epoch = "5203"
2024-10-20T10:58:11.678Z In(05) vmx DICT installerDefaults.componentDownloadEnabled = "yes"
2024-10-20T10:58:11.678Z In(05) vmx DICT installerDefaults.dataCollectionEnabled = "yes"
2024-10-20T10:58:11.678Z In(05) vmx DICT installerDefaults.dataCollectionEnabled.epoch = "5203"
2024-10-20T10:58:11.678Z In(05) vmx DICT --- SITE DEFAULTS C:\ProgramData\VMware\VMware Workstation\config.ini
2024-10-20T10:58:11.678Z In(05) vmx DICT         authd.client.port = "902"
2024-10-20T10:58:11.678Z In(05) vmx DICT           authd.proxy.nfc = "vmware-hostd:ha-nfc"
2024-10-20T10:58:11.678Z In(05) vmx DICT installerDefaults.autoSoftwareUpdateEnabled = "yes"
2024-10-20T10:58:11.678Z In(05) vmx DICT installerDefaults.autoSoftwareUpdateEnabled.epoch = "5203"
2024-10-20T10:58:11.678Z In(05) vmx DICT installerDefaults.componentDownloadEnabled = "yes"
2024-10-20T10:58:11.678Z In(05) vmx DICT installerDefaults.dataCollectionEnabled = "yes"
2024-10-20T10:58:11.678Z In(05) vmx DICT installerDefaults.dataCollectionEnabled.epoch = "5203"
2024-10-20T10:58:11.678Z In(05) vmx DICT --- NONPERSISTENT
2024-10-20T10:58:11.678Z In(05) vmx DICT            vmx.stdio.keep = "TRUE"
2024-10-20T10:58:11.678Z In(05) vmx DICT             gui.available = "TRUE"
2024-10-20T10:58:11.678Z In(05) vmx DICT --- COMMAND LINE
2024-10-20T10:58:11.678Z In(05) vmx DICT            vmx.stdio.keep = "TRUE"
2024-10-20T10:58:11.678Z In(05) vmx DICT             gui.available = "TRUE"
2024-10-20T10:58:11.678Z In(05) vmx DICT --- RECORDING
2024-10-20T10:58:11.678Z In(05) vmx DICT            vmx.stdio.keep = "TRUE"
2024-10-20T10:58:11.678Z In(05) vmx DICT             gui.available = "TRUE"
2024-10-20T10:58:11.678Z In(05) vmx DICT --- CONFIGURATION C:\Users\<USER>\Documents\Virtual Machines\windows10\windows10.vmx 
2024-10-20T10:58:11.678Z In(05) vmx DICT               displayname = "windows10"
2024-10-20T10:58:11.678Z In(05) vmx DICT                   guestos = "windows9-64"
2024-10-20T10:58:11.678Z In(05) vmx DICT         virtualhw.version = "12"
2024-10-20T10:58:11.678Z In(05) vmx DICT            config.version = "8"
2024-10-20T10:58:11.678Z In(05) vmx DICT                  numvcpus = "2"
2024-10-20T10:58:11.678Z In(05) vmx DICT                   memsize = "2048"
2024-10-20T10:58:11.683Z In(05) vmx DICT              cpuid.numSMT = "1"
2024-10-20T10:58:11.683Z In(05) vmx DICT    numa.vcpu.coresPerNode = "0"
2024-10-20T10:58:11.683Z In(05) vmx DICT        pciBridge0.present = "TRUE"
2024-10-20T10:58:11.683Z In(05) vmx DICT        pciBridge4.present = "TRUE"
2024-10-20T10:58:11.683Z In(05) vmx DICT     pciBridge4.virtualDev = "pcieRootPort"
2024-10-20T10:58:11.683Z In(05) vmx DICT      pciBridge4.functions = "8"
2024-10-20T10:58:11.683Z In(05) vmx DICT        pciBridge5.present = "TRUE"
2024-10-20T10:58:11.683Z In(05) vmx DICT     pciBridge5.virtualDev = "pcieRootPort"
2024-10-20T10:58:11.683Z In(05) vmx DICT      pciBridge5.functions = "8"
2024-10-20T10:58:11.683Z In(05) vmx DICT        pciBridge6.present = "TRUE"
2024-10-20T10:58:11.683Z In(05) vmx DICT     pciBridge6.virtualDev = "pcieRootPort"
2024-10-20T10:58:11.683Z In(05) vmx DICT      pciBridge6.functions = "8"
2024-10-20T10:58:11.683Z In(05) vmx DICT        pciBridge7.present = "TRUE"
2024-10-20T10:58:11.683Z In(05) vmx DICT     pciBridge7.virtualDev = "pcieRootPort"
2024-10-20T10:58:11.683Z In(05) vmx DICT      pciBridge7.functions = "8"
2024-10-20T10:58:11.683Z In(05) vmx DICT             vmci0.present = "TRUE"
2024-10-20T10:58:11.683Z In(05) vmx DICT           floppy0.present = "FALSE"
2024-10-20T10:58:11.683Z In(05) vmx DICT       ide0:0.clientDevice = "FALSE"
2024-10-20T10:58:11.683Z In(05) vmx DICT            ide0:0.present = "TRUE"
2024-10-20T10:58:11.683Z In(05) vmx DICT         ide0:0.deviceType = "atapi-cdrom"
2024-10-20T10:58:11.683Z In(05) vmx DICT         ide0:0.autodetect = "TRUE"
2024-10-20T10:58:11.683Z In(05) vmx DICT     ide0:0.startConnected = "FALSE"
2024-10-20T10:58:11.683Z In(05) vmx DICT ide0:0.allowguestconnectioncontrol = "FALSE"
2024-10-20T10:58:11.683Z In(05) vmx DICT           scsi0:0.present = "TRUE"
2024-10-20T10:58:11.683Z In(05) vmx DICT        scsi0:0.deviceType = "disk"
2024-10-20T10:58:11.683Z In(05) vmx DICT          scsi0:0.fileName = "windows10-disk1.vmdk"
2024-10-20T10:58:11.683Z In(05) vmx DICT scsi0:0.allowguestconnectioncontrol = "false"
2024-10-20T10:58:11.683Z In(05) vmx DICT              scsi0:0.mode = "persistent"
2024-10-20T10:58:11.683Z In(05) vmx DICT          scsi0.virtualDev = "lsisas1068"
2024-10-20T10:58:11.683Z In(05) vmx DICT             scsi0.present = "TRUE"
2024-10-20T10:58:11.683Z In(05) vmx DICT         ethernet0.present = "TRUE"
2024-10-20T10:58:11.683Z In(05) vmx DICT      ethernet0.virtualDev = "e1000"
2024-10-20T10:58:11.683Z In(05) vmx DICT  ethernet0.connectionType = "nat"
2024-10-20T10:58:11.683Z In(05) vmx DICT  ethernet0.startConnected = "TRUE"
2024-10-20T10:58:11.683Z In(05) vmx DICT     ethernet0.addressType = "generated"
2024-10-20T10:58:11.683Z In(05) vmx DICT   ethernet0.pciSlotNumber = "33"
2024-10-20T10:58:11.683Z In(05) vmx DICT   ethernet0.wakeonpcktrcv = "FALSE"
2024-10-20T10:58:11.683Z In(05) vmx DICT ethernet0.allowguestconnectioncontrol = "FALSE"
2024-10-20T10:58:11.683Z In(05) vmx DICT        vmci0.unrestricted = "FALSE"
2024-10-20T10:58:11.683Z In(05) vmx DICT            tools.syncTime = "TRUE"
2024-10-20T10:58:11.683Z In(05) vmx DICT  toolscripts.afterpoweron = "TRUE"
2024-10-20T10:58:11.683Z In(05) vmx DICT   toolscripts.afterresume = "TRUE"
2024-10-20T10:58:11.683Z In(05) vmx DICT toolscripts.beforepoweroff = "TRUE"
2024-10-20T10:58:11.683Z In(05) vmx DICT toolscripts.beforesuspend = "TRUE"
2024-10-20T10:58:11.683Z In(05) vmx DICT      tools.upgrade.policy = "upgradeAtPowerCycle"
2024-10-20T10:58:11.683Z In(05) vmx DICT        powerType.powerOff = "soft"
2024-10-20T10:58:11.683Z In(05) vmx DICT           powerType.reset = "soft"
2024-10-20T10:58:11.683Z In(05) vmx DICT         powerType.suspend = "soft"
2024-10-20T10:58:11.683Z In(05) vmx DICT                     nvram = "windows10-file1.nvram"
2024-10-20T10:58:11.683Z In(05) vmx DICT virtualhw.productcompatibility = "hosted"
2024-10-20T10:58:11.683Z In(05) vmx DICT        extendedConfigFile = "windows10.vmxf"
2024-10-20T10:58:11.683Z In(05) vmx DICT         vmxstats.filename = "windows10.scoreboard"
2024-10-20T10:58:11.683Z In(05) vmx DICT --- USER DEFAULTS C:\Users\<USER>\AppData\Roaming\VMware\config.ini 
2024-10-20T10:58:11.683Z In(05) vmx DICT --- HOST DEFAULTS C:\ProgramData\VMware\VMware Workstation\config.ini 
2024-10-20T10:58:11.683Z In(05) vmx DICT         authd.client.port = "902"
2024-10-20T10:58:11.683Z In(05) vmx DICT           authd.proxy.nfc = "vmware-hostd:ha-nfc"
2024-10-20T10:58:11.683Z In(05) vmx DICT installerDefaults.autoSoftwareUpdateEnabled = "yes"
2024-10-20T10:58:11.683Z In(05) vmx DICT installerDefaults.autoSoftwareUpdateEnabled.epoch = "5203"
2024-10-20T10:58:11.683Z In(05) vmx DICT installerDefaults.componentDownloadEnabled = "yes"
2024-10-20T10:58:11.683Z In(05) vmx DICT installerDefaults.dataCollectionEnabled = "yes"
2024-10-20T10:58:11.683Z In(05) vmx DICT installerDefaults.dataCollectionEnabled.epoch = "5203"
2024-10-20T10:58:11.683Z In(05) vmx DICT --- SITE DEFAULTS C:\ProgramData\VMware\VMware Workstation\config.ini 
2024-10-20T10:58:11.683Z In(05) vmx DICT         authd.client.port = "902"
2024-10-20T10:58:11.683Z In(05) vmx DICT           authd.proxy.nfc = "vmware-hostd:ha-nfc"
2024-10-20T10:58:11.683Z In(05) vmx DICT installerDefaults.autoSoftwareUpdateEnabled = "yes"
2024-10-20T10:58:11.683Z In(05) vmx DICT installerDefaults.autoSoftwareUpdateEnabled.epoch = "5203"
2024-10-20T10:58:11.683Z In(05) vmx DICT installerDefaults.componentDownloadEnabled = "yes"
2024-10-20T10:58:11.683Z In(05) vmx DICT installerDefaults.dataCollectionEnabled = "yes"
2024-10-20T10:58:11.683Z In(05) vmx DICT installerDefaults.dataCollectionEnabled.epoch = "5203"
2024-10-20T10:58:11.683Z In(05) vmx DICT --- GLOBAL SETTINGS C:\ProgramData\VMware\VMware Workstation\settings.ini 
2024-10-20T10:58:11.683Z In(05) vmx DICT          printers.enabled = "FALSE"
2024-10-20T10:58:11.683Z In(05) vmx VMXSTATS: Registering 1 stats: vmx.diskLibDataVmdkOpenTime
2024-10-20T10:58:11.683Z In(05) vmx VMXSTATS: Registering 2 stats: vmx.diskLibDataVmdkOpenTime
2024-10-20T10:58:11.683Z In(05) vmx VMXSTATS: Registering 3 stats: vmx.diskLibDataVmdkGrowTime
2024-10-20T10:58:11.683Z In(05) vmx VMXSTATS: Registering 4 stats: vmx.diskLibDataVmdkGrowTime
2024-10-20T10:58:11.683Z In(05) vmx VMXSTATS: Registering 5 stats: vmx.diskLibDigestVmdkOpenTime
2024-10-20T10:58:11.683Z In(05) vmx VMXSTATS: Registering 6 stats: vmx.diskLibDigestVmdkOpenTime
2024-10-20T10:58:11.683Z In(05) vmx VMXSTATS: Registering 7 stats: vmx.diskLibDigestVmdkGrowTime
2024-10-20T10:58:11.683Z In(05) vmx VMXSTATS: Registering 8 stats: vmx.diskLibDigestVmdkGrowTime
2024-10-20T10:58:11.683Z In(05) vmx VMXSTATS: Registering 9 stats: vmx.diskLibDigestFileDataGrowTime
2024-10-20T10:58:11.683Z In(05) vmx VMXSTATS: Registering 10 stats: vmx.diskLibDigestFileDataGrowTime
2024-10-20T10:58:11.683Z In(05) vmx VMXSTATS: Registering 11 stats: vmx.digestLibOpenIntTime
2024-10-20T10:58:11.683Z In(05) vmx VMXSTATS: Registering 12 stats: vmx.digestLibOpenIntTime
2024-10-20T10:58:11.683Z In(05) vmx VMXSTATS: Registering 13 stats: vmx.diskLibDataVmdkCloseTime
2024-10-20T10:58:11.683Z In(05) vmx VMXSTATS: Registering 14 stats: vmx.diskLibDataVmdkCloseTime
2024-10-20T10:58:11.683Z In(05) vmx VMXSTATS: Registering 15 stats: vmx.diskLibDigestVmdkCloseTime
2024-10-20T10:58:11.683Z In(05) vmx VMXSTATS: Registering 16 stats: vmx.diskLibDigestVmdkCloseTime
2024-10-20T10:58:11.683Z In(05) vmx VMXSTATS: Registering 17 stats: vmx.diskLibVmdkCreateTime
2024-10-20T10:58:11.683Z In(05) vmx VMXSTATS: Registering 18 stats: vmx.diskLibVmdkCreateTime
2024-10-20T10:58:11.683Z In(05) vmx VMXSTATS: Registering 19 stats: vmx.diskLibChildVmdkCreateTime
2024-10-20T10:58:11.683Z In(05) vmx VMXSTATS: Registering 20 stats: vmx.diskLibChildVmdkCreateTime
2024-10-20T10:58:11.683Z In(05) vmx VMXSTATS: Registering 21 stats: vmx.snapshotCreateTime
2024-10-20T10:58:11.683Z In(05) vmx VMXSTATS: Registering 22 stats: vmx.snapshotCreateTime
2024-10-20T10:58:11.683Z In(05) vmx VMXSTATS: Registering 23 stats: vmx.snapshotCreateQuiescedTime
2024-10-20T10:58:11.683Z In(05) vmx VMXSTATS: Registering 24 stats: vmx.snapshotCreateQuiescedTime
2024-10-20T10:58:11.683Z In(05) vmx VMXSTATS: Registering 25 stats: vmx.snapshotCreateMemoryTime
2024-10-20T10:58:11.683Z In(05) vmx VMXSTATS: Registering 26 stats: vmx.snapshotCreateMemoryTime
2024-10-20T10:58:11.683Z In(05) vmx VMXSTATS: Registering 27 stats: vmx.snapshotDeleteTime
2024-10-20T10:58:11.683Z In(05) vmx VMXSTATS: Registering 28 stats: vmx.snapshotDeleteTime
2024-10-20T10:58:11.683Z In(05) vmx VMXSTATS: Registering 29 stats: vmx.snapshotConsolidateTime
2024-10-20T10:58:11.683Z In(05) vmx VMXSTATS: Registering 30 stats: vmx.snapshotConsolidateTime
2024-10-20T10:58:11.683Z In(05) vmx VMXSTATS: Registering 31 stats: vmx.checkpointStunTime
2024-10-20T10:58:11.683Z In(05) vmx VMXSTATS: Registering 32 stats: vmx.checkpointStunTime
2024-10-20T10:58:11.683Z In(05) vmx VMXSTATS: Registering 33 stats: vmx.setPolicyTime
2024-10-20T10:58:11.683Z In(05) vmx VMXSTATS: Registering 34 stats: vmx.setPolicyTime
2024-10-20T10:58:11.683Z In(05) vmx VMXSTATS: Registering 35 stats: vmx.filtlibApplyDiskConfigTime
2024-10-20T10:58:11.683Z In(05) vmx VMXSTATS: Registering 36 stats: vmx.filtlibApplyDiskConfigTime
2024-10-20T10:58:11.683Z In(05) vmx VMXSTATS: Registering 37 stats: vmx.diskLibGetInfoTime
2024-10-20T10:58:11.683Z In(05) vmx VMXSTATS: Registering 38 stats: vmx.diskLibGetInfoTime
2024-10-20T10:58:11.683Z In(05) vmx VMXSTATS: Registering 39 stats: vmx.diskLibDigestGetInfoTime
2024-10-20T10:58:11.683Z In(05) vmx VMXSTATS: Registering 40 stats: vmx.diskLibDigestGetInfoTime
2024-10-20T10:58:11.683Z In(05) vmx Powering on guestOS 'windows9-64' using the configuration for 'windows9-64'.
2024-10-20T10:58:11.694Z In(05) vmx ToolsISO: open of C:\Program Files (x86)\VMware\VMware Workstation\isoimages_manifest.txt.sig failed: Could not find the file
2024-10-20T10:58:11.694Z In(05) vmx ToolsISO: Unable to read signature file 'C:\Program Files (x86)\VMware\VMware Workstation\isoimages_manifest.txt.sig', ignoring.
2024-10-20T10:58:11.694Z In(05) vmx ToolsISO: Updated cached value for imageName to 'windows.iso'.
2024-10-20T10:58:11.694Z In(05) vmx ToolsISO: Selected Tools ISO 'windows.iso' for 'windows9-64' guest.
2024-10-20T10:58:11.694Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=1, newAppState=1873, success=1 additionalError=0
2024-10-20T10:58:11.694Z In(05) vmx Win32U_GetFileAttributes: GetFileAttributesExW("C:\Users\<USER>\Documents\Virtual Machines\windows10\windows10-file1.nvram", ...) failed, error: 2
2024-10-20T10:58:11.694Z In(05) vmx Monitor Mode: CPL0
2024-10-20T10:58:11.710Z In(05) vmx OvhdMem_PowerOn: initial admission: paged   408241 nonpaged    36401 anonymous     6660
2024-10-20T10:58:11.710Z In(05) vmx VMMEM: Initial Reservation: 1762MB (MainMem=2048MB)
2024-10-20T10:58:11.710Z In(05) vmx numa: Invalid NUMA cookie.
2024-10-20T10:58:11.710Z No(00) vmx ConfigDB: Setting numa.autosize.cookie = "20001"
2024-10-20T10:58:11.710Z No(00) vmx ConfigDB: Setting numa.autosize.vcpu.maxPerVirtualNode = "2"
2024-10-20T10:58:11.721Z In(05) vmx VMXSTATS: Registering 41 stats: vmx.configWriteMinMaxTime
2024-10-20T10:58:11.721Z In(05) vmx VMXSTATS: Registering 42 stats: vmx.configWriteAvgTime
2024-10-20T10:58:11.728Z In(05) vmx llc: maximum vcpus per LLC: 1
2024-10-20T10:58:11.728Z In(05) vmx llc: vLLC size: 1
2024-10-20T10:58:11.728Z In(05) vmx MemSched_PowerOn: balloon minGuestSize 419430 (80% of min required size 524288)
2024-10-20T10:58:11.728Z In(05) vmx MemSched: reserved mem (in MB) min 128 max 6200 recommended 6200
2024-10-20T10:58:11.728Z In(05) vmx MemSched: pg 408241 np 36401 anon 6660 mem 524288
2024-10-20T10:58:11.785Z In(05) vmx MemSched: numvm 1 locked pages: num 0 max 1579008
2024-10-20T10:58:11.785Z In(05) vmx MemSched: locked Page Limit: host 1684949 config 1587200
2024-10-20T10:58:11.785Z In(05) vmx MemSched: minmempct 50 minalloc 0 admitted 1
2024-10-20T10:58:11.793Z In(05) PowerNotifyThread VTHREAD 5456 "PowerNotifyThread"
2024-10-20T10:58:11.793Z In(05) PowerNotifyThread PowerNotify thread is alive.
2024-10-20T10:58:11.793Z In(05) vmx VMXSTATS: Registering 43 stats: vmx.logBytesDropped
2024-10-20T10:58:11.793Z In(05) vmx VMXSTATS: Registering 44 stats: vmx.logMsgsDropped
2024-10-20T10:58:11.793Z In(05) vmx VMXSTATS: Registering 45 stats: vmx.logBytesLogged
2024-10-20T10:58:11.793Z In(05) vmx VMXSTATS: Registering 46 stats: vmx.logWriteMinMaxTime
2024-10-20T10:58:11.793Z In(05) vmx VMXSTATS: Registering 47 stats: vmx.logWriteAvgTime
2024-10-20T10:58:11.793Z In(05) vmx LICENSE: Running unlicensed VMX (VMware Workstation)
2024-10-20T10:58:11.793Z In(05) vthread-6188 VTHREAD 6188 "vthread-6188"
2024-10-20T10:58:11.793Z In(05) vmx Win32U_GetFileAttributes: GetFileAttributesExW("C:\Users\<USER>\Documents\Virtual Machines\windows10\windows10.vmpl", ...) failed, error: 2
2024-10-20T10:58:11.793Z In(05) vmx PolicyVMXFindPolicyKey: policy file does not exist.
2024-10-20T10:58:11.796Z In(05) vmx Win32U_GetFileAttributes: GetFileAttributesExW("C:\Users\<USER>\Documents\Virtual Machines\windows10\windows10.vmpl", ...) failed, error: 2
2024-10-20T10:58:11.796Z In(05) vmx PolicyVMXFindPolicyKey: policy file does not exist.
2024-10-20T10:58:11.796Z In(05) vmx Host PA size: 39 bits. Guest PA size: 42 bits.
2024-10-20T10:58:11.796Z In(05) vmx ToolsISO: Refreshing imageName for 'windows9-64' (refreshCount=1, lastCount=1).
2024-10-20T10:58:11.796Z In(05) vmx ToolsISO: open of C:\Program Files (x86)\VMware\VMware Workstation\isoimages_manifest.txt.sig failed: Could not find the file
2024-10-20T10:58:11.796Z In(05) vmx ToolsISO: Unable to read signature file 'C:\Program Files (x86)\VMware\VMware Workstation\isoimages_manifest.txt.sig', ignoring.
2024-10-20T10:58:11.796Z In(05) vmx ToolsISO: Updated cached value for imageName to 'windows.iso'.
2024-10-20T10:58:11.796Z In(05) vmx ToolsISO: Selected Tools ISO 'windows.iso' for 'windows9-64' guest.
2024-10-20T10:58:11.796Z In(05) deviceThread VTHREAD 3720 "deviceThread"
2024-10-20T10:58:11.796Z In(05) vmx Host VT-x Capabilities:
2024-10-20T10:58:11.796Z In(05) vmx Basic VMX Information (0x00da050000000013)
2024-10-20T10:58:11.796Z In(05) vmx   VMCS revision ID                          19
2024-10-20T10:58:11.796Z In(05) vmx   VMCS region length                      1280
2024-10-20T10:58:11.796Z In(05) vmx   VMX physical-address width           natural
2024-10-20T10:58:11.796Z In(05) vmx   SMM dual-monitor mode                    yes
2024-10-20T10:58:11.796Z In(05) vmx   VMCS memory type                          WB
2024-10-20T10:58:11.796Z In(05) vmx   Advanced INS/OUTS info                   yes
2024-10-20T10:58:11.796Z In(05) vmx   True VMX MSRs                            yes
2024-10-20T10:58:11.796Z In(05) vmx   Exception Injection ignores error code    no
2024-10-20T10:58:11.796Z In(05) vmx True Pin-Based VM-Execution Controls (0x000000ff00000016)
2024-10-20T10:58:11.796Z In(05) vmx   External-interrupt exiting               {0,1}
2024-10-20T10:58:11.796Z In(05) vmx   NMI exiting                              {0,1}
2024-10-20T10:58:11.796Z In(05) vmx   Virtual NMIs                             {0,1}
2024-10-20T10:58:11.796Z In(05) vmx   Activate VMX-preemption timer            {0,1}
2024-10-20T10:58:11.796Z In(05) vmx   Process posted interrupts                {0,1}
2024-10-20T10:58:11.796Z In(05) vmx True Primary Processor-Based VM-Execution Controls (0xfff9fffe04006172)
2024-10-20T10:58:11.796Z In(05) vmx   Interrupt-window exiting                 {0,1}
2024-10-20T10:58:11.796Z In(05) vmx   Use TSC offsetting                       {0,1}
2024-10-20T10:58:11.796Z In(05) deviceThread Device thread is alive
2024-10-20T10:58:11.796Z In(05) vmx   HLT exiting                              {0,1}
2024-10-20T10:58:11.796Z In(05) vmx   INVLPG exiting                           {0,1}
2024-10-20T10:58:11.796Z In(05) vmx   MWAIT exiting                            {0,1}
2024-10-20T10:58:11.796Z In(05) vmx   RDPMC exiting                            {0,1}
2024-10-20T10:58:11.796Z In(05) vmx   RDTSC exiting                            {0,1}
2024-10-20T10:58:11.796Z In(05) vmx   CR3-load exiting                         {0,1}
2024-10-20T10:58:11.796Z In(05) vmx   CR3-store exiting                        {0,1}
2024-10-20T10:58:11.796Z In(05) vmx   Activate tertiary controls               { 0 }
2024-10-20T10:58:11.796Z In(05) vmx   CR8-load exiting                         {0,1}
2024-10-20T10:58:11.796Z In(05) vmx   CR8-store exiting                        {0,1}
2024-10-20T10:58:11.796Z In(05) vmx   Use TPR shadow                           {0,1}
2024-10-20T10:58:11.796Z In(05) vmx   NMI-window exiting                       {0,1}
2024-10-20T10:58:11.796Z In(05) vmx   MOV-DR exiting                           {0,1}
2024-10-20T10:58:11.796Z In(05) vmx   Unconditional I/O exiting                {0,1}
2024-10-20T10:58:11.796Z In(05) vmx   Use I/O bitmaps                          {0,1}
2024-10-20T10:58:11.796Z In(05) vmx   Monitor trap flag                        {0,1}
2024-10-20T10:58:11.796Z In(05) vmx   Use MSR bitmaps                          {0,1}
2024-10-20T10:58:11.796Z In(05) vmx   MONITOR exiting                          {0,1}
2024-10-20T10:58:11.796Z In(05) vmx   PAUSE exiting                            {0,1}
2024-10-20T10:58:11.796Z In(05) vmx   Activate secondary controls              {0,1}
2024-10-20T10:58:11.796Z In(05) vmx Secondary Processor-Based VM-Execution Controls (0x335fbfff00000000)
2024-10-20T10:58:11.796Z In(05) vmx   Virtualize APIC accesses                 {0,1}
2024-10-20T10:58:11.796Z In(05) vmx   Enable EPT                               {0,1}
2024-10-20T10:58:11.796Z In(05) vmx   Descriptor-table exiting                 {0,1}
2024-10-20T10:58:11.796Z In(05) vmx   Enable RDTSCP                            {0,1}
2024-10-20T10:58:11.796Z In(05) vmx   Virtualize x2APIC mode                   {0,1}
2024-10-20T10:58:11.796Z In(05) vmx   Enable VPID                              {0,1}
2024-10-20T10:58:11.796Z In(05) vmx   WBINVD exiting                           {0,1}
2024-10-20T10:58:11.796Z In(05) vmx   Unrestricted guest                       {0,1}
2024-10-20T10:58:11.796Z In(05) vmx   APIC-register virtualization             {0,1}
2024-10-20T10:58:11.796Z In(05) vmx   Virtual-interrupt delivery               {0,1}
2024-10-20T10:58:11.796Z In(05) vmx   PAUSE-loop exiting                       {0,1}
2024-10-20T10:58:11.796Z In(05) vmx   RDRAND exiting                           {0,1}
2024-10-20T10:58:11.796Z In(05) vmx   Enable INVPCID                           {0,1}
2024-10-20T10:58:11.796Z In(05) vmx   Enable VM Functions                      {0,1}
2024-10-20T10:58:11.796Z In(05) vmx   Use VMCS shadowing                       { 0 }
2024-10-20T10:58:11.796Z In(05) vmx   ENCLS exiting                            {0,1}
2024-10-20T10:58:11.796Z In(05) vmx   RDSEED exiting                           {0,1}
2024-10-20T10:58:11.796Z In(05) vmx   Enable PML                               {0,1}
2024-10-20T10:58:11.796Z In(05) vmx   EPT-violation #VE                        {0,1}
2024-10-20T10:58:11.796Z In(05) vmx   Conceal VMX from PT                      {0,1}
2024-10-20T10:58:11.796Z In(05) vmx   Enable XSAVES/XRSTORS                    {0,1}
2024-10-20T10:58:11.796Z In(05) vmx   PASID translation                        { 0 }
2024-10-20T10:58:11.796Z In(05) vmx   Mode-based execute control for EPT       {0,1}
2024-10-20T10:58:11.796Z In(05) vmx   Sub-page write permissions for EPT       { 0 }
2024-10-20T10:58:11.796Z In(05) vmx   PT uses guest physical addresses         {0,1}
2024-10-20T10:58:11.796Z In(05) vmx   Use TSC scaling                          {0,1}
2024-10-20T10:58:11.796Z In(05) vmx   Enable UMWAIT and TPAUSE                 { 0 }
2024-10-20T10:58:11.796Z In(05) vmx   Enable ENCLV in VMX non-root mode        {0,1}
2024-10-20T10:58:11.796Z In(05) vmx   Enable EPC Virtualization Extensions     {0,1}
2024-10-20T10:58:11.796Z In(05) vmx   Bus lock exiting                         { 0 }
2024-10-20T10:58:11.796Z In(05) vmx   Notification VM exits                    { 0 }
2024-10-20T10:58:11.796Z In(05) vmx Tertiary Processor-Based VM-Execution Controls (0x0000000000000000)
2024-10-20T10:58:11.796Z In(05) vmx   LOADIWKEY exiting                          no
2024-10-20T10:58:11.796Z In(05) vmx   Enable HLAT                                no
2024-10-20T10:58:11.796Z In(05) vmx   Enable Paging-Write                        no
2024-10-20T10:58:11.796Z In(05) vmx   Enable Guest Paging Verification           no
2024-10-20T10:58:11.796Z In(05) vmx   Enable IPI Virtualization                  no
2024-10-20T10:58:11.796Z In(05) vmx   Enable Virtual MSR_SPEC_CTRL               no
2024-10-20T10:58:11.796Z In(05) vmx True VM-Exit Controls (0x037fffff00036dfb)
2024-10-20T10:58:11.796Z In(05) vmx   Save debug controls                      {0,1}
2024-10-20T10:58:11.796Z In(05) vmx   Host address-space size                  {0,1}
2024-10-20T10:58:11.796Z In(05) vmx   Load IA32_PERF_GLOBAL_CTRL               {0,1}
2024-10-20T10:58:11.796Z In(05) vmx   Acknowledge interrupt on exit            {0,1}
2024-10-20T10:58:11.796Z In(05) vmx   Save IA32_PAT                            {0,1}
2024-10-20T10:58:11.796Z In(05) vmx   Load IA32_PAT                            {0,1}
2024-10-20T10:58:11.796Z In(05) vmx   Save IA32_EFER                           {0,1}
2024-10-20T10:58:11.796Z In(05) vmx   Load IA32_EFER                           {0,1}
2024-10-20T10:58:11.796Z In(05) vmx   Save VMX-preemption timer                {0,1}
2024-10-20T10:58:11.796Z In(05) vmx   Clear IA32_BNDCFGS                       { 0 }
2024-10-20T10:58:11.796Z In(05) vmx   Conceal VMX from processor trace         {0,1}
2024-10-20T10:58:11.796Z In(05) vmx   Clear IA32_RTIT MSR                      {0,1}
2024-10-20T10:58:11.796Z In(05) vmx   Clear IA32_LBR_CTL MSR                   { 0 }
2024-10-20T10:58:11.796Z In(05) vmx   Clear user-interrupt notification vector { 0 }
2024-10-20T10:58:11.796Z In(05) vmx   Load CET state                           { 0 }
2024-10-20T10:58:11.796Z In(05) vmx   Load PKRS                                { 0 }
2024-10-20T10:58:11.796Z In(05) vmx True VM-Entry Controls (0x0006ffff000011fb)
2024-10-20T10:58:11.796Z In(05) vmx   Load debug controls                      {0,1}
2024-10-20T10:58:11.796Z In(05) vmx   IA-32e mode guest                        {0,1}
2024-10-20T10:58:11.796Z In(05) vmx   Entry to SMM                             {0,1}
2024-10-20T10:58:11.796Z In(05) vmx   Deactivate dual-monitor mode             {0,1}
2024-10-20T10:58:11.796Z In(05) vmx   Load IA32_PERF_GLOBAL_CTRL               {0,1}
2024-10-20T10:58:11.796Z In(05) vmx   Load IA32_PAT                            {0,1}
2024-10-20T10:58:11.796Z In(05) vmx   Load IA32_EFER                           {0,1}
2024-10-20T10:58:11.796Z In(05) vmx   Load IA32_BNDCFGS                        { 0 }
2024-10-20T10:58:11.796Z In(05) vmx   Conceal VMX from processor trace         {0,1}
2024-10-20T10:58:11.796Z In(05) vmx   Load IA32_RTIT MSR                       {0,1}
2024-10-20T10:58:11.796Z In(05) vmx   Load user-interrupt notification vector  { 0 }
2024-10-20T10:58:11.796Z In(05) vmx   Load CET state                           { 0 }
2024-10-20T10:58:11.796Z In(05) vmx   Load IA32_LBR_CTL MSR                    { 0 }
2024-10-20T10:58:11.796Z In(05) vmx   Load PKRS                                { 0 }
2024-10-20T10:58:11.796Z In(05) vmx VPID and EPT Capabilities (0x00000f0106734141)
2024-10-20T10:58:11.796Z In(05) vmx   R=0/W=0/X=1                               yes
2024-10-20T10:58:11.796Z In(05) vmx   Page-walk length 3                        yes
2024-10-20T10:58:11.796Z In(05) vmx   EPT memory type WB                        yes
2024-10-20T10:58:11.796Z In(05) vmx   2MB super-page                            yes
2024-10-20T10:58:11.796Z In(05) vmx   1GB super-page                            yes
2024-10-20T10:58:11.796Z In(05) vmx   INVEPT support                            yes
2024-10-20T10:58:11.796Z In(05) vmx   Access & Dirty Bits                       yes
2024-10-20T10:58:11.796Z In(05) vmx   Advanced VM exit information for EPT violations   yes
2024-10-20T10:58:11.796Z In(05) vmx   Supervisor shadow-stack control            no
2024-10-20T10:58:11.796Z In(05) vmx   Type 1 INVEPT                             yes
2024-10-20T10:58:11.796Z In(05) vmx   Type 2 INVEPT                             yes
2024-10-20T10:58:11.796Z In(05) vmx   INVVPID support                           yes
2024-10-20T10:58:11.796Z In(05) vmx   Type 0 INVVPID                            yes
2024-10-20T10:58:11.796Z In(05) vmx   Type 1 INVVPID                            yes
2024-10-20T10:58:11.796Z In(05) vmx   Type 2 INVVPID                            yes
2024-10-20T10:58:11.796Z In(05) vmx   Type 3 INVVPID                            yes
2024-10-20T10:58:11.796Z In(05) vmx Miscellaneous VMX Data (0x000000007004c1e7)
2024-10-20T10:58:11.796Z In(05) vmx   TSC to preemption timer ratio      7
2024-10-20T10:58:11.796Z In(05) vmx   VM-Exit saves EFER.LMA           yes
2024-10-20T10:58:11.796Z In(05) vmx   Activity State HLT               yes
2024-10-20T10:58:11.796Z In(05) vmx   Activity State shutdown          yes
2024-10-20T10:58:11.796Z In(05) vmx   Activity State wait-for-SIPI     yes
2024-10-20T10:58:11.796Z In(05) vmx   Processor trace in VMX           yes
2024-10-20T10:58:11.796Z In(05) vmx   RDMSR SMBASE MSR in SMM          yes
2024-10-20T10:58:11.796Z In(05) vmx   CR3 targets supported              4
2024-10-20T10:58:11.796Z In(05) vmx   Maximum MSR list size            512
2024-10-20T10:58:11.796Z In(05) vmx   VMXOFF holdoff of SMIs           yes
2024-10-20T10:58:11.796Z In(05) vmx   Allow all VMWRITEs               yes
2024-10-20T10:58:11.796Z In(05) vmx   Allow zero instruction length    yes
2024-10-20T10:58:11.796Z In(05) vmx   MSEG revision ID                   0
2024-10-20T10:58:11.796Z In(05) vmx VMX-Fixed Bits in CR0 (0x0000000080000021/0x00000000ffffffff)
2024-10-20T10:58:11.796Z In(05) vmx   Fixed to 0        0xffffffff00000000
2024-10-20T10:58:11.796Z In(05) vmx   Fixed to 1        0x0000000080000021
2024-10-20T10:58:11.796Z In(05) vmx   Variable          0x000000007fffffde
2024-10-20T10:58:11.796Z In(05) vmx VMX-Fixed Bits in CR4 (0x0000000000002000/0x0000000000772fff)
2024-10-20T10:58:11.796Z In(05) vmx   Fixed to 0        0xffffffffff88d000
2024-10-20T10:58:11.796Z In(05) vmx   Fixed to 1        0x0000000000002000
2024-10-20T10:58:11.796Z In(05) vmx   Variable          0x0000000000770fff
2024-10-20T10:58:11.796Z In(05) vmx VMCS Enumeration (0x000000000000002e)
2024-10-20T10:58:11.796Z In(05) vmx   Highest index                   0x17
2024-10-20T10:58:11.796Z In(05) vmx VM Functions (0x0000000000000001)
2024-10-20T10:58:11.796Z In(05) vmx   Function  0 (EPTP-switching) supported.
2024-10-20T10:58:11.796Z In(05) vmx Monitor_PowerOn: hostedVSMPMaxSkew is 1500 us (1785583 cycles)
2024-10-20T10:58:11.802Z In(05) vmx vmm-modules: [vmm.vmm, vmce-none.vmm, viommu-none.vmm, vprobe-none.vmm, hv-vt.vmm, gphys-ept.vmm, callstack-none.vmm, !e1000Shared=0x0, !tdxSharedVMData=0x880, !vmSamples=0x880, !theIOSpace=0x8c0, !ttGPPerVcpu=0x76c0, {UseUnwind}=0x0, numVCPUsAsAddr=0x2, {SharedAreaReservations}=0x7700, {rodataSize}=0x209c0, {textAddr}=0xfffffffffc000000, {textSize}=0x8e379, <MonSrcFile>]
2024-10-20T10:58:11.802Z In(05) vmx vmm-vcpus:   2
2024-10-20T10:58:11.826Z In(05) vmx KHZEstimate 1190389
2024-10-20T10:58:11.826Z In(05) vmx MHZEstimate 1190
2024-10-20T10:58:11.826Z In(05) vmx NumVCPUs 2
2024-10-20T10:58:11.834Z In(05) vmx UUID: Writing uuid.bios value: '56 4d 2b d7 e7 54 33 41-e0 75 e0 a2 de d9 08 d2'
2024-10-20T10:58:11.834Z No(00) vmx ConfigDB: Setting uuid.bios = "56 4d 2b d7 e7 54 33 41-e0 75 e0 a2 de d9 08 d2"
2024-10-20T10:58:11.834Z In(05) vmx UUID: Writing uuid.location value: '56 4d 2b d7 e7 54 33 41-e0 75 e0 a2 de d9 08 d2'
2024-10-20T10:58:11.834Z No(00) vmx ConfigDB: Setting uuid.location = "56 4d 2b d7 e7 54 33 41-e0 75 e0 a2 de d9 08 d2"
2024-10-20T10:58:11.843Z In(05) vmx AIOGNRC: numThreads=18 ide=0, scsi=1, passthru=1
2024-10-20T10:58:11.843Z In(05) vmx WORKER: Creating new group with maxThreads=18 (18)
2024-10-20T10:58:11.851Z In(05) vmx WORKER: Creating new group with maxThreads=1 (19)
2024-10-20T10:58:11.851Z In(05) vmx MainMem: CPT Host WZ=0 PF=2048 D=0
2024-10-20T10:58:11.851Z In(05) vmx MainMem: CPT PLS=1 PLR=1 BS=1 BlkP=32 Mult=4 W=50
2024-10-20T10:58:11.857Z In(05) vmx MainMem: Opened paging file, 'C:\Users\<USER>\Documents\Virtual Machines\windows10\564d2bd7-e754-3341-e075-e0a2ded908d2.vmem'.
2024-10-20T10:58:11.857Z In(05) vmx MStat: Creating Stat vm.uptime
2024-10-20T10:58:11.857Z In(05) vmx MStat: Creating Stat vm.suspendTime
2024-10-20T10:58:11.857Z In(05) vmx MStat: Creating Stat vm.powerOnTimeStamp
2024-10-20T10:58:11.859Z In(05) aioCompletion VTHREAD 15416 "aioCompletion"
2024-10-20T10:58:11.859Z In(05) vmx VMXAIOMGR: Using: simple=Compl
2024-10-20T10:58:11.860Z In(05) vmx WORKER: Creating new group with maxThreads=1 (20)
2024-10-20T10:58:11.865Z In(05) vmx WORKER: Creating new group with maxThreads=1 (21)
2024-10-20T10:58:11.865Z In(05) vmx WORKER: Creating new group with maxThreads=14 (35)
2024-10-20T10:58:11.867Z In(05) vmx FeatureCompat: No VM masks.
2024-10-20T10:58:11.867Z In(05) vmx TimeTracker host to guest rate conversion 342385692 @ 1190389000Hz -> 0 @ 1190389000Hz
2024-10-20T10:58:11.867Z In(05) vmx TimeTracker host to guest rate conversion ((x * 2147483648) >> 31) + -342385692
2024-10-20T10:58:11.867Z In(05) vmx TSC scaling enabled.
2024-10-20T10:58:11.867Z In(05) vmx TSC offsetting enabled.
2024-10-20T10:58:11.867Z In(05) vmx timeTracker.globalProgressMaxAllowanceMS: 2000
2024-10-20T10:58:11.867Z In(05) vmx timeTracker.globalProgressToAllowanceNS: 1000
2024-10-20T10:58:11.867Z In(05) vmx MKS PowerOn
2024-10-20T10:58:11.876Z In(05) mks VTHREAD 8296 "mks"
2024-10-20T10:58:11.876Z In(05) mks MKS thread is alive
2024-10-20T10:58:11.876Z In(05) svga VTHREAD 12844 "svga"
2024-10-20T10:58:11.876Z In(05) svga SVGA thread is alive
2024-10-20T10:58:11.878Z In(05) mks MKS: SSE2=1, SSSE3=1, SSE4_1=1
2024-10-20T10:58:11.878Z In(05) mouse VTHREAD 7728 "mouse"
2024-10-20T10:58:11.878Z In(05) mks MKS-HookKeyboard: RegQueryValueEx(LowLevelHooksTimeout) failed: The system cannot find the file specified (2)
2024-10-20T10:58:11.881Z In(05) kbh VTHREAD 15016 "kbh"
2024-10-20T10:58:11.881Z In(05) mks MKS Win32: Registering top level window (0x6066a) to receive session change notification.
2024-10-20T10:58:11.883Z In(05) mks Current Display Settings:
2024-10-20T10:58:11.883Z In(05) mks    Display: 0 size: 1920x1080  position: (0, 0) name: \\.\DISPLAY1  
2024-10-20T10:58:11.883Z In(05) mks MKS Win32: MIL: 0x4000
2024-10-20T10:58:11.883Z In(05) mks MKS-RenderMain: PowerOn allowed MKSBasicOps 
2024-10-20T10:58:11.883Z In(05) mks MKS-RenderMain: ISB enabled by config
2024-10-20T10:58:11.883Z In(05) mks MKS-RenderMain: Collecting RenderOps caps from MKSBasicOps
2024-10-20T10:58:11.883Z In(05) mks MKS-RenderMain: Starting MKSBasicOps
2024-10-20T10:58:11.883Z In(05) mks MKS-RenderMain: Started MKSBasicOps
2024-10-20T10:58:11.883Z In(05) mks MKS-RenderMain: Found Full Renderer: MKSBasicOps
2024-10-20T10:58:11.883Z In(05) mks MKS-RenderMain: maxTextureSize=32768
2024-10-20T10:58:11.883Z In(05) mks KHBKL: Unable to parse keystring at: ''
2024-10-20T10:58:11.883Z In(05) mks MKSRemoteMgr: Set default display name: windows10
2024-10-20T10:58:11.883Z In(05) mks MKSRemoteMgr: Loading VNC Configuration from VM config file
2024-10-20T10:58:11.883Z In(05) mks MKSRemoteMgr: Using default VNC keymap table "us"
2024-10-20T10:58:11.883Z In(05) vmx VLANCE: send cluster threshold is 80, size = 2 recalcInterval is 20000 us
2024-10-20T10:58:11.883Z In(05) vmx VMXNET: send cluster threshold is 80, size = 2 recalcInterval is 20000 ticks, dontClusterSize is 128
2024-10-20T10:58:11.883Z In(05) vmx Chipset version: 0x13
2024-10-20T10:58:11.883Z No(00) vmx ConfigDB: Setting vm.genid = "3670577129104394518"
2024-10-20T10:58:11.883Z No(00) vmx ConfigDB: Setting vm.genidX = "-5499800593787419783"
2024-10-20T10:58:11.908Z In(05) vmx SOUNDLIB: Creating the Wave sound backend.
2024-10-20T10:58:11.932Z No(00) vmx ConfigDB: Setting pciBridge0.pciSlotNumber = "17"
2024-10-20T10:58:11.932Z No(00) vmx ConfigDB: Setting pciBridge4.pciSlotNumber = "21"
2024-10-20T10:58:11.932Z No(00) vmx ConfigDB: Setting pciBridge5.pciSlotNumber = "22"
2024-10-20T10:58:11.932Z No(00) vmx ConfigDB: Setting pciBridge6.pciSlotNumber = "23"
2024-10-20T10:58:11.932Z No(00) vmx ConfigDB: Setting pciBridge7.pciSlotNumber = "24"
2024-10-20T10:58:11.932Z No(00) vmx ConfigDB: Setting scsi0.pciSlotNumber = "160"
2024-10-20T10:58:11.948Z In(05) vmx MigrateBusMemPrealloc: BusMem preallocation begins.
2024-10-20T10:58:11.948Z In(05) vmx MigrateBusMemPrealloc: BusMem preallocation completes.
2024-10-20T10:58:11.948Z No(00) vmx ConfigDB: Setting scsi0:0.redo = ""
2024-10-20T10:58:11.948Z In(05) vmx DISK: OPEN scsi0:0 'C:\Users\<USER>\Documents\Virtual Machines\windows10\windows10-disk1.vmdk' persistent R[]
2024-10-20T10:58:11.957Z In(05) vmx DiskGetGeometry: Reading of disk partition table
2024-10-20T10:58:11.957Z In(05) vmx DISK: Disk 'C:\Users\<USER>\Documents\Virtual Machines\windows10\windows10-disk1.vmdk' has UUID '60 00 c2 98 f7 93 a9 04-d6 7c 2a ca 2b 79 e6 02'
2024-10-20T10:58:11.957Z In(05) vmx DISK: OPEN 'C:\Users\<USER>\Documents\Virtual Machines\windows10\windows10-disk1.vmdk' Geo (5221/255/63) BIOS Geo (5221/255/63)
2024-10-20T10:58:11.965Z In(05) vmx DISKUTILWIN32: DiskUtilW32IsATASSDDevice: Failed to send ATA IDENTIFY command to the target device.
2024-10-20T10:58:11.965Z In(05) vmx DISKUTILWIN32: DiskUtilW32IsScsiSSDDevice: Reported rotation rate = 1
2024-10-20T10:58:11.965Z In(05) vmx DISK: DiskConfigureVirtualSSD:  Disk 'scsi0:0' identified as Virtual SSD device.
2024-10-20T10:58:11.976Z In(05) vmx DISK: Opening disks took 22 ms.
2024-10-20T10:58:11.976Z Wa(03) vmx USB: No USB controllers found.
2024-10-20T10:58:11.976Z In(05) vmx OBJLIB-FILEBE : FileBEOpen: can't open 'windows10-file1.nvram' : Could not find the file (393218).
2024-10-20T10:58:11.976Z In(05) vmx OBJLIB-FILEBE : FileBEOpen: can't open 'windows10-file1.nvram' : Could not find the file (393218).
2024-10-20T10:58:11.976Z Wa(03) vmx NVRAMMGR: No valid NVRAM file found, will create default NVRAM.
2024-10-20T10:58:11.981Z In(05) vmx SCSI DEVICE (ide0:0): Computed value of ide0:0.useBounceBuffers: default
2024-10-20T10:58:11.981Z In(05) vmx DISKUTIL: ide0:0 : capacity=0 logical sector size=2048
2024-10-20T10:58:11.981Z In(05) vmx DISKUTIL: ide0:0 : geometry=0/0/0
2024-10-20T10:58:11.981Z In(05) vmx SCSI: scsi0: intr coalescing: on period=50msec cifTh=4 iopsTh=2000 hlt=0
2024-10-20T10:58:11.981Z Wa(03) vmx Invalid sas WWID - Trying to generate a new one
2024-10-20T10:58:11.981Z No(00) vmx ConfigDB: Setting scsi0.sasWWID = "50 05 05 67 e7 54 33 40"
2024-10-20T10:58:11.989Z In(05) vmx SCSI0: UNTAGGED commands will be converted to ORDER tags.
2024-10-20T10:58:11.989Z In(05) vmx SCSI DEVICE (scsi0:0): Computed value of scsi0:0.useBounceBuffers: default
2024-10-20T10:58:11.989Z In(05) vmx DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T10:58:11.989Z In(05) vmx DISKUTIL: scsi0:0 : geometry=5221/255/63
2024-10-20T10:58:11.989Z In(05) vmx SVGA-GFB: Config settings: autodetect=1, numDisplays=1, maxWidth=2560, maxHeight=1600
2024-10-20T10:58:11.989Z In(05) vmx SVGA-GFB: Desired maximum display topology: wh(6688, 5016)
2024-10-20T10:58:11.989Z In(05) vmx SVGA-GFB: Autodetected target gfbSize = 268435456
2024-10-20T10:58:11.989Z In(05) vmx SVGA-GFB: Using Initial       gfbSize = 134217728
2024-10-20T10:58:11.989Z In(05) vmx SVGA-GFB: MaxPrimaryMem      register = 134217728
2024-10-20T10:58:11.989Z In(05) vmx SVGA-GFB: Max wh(6688, 5016), number of displays: 10
2024-10-20T10:58:11.989Z In(05) vmx SVGA-GFB: Allocated gfbSize=134217728
2024-10-20T10:58:11.989Z No(00) vmx ConfigDB: Setting svga.vramSize = "134217728"
2024-10-20T10:58:11.989Z No(00) vmx ConfigDB: Setting vmotion.checkpointFBSize = "134217728"
2024-10-20T10:58:11.989Z No(00) vmx ConfigDB: Setting vmotion.checkpointSVGAPrimarySize = "134217728"
2024-10-20T10:58:11.989Z In(05) vmx SVGA: SVGA DeviceLabel: svga2
2024-10-20T10:58:11.989Z In(05) vmx SVGA: mobMaxSize=134217728
2024-10-20T10:58:11.989Z In(05) vmx SVGA: graphicsMemoryKB=131072
2024-10-20T10:58:11.989Z In(05) vmx SVGA: FIFO capabilities 0x0000077f
2024-10-20T10:58:11.989Z In(05) vmx SVGAFeature renderer (before clamping) svga.supports3D bool 0
2024-10-20T10:58:11.989Z In(05) vmx SVGAFeature renderer (before clamping) svga.baseCapsLevel num 11
2024-10-20T10:58:11.989Z In(05) vmx SVGAFeature renderer (before clamping) svga.maxPointSize num 0
2024-10-20T10:58:11.989Z In(05) vmx SVGAFeature renderer (before clamping) svga.maxTextureSize num 32768
2024-10-20T10:58:11.989Z In(05) vmx SVGAFeature renderer (before clamping) svga.maxVolumeExtent num 0
2024-10-20T10:58:11.989Z In(05) vmx SVGAFeature renderer (before clamping) svga.maxTextureAnisotropy num 0
2024-10-20T10:58:11.989Z In(05) vmx SVGAFeature renderer (before clamping) svga.lineStipple bool 0
2024-10-20T10:58:11.989Z In(05) vmx SVGAFeature renderer (before clamping) svga.dxMaxConstantBuffers num 0
2024-10-20T10:58:11.989Z In(05) vmx SVGAFeature renderer (before clamping) svga.dxProvokingVertex bool 0
2024-10-20T10:58:11.989Z In(05) vmx SVGAFeature renderer (before clamping) svga.sm41 bool 0
2024-10-20T10:58:11.989Z In(05) vmx SVGAFeature renderer (before clamping) svga.multisample2x bool 0
2024-10-20T10:58:11.989Z In(05) vmx SVGAFeature renderer (before clamping) svga.multisample4x bool 0
2024-10-20T10:58:11.989Z In(05) vmx SVGAFeature renderer (before clamping) svga.msFullQuality bool 0
2024-10-20T10:58:11.989Z In(05) vmx SVGAFeature renderer (before clamping) svga.logicOps bool 0
2024-10-20T10:58:11.989Z In(05) vmx SVGAFeature renderer (before clamping) svga.bc67 num 0
2024-10-20T10:58:11.989Z In(05) vmx SVGAFeature renderer (before clamping) svga.sm5 bool 0
2024-10-20T10:58:11.989Z In(05) vmx SVGAFeature renderer (before clamping) svga.multisample8x bool 0
2024-10-20T10:58:11.989Z In(05) vmx SVGAFeature renderer (before clamping) svga.logicBlendOps bool 0
2024-10-20T10:58:11.989Z In(05) vmx SVGAFeature renderer (before clamping) svga.maxForcedSampleCount num 0
2024-10-20T10:58:11.989Z In(05) vmx SVGAFeature renderer (before clamping) svga.gl43 bool 0
2024-10-20T10:58:11.989Z In(05) vmx SVGAFeature renderer (after  clamping) svga.supports3D bool 0
2024-10-20T10:58:11.989Z In(05) vmx SVGAFeature renderer (after  clamping) svga.baseCapsLevel num 0
2024-10-20T10:58:11.989Z In(05) vmx SVGAFeature renderer (after  clamping) svga.maxPointSize num 0
2024-10-20T10:58:11.989Z In(05) vmx SVGAFeature renderer (after  clamping) svga.maxTextureSize num 0
2024-10-20T10:58:11.989Z In(05) vmx SVGAFeature renderer (after  clamping) svga.maxVolumeExtent num 0
2024-10-20T10:58:11.989Z In(05) vmx SVGAFeature renderer (after  clamping) svga.maxTextureAnisotropy num 0
2024-10-20T10:58:11.989Z In(05) vmx SVGAFeature renderer (after  clamping) svga.lineStipple bool 0
2024-10-20T10:58:11.989Z In(05) vmx SVGAFeature renderer (after  clamping) svga.dxMaxConstantBuffers num 0
2024-10-20T10:58:11.989Z In(05) vmx SVGAFeature renderer (after  clamping) svga.dxProvokingVertex bool 0
2024-10-20T10:58:11.989Z In(05) vmx SVGAFeature renderer (after  clamping) svga.sm41 bool 0
2024-10-20T10:58:11.989Z In(05) vmx SVGAFeature renderer (after  clamping) svga.multisample2x bool 0
2024-10-20T10:58:11.989Z In(05) vmx SVGAFeature renderer (after  clamping) svga.multisample4x bool 0
2024-10-20T10:58:11.989Z In(05) vmx SVGAFeature renderer (after  clamping) svga.msFullQuality bool 0
2024-10-20T10:58:11.989Z In(05) vmx SVGAFeature renderer (after  clamping) svga.logicOps bool 0
2024-10-20T10:58:11.989Z In(05) vmx SVGAFeature renderer (after  clamping) svga.bc67 num 0
2024-10-20T10:58:11.989Z In(05) vmx SVGAFeature renderer (after  clamping) svga.sm5 bool 0
2024-10-20T10:58:11.989Z In(05) vmx SVGAFeature renderer (after  clamping) svga.multisample8x bool 0
2024-10-20T10:58:11.989Z In(05) vmx SVGAFeature renderer (after  clamping) svga.logicBlendOps bool 0
2024-10-20T10:58:11.989Z In(05) vmx SVGAFeature renderer (after  clamping) svga.maxForcedSampleCount num 0
2024-10-20T10:58:11.989Z In(05) vmx SVGAFeature renderer (after  clamping) svga.gl43 bool 0
2024-10-20T10:58:11.989Z In(05) vmx SVGA3dClamp: Renderer Provides     BC67Level:     0 (    0,     0)
2024-10-20T10:58:11.989Z In(05) vmx SVGA3dClamp: Renderer Provides BaseCapsLevel:     0 (    0,     0)
2024-10-20T10:58:11.989Z In(05) vmx SVGA3dClamp: Renderer Provides    ClampLevel:     0 (    0,     0)
2024-10-20T10:58:11.989Z In(05) vmx SVGA3dCaps: host, at power on (3d disabled)
2024-10-20T10:58:11.989Z In(05) vmx   cap[ 19]: 0x00001000 (MAX_TEXTURE_WIDTH)
2024-10-20T10:58:11.989Z In(05) vmx   cap[ 20]: 0x00001000 (MAX_TEXTURE_HEIGHT)
2024-10-20T10:58:11.989Z In(05) vmx   cap[ 93]: 0x00000001 (TS_COLOR_KEY)
2024-10-20T10:58:11.989Z In(05) vmx SVGA3dClamp:     Host Provides     BC67Level:     0 (    0,     0)
2024-10-20T10:58:11.989Z In(05) vmx SVGA3dClamp:     Host Provides BaseCapsLevel:     0 (    0,     4)
2024-10-20T10:58:11.989Z In(05) vmx SVGA3dClamp:     Host Provides    ClampLevel:     0 (    0,     0)
2024-10-20T10:58:11.989Z In(05) vmx SVGA3dCaps: Disabling 3d support
2024-10-20T10:58:11.989Z In(05) vmx SVGA3dCaps: guest, compatibility level: 8
2024-10-20T10:58:11.989Z In(05) vmx   cap[ 19]: 0x00001000 (MAX_TEXTURE_WIDTH)
2024-10-20T10:58:11.989Z In(05) vmx   cap[ 20]: 0x00001000 (MAX_TEXTURE_HEIGHT)
2024-10-20T10:58:11.989Z In(05) vmx SVGA3dClamp:    Guest Requires     BC67Level:     0 (    0,     0)
2024-10-20T10:58:11.989Z In(05) vmx SVGA3dClamp:    Guest Requires BaseCapsLevel:     0 (    0,     0)
2024-10-20T10:58:11.989Z In(05) vmx SVGA3dClamp:    Guest Requires    ClampLevel:     0 (    0,     0)
2024-10-20T10:58:11.997Z No(00) vmx ConfigDB: Setting ethernet0.generatedAddress = "00:0c:29:d9:08:d2"
2024-10-20T10:58:11.997Z No(00) vmx ConfigDB: Setting ethernet0.generatedAddressOffset = "0"
2024-10-20T10:58:11.997Z In(05) vmx Ethernet0 MAC Address: 00:0c:29:d9:08:d2
2024-10-20T10:58:12.013Z No(00) vmx ConfigDB: Setting vmci0.id = "-556201774"
2024-10-20T10:58:12.029Z In(05) vmx WORKER: Creating new group with maxThreads=1 (36)
2024-10-20T10:58:12.029Z In(05) vmx DISKUTIL: scsi0:0 : max toolsVersion = 10309, type = 1
2024-10-20T10:58:12.029Z In(05) vmx TOOLS setting legacy tools version to '10309' type 1, manifest status is 9
2024-10-20T10:58:12.029Z In(05) vmx Tools: sending 'OS_PowerOn' (state = 3) state change request
2024-10-20T10:58:12.029Z In(05) vmx Tools: Delaying state change request to state 3.
2024-10-20T10:58:12.029Z In(05) worker-6188 ToolsISO: Refreshing imageName for 'windows9-64' (refreshCount=1, lastCount=1).
2024-10-20T10:58:12.029Z In(05) vmx TOOLS INSTALL initializing state to IDLE on power on.
2024-10-20T10:58:12.029Z In(05) worker-6188 ToolsISO: open of C:\Program Files (x86)\VMware\VMware Workstation\isoimages_manifest.txt.sig failed: Could not find the file
2024-10-20T10:58:12.029Z In(05) worker-6188 ToolsISO: Unable to read signature file 'C:\Program Files (x86)\VMware\VMware Workstation\isoimages_manifest.txt.sig', ignoring.
2024-10-20T10:58:12.029Z In(05) vmx TOOLS INSTALL updating Rpc handlers registration.
2024-10-20T10:58:12.029Z In(05) vmx TOOLS INSTALL register RPC: upgrader.setGuestFileRoot
2024-10-20T10:58:12.029Z In(05) vmx TOOLS INSTALL register RPC: toolinstall.is_image_inserted
2024-10-20T10:58:12.029Z In(05) vmx TOOLS INSTALL register RPC: toolinstall.installerActive
2024-10-20T10:58:12.029Z In(05) vmx TOOLS INSTALL register RPC: guest.upgrader_send_cmd_line_args
2024-10-20T10:58:12.029Z In(05) vmx P9FS_PowerOn: 9PFS server is not enabled.
2024-10-20T10:58:12.029Z In(05) vmx HgfsServerManagerVigorInit: Initialize: dev api
2024-10-20T10:58:12.029Z In(05) vmx MKSVMX: Copy/paste enabled = 1
2024-10-20T10:58:12.029Z In(05) vmx DEPLOYPKG: No pending deploy package name set
2024-10-20T10:58:12.029Z In(05) worker-6188 ToolsISO: Updated cached value for imageName to 'windows.iso'.
2024-10-20T10:58:12.029Z In(05) worker-6188 ToolsISO: Selected Tools ISO 'windows.iso' for 'windows9-64' guest.
2024-10-20T10:58:12.029Z In(05) worker-6188 GetHostManifests: Extracting C:\Users\<USER>\AppData\Local\Temp\vmware-97252\manifest.txt.6188.iso.shipped manifest file.
2024-10-20T10:58:12.029Z In(05) vmx DEPLOYPKG: ToolsDeployPkgPublishState: state=0, code=0, message=(null)
2024-10-20T10:58:12.029Z In(05) worker-6188 ToolsISO: Failed to open Tools ISO C:\Program Files (x86)\VMware\VMware Workstation\windows.iso (FileIOResult=6).
2024-10-20T10:58:12.029Z In(05) worker-6188 Error getting host tools manifest files.
2024-10-20T10:58:12.029Z In(05) worker-6188 ToolsISO: Refreshing imageName for 'windows9-64' (refreshCount=1, lastCount=1).
2024-10-20T10:58:12.029Z In(05) worker-6188 ToolsISO: open of C:\Program Files (x86)\VMware\VMware Workstation\isoimages_manifest.txt.sig failed: Could not find the file
2024-10-20T10:58:12.029Z In(05) worker-6188 ToolsISO: Unable to read signature file 'C:\Program Files (x86)\VMware\VMware Workstation\isoimages_manifest.txt.sig', ignoring.
2024-10-20T10:58:12.029Z In(05) worker-6188 ToolsISO: Updated cached value for imageName to 'windows.iso'.
2024-10-20T10:58:12.029Z In(05) worker-6188 ToolsISO: Selected Tools ISO 'windows.iso' for 'windows9-64' guest.
2024-10-20T10:58:12.029Z In(05) worker-6188 GetHostManifests: Extracting C:\Users\<USER>\AppData\Local\Temp\vmware-97252\manifest.txt.6188.iso.shipped manifest file.
2024-10-20T10:58:12.029Z In(05) worker-6188 ToolsISO: Failed to open Tools ISO C:\Program Files (x86)\VMware\VMware Workstation\windows.iso (FileIOResult=6).
2024-10-20T10:58:12.029Z In(05) worker-6188 Error getting host tools manifest files.
2024-10-20T10:58:12.029Z In(05) worker-6188 Using ToolsMinVersion = 8384
2024-10-20T10:58:12.029Z In(05) worker-6188 Using ToolsMaxVersion = 12416
2024-10-20T10:58:12.029Z In(05) worker-6188 ToolsVersionGetStatusWorkerThread: Tools status 2 derived from environment
2024-10-20T10:58:12.037Z In(05) vmx MonPmc: ctrBase 0x4c1 selBase 0x186/1 PGC 1/1 SMM 1 drain 0 AMD 0
2024-10-20T10:58:12.037Z In(05)+ vmx MonPmc:   gen counters num: 8 width 48 write width 48
2024-10-20T10:58:12.037Z In(05)+ vmx MonPmc:   fix counters num: 4 width 48; version 5
2024-10-20T10:58:12.037Z In(05)+ vmx MonPmc:   unavailable counters: 0xf000000ff
2024-10-20T10:58:12.074Z No(00) vmx ConfigDB: Setting monitor.phys_bits_used = "42"
2024-10-20T10:58:12.090Z In(05) vmx Full guest CPUID with differences from hostCPUID highlighted.
2024-10-20T10:58:12.090Z In(05) vmx guest vs. host CPUID guest vendor: GenuineIntel
2024-10-20T10:58:12.090Z In(05) vmx guest vs. host CPUID guest family: 0x6 model: 0x7e stepping: 0x5
2024-10-20T10:58:12.090Z In(05) vmx guest vs. host CPUID guest codename: Ice Lake-U/Y
2024-10-20T10:58:12.090Z In(05) vmx guest vs. host CPUID guest name: Intel(R) Core(TM) i3-1005G1 CPU @ 1.20GHz
2024-10-20T10:58:12.090Z In(05) vmx guest vs. host CPUID       level eaxIn, ecxIn:        eax        ebx        ecx        edx
2024-10-20T10:58:12.090Z In(05) vmx guest vs. host CPUID guest level 00000000,  0: 0x0000001b 0x756e6547 0x6c65746e 0x49656e69
2024-10-20T10:58:12.090Z In(05) vmx guest vs. host CPUID guest level 00000001,  0: 0x000706e5 0x00010800 0xf7fa3203 0x0f8bfbff
2024-10-20T10:58:12.090Z In(05) vmx guest vs. host CPUID *host level 00000001,  0: 0x000706e5 0x00100800 0x7ffafbbf 0xbfebfbff
2024-10-20T10:58:12.090Z In(05) vmx guest vs. host CPUID guest level 00000002,  0: 0x00feff01 0x000000f0 0x00000000 0x00000000
2024-10-20T10:58:12.090Z In(05) vmx guest vs. host CPUID guest level 00000004,  0: 0x00000121 0x02c0003f 0x0000003f 0x00000000
2024-10-20T10:58:12.090Z In(05) vmx guest vs. host CPUID *host level 00000004,  0: 0x1c004121 0x02c0003f 0x0000003f 0x00000000
2024-10-20T10:58:12.090Z In(05) vmx guest vs. host CPUID guest level 00000004,  1: 0x00000122 0x01c0003f 0x0000003f 0x00000000
2024-10-20T10:58:12.090Z In(05) vmx guest vs. host CPUID *host level 00000004,  1: 0x1c004122 0x01c0003f 0x0000003f 0x00000000
2024-10-20T10:58:12.090Z In(05) vmx guest vs. host CPUID guest level 00000004,  2: 0x00000143 0x01c0003f 0x000003ff 0x00000000
2024-10-20T10:58:12.090Z In(05) vmx guest vs. host CPUID *host level 00000004,  2: 0x1c004143 0x01c0003f 0x000003ff 0x00000000
2024-10-20T10:58:12.090Z In(05) vmx guest vs. host CPUID guest level 00000004,  3: 0x00000163 0x03c0003f 0x00000fff 0x00000006
2024-10-20T10:58:12.090Z In(05) vmx guest vs. host CPUID *host level 00000004,  3: 0x1c03c163 0x03c0003f 0x00000fff 0x00000006
2024-10-20T10:58:12.090Z In(05) vmx guest vs. host CPUID guest level 00000006,  0: 0x00000004 0x00000000 0x00000000 0x00000000
2024-10-20T10:58:12.090Z In(05) vmx guest vs. host CPUID *host level 00000006,  0: 0x0017aff7 0x00000002 0x00000009 0x00000000
2024-10-20T10:58:12.090Z In(05) vmx guest vs. host CPUID guest level 00000007,  0: 0x00000000 0x001c27eb 0x00000000 0xbc000400
2024-10-20T10:58:12.090Z In(05) vmx guest vs. host CPUID *host level 00000007,  0: 0x00000000 0xf2bf27ef 0x40405f4e 0xbc000410
2024-10-20T10:58:12.090Z In(05) vmx guest vs. host CPUID guest level 0000000a,  0: 0x08300801 0x000000ff 0x0000000f 0x00008000
2024-10-20T10:58:12.090Z In(05) vmx guest vs. host CPUID *host level 0000000a,  0: 0x08300805 0x00000000 0x0000000f 0x00008604
2024-10-20T10:58:12.090Z In(05) vmx guest vs. host CPUID guest level 0000000b,  0: 0x00000000 0x00000001 0x00000100 0x00000000
2024-10-20T10:58:12.090Z In(05) vmx guest vs. host CPUID *host level 0000000b,  0: 0x00000001 0x00000002 0x00000100 0x00000000
2024-10-20T10:58:12.090Z In(05) vmx guest vs. host CPUID guest level 0000000b,  1: 0x00000000 0x00000001 0x00000201 0x00000000
2024-10-20T10:58:12.090Z In(05) vmx guest vs. host CPUID *host level 0000000b,  1: 0x00000004 0x00000004 0x00000201 0x00000000
2024-10-20T10:58:12.090Z In(05) vmx guest vs. host CPUID guest level 0000000d,  0: 0x00000007 0x00000340 0x00000340 0x00000000
2024-10-20T10:58:12.090Z In(05) vmx guest vs. host CPUID *host level 0000000d,  0: 0x000002e7 0x00000a80 0x00000a88 0x00000000
2024-10-20T10:58:12.090Z In(05) vmx guest vs. host CPUID guest level 0000000d,  1: 0x00000001 0x00000000 0x00000000 0x00000000
2024-10-20T10:58:12.090Z In(05) vmx guest vs. host CPUID *host level 0000000d,  1: 0x0000000f 0x00000a00 0x00002100 0x00000000
2024-10-20T10:58:12.090Z In(05) vmx guest vs. host CPUID guest level 0000000d,  2: 0x00000100 0x00000240 0x00000000 0x00000000
2024-10-20T10:58:12.090Z In(05) vmx guest vs. host CPUID guest level 40000000,  0: 0x40000010 0x61774d56 0x4d566572 0x65726177
2024-10-20T10:58:12.090Z In(05) vmx guest vs. host CPUID guest level 40000001,  0: 0x31237648 0x00000000 0x00000000 0x00000000
2024-10-20T10:58:12.090Z In(05) vmx guest vs. host CPUID guest level 40000003,  0: 0x00000662 0x00000000 0x00000000 0x00000528
2024-10-20T10:58:12.090Z In(05) vmx guest vs. host CPUID guest level 40000004,  0: 0x00000020 0x00000000 0x00000000 0x00000000
2024-10-20T10:58:12.090Z In(05) vmx guest vs. host CPUID guest level 40000005,  0: 0xffffffff 0x00000000 0x00000000 0x00000000
2024-10-20T10:58:12.090Z In(05) vmx guest vs. host CPUID guest level 40000010,  0: 0x001229f5 0x000101d0 0x00000000 0x00000000
2024-10-20T10:58:12.090Z In(05) vmx guest vs. host CPUID guest level 80000000,  0: 0x80000008 0x00000000 0x00000000 0x00000000
2024-10-20T10:58:12.090Z In(05) vmx guest vs. host CPUID guest level 80000001,  0: 0x00000000 0x00000000 0x00000121 0x2c100800
2024-10-20T10:58:12.090Z In(05) vmx guest vs. host CPUID guest level 80000002,  0: 0x65746e49 0x2952286c 0x726f4320 0x4d542865
2024-10-20T10:58:12.090Z In(05) vmx guest vs. host CPUID guest level 80000003,  0: 0x33692029 0x3030312d 0x20314735 0x20555043
2024-10-20T10:58:12.090Z In(05) vmx guest vs. host CPUID guest level 80000004,  0: 0x2e312040 0x48473032 0x0000007a 0x00000000
2024-10-20T10:58:12.090Z In(05) vmx guest vs. host CPUID guest level 80000006,  0: 0x00000000 0x00000000 0x01006040 0x00000000
2024-10-20T10:58:12.090Z In(05) vmx guest vs. host CPUID guest level 80000007,  0: 0x00000000 0x00000000 0x00000000 0x00000100
2024-10-20T10:58:12.090Z In(05) vmx guest vs. host CPUID guest level 80000008,  0: 0x0000302a 0x00000000 0x00000000 0x00000000
2024-10-20T10:58:12.090Z In(05) vmx guest vs. host CPUID *host level 80000008,  0: 0x00003027 0x00000000 0x00000000 0x00000000
2024-10-20T10:58:12.090Z In(05) vmx Minimum ucode level: 0x000000a6
2024-10-20T10:58:12.090Z In(05) vmx VPMC: events will use hybrid freeze.
2024-10-20T10:58:12.090Z In(05) vmx VPMC: gen counters: num 8 mask 0xffffffffffff
2024-10-20T10:58:12.090Z In(05) vmx VPMC: fix counters: num 0 mask 0; version 1
2024-10-20T10:58:12.090Z In(05) vmx VPMC: hardware counters: 0
2024-10-20T10:58:12.090Z In(05) vmx VPMC: perf capabilities: 0x2000
2024-10-20T10:58:12.090Z In(05) vmx Guest MSR IA32_ARCH_CAPABILITIES 0x10a = 0x2b
2024-10-20T10:58:12.091Z In(05) vmx SVGA-PCI: BAR gfbSize=134217728, fifoSize=8388608
2024-10-20T10:58:12.091Z In(05) vmx SVGA: SVGA_REG_MEMORY_SIZE=134217728
2024-10-20T10:58:12.091Z In(05) vmx SVGA: SVGA_REG_VRAM_SIZE=134217728
2024-10-20T10:58:12.091Z In(05) vmx SVGA: Final Device caps : 0x1dff83e2
2024-10-20T10:58:12.091Z In(05) vmx SVGA: Final Device caps2: 0x00000000
2024-10-20T10:58:12.091Z In(05) vmx BusMemSampleSetUpStats: touched: initPct 75 pages 393216 : dirtied: initPct 75 pages 393216
2024-10-20T10:58:12.091Z In(05) vmx MemSched: caller 0 numvm 1 locked pages: num 1610 max 1579008
2024-10-20T10:58:12.091Z In(05) vmx MemSched: locked Page Limit: host 1683920 config 1587200
2024-10-20T10:58:12.091Z In(05) vmx MemSched: minmempct 50  timestamp 51845
2024-10-20T10:58:12.091Z In(05) vmx MemSched: VM 0 min 304757 max 566901 shares 524288 paged 408241 nonpaged 35953 anonymous 6660 locked 1610 touchedPct 75 dirtiedPct 75 timestamp 51845 vmResponsive is 1
2024-10-20T10:58:12.091Z In(05) vmx MemSched: locked 1610 target 566901 balloon 0 0 0 swapped 0 0 allocd 0 512 state 0 100
2024-10-20T10:58:12.091Z In(05) vmx MemSched: states: 0 1 : 1 0 : 2 0 : 3 0
2024-10-20T10:58:12.091Z In(05) vmx MemSched: Balloon enabled 1 guestType 0 maxSize 0
2024-10-20T10:58:12.091Z In(05) vmx PStrIntern expansion: nBkts=256
2024-10-20T10:58:12.091Z In(05) vmx FeatureCompat: Capabilities:
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.sse3 = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.pclmulqdq = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.mwait = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.vmx = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.ssse3 = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.fma = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.cmpxchg16b = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.pcid = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.sse41 = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.sse42 = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.movbe = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.popcnt = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.aes = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.xsave = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.avx = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.f16c = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.rdrand = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.ds = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.ss = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.fsgsbase = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.bmi1 = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.avx2 = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.smep = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.bmi2 = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.enfstrg = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.invpcid = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.avx512f = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.avx512dq = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.rdseed = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.adx = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.smap = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.avx512ifma = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.clflushopt = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.avx512cd = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.sha = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.avx512bw = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.avx512vl = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.avx512vbmi = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.umip = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.pku = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.avx512vbmi2 = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.gfni = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.vaes = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.vpclmulqdq = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.avx512vnni = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.avx512bitalg = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.avx512vpopcntdq = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.rdpid = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.fast_short_repmov = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.mdclear = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.stibp = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.fcmd = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.ssbd = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.xcr0_master_sse = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.xcr0_master_ymm_h = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.xcr0_master_bndregs = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.xcr0_master_bndcsr = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.xcr0_master_opmask = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.xcr0_master_zmm_h = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.xcr0_master_hi16_zmm = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.xcr0_master_pkru = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.xsaveopt = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.xsavec = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.xgetbv_ecx1 = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.xsaves = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.lahf64 = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.abm = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.3dnprefetch = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.nx = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.pdpe1gb = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.rdtscp = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.lm = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.intel = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.ibrs = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: cpuid.ibpb = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: hv.capable = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: vt.realmode = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: vt.mbx = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: misc.cpuidfaulting = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: vt.advexitinfo = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: vt.eptad = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: vt.ple = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: vt.zeroinstlen = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: misc.rdcl_no = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: misc.ibrs_all = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: misc.rsba_no = 1
2024-10-20T10:58:12.091Z In(05) vmx Capability Found: misc.mds_no = 1
2024-10-20T10:58:12.091Z In(05) vmx FeatureCompat: Requirements:
2024-10-20T10:58:12.091Z In(05) vmx VM Features Required: cpuid.sse3 - Bool:Min:1
2024-10-20T10:58:12.091Z In(05) vmx VM Features Required: cpuid.pclmulqdq - Bool:Min:1
2024-10-20T10:58:12.091Z In(05) vmx VM Features Required: cpuid.ssse3 - Bool:Min:1
2024-10-20T10:58:12.091Z In(05) vmx VM Features Required: cpuid.fma - Bool:Min:1
2024-10-20T10:58:12.091Z In(05) vmx VM Features Required: cpuid.cmpxchg16b - Bool:Min:1
2024-10-20T10:58:12.091Z In(05) vmx VM Features Required: cpuid.pcid - Bool:Min:1
2024-10-20T10:58:12.091Z In(05) vmx VM Features Required: cpuid.sse41 - Bool:Min:1
2024-10-20T10:58:12.091Z In(05) vmx VM Features Required: cpuid.sse42 - Bool:Min:1
2024-10-20T10:58:12.091Z In(05) vmx VM Features Required: cpuid.movbe - Bool:Min:1
2024-10-20T10:58:12.091Z In(05) vmx VM Features Required: cpuid.popcnt - Bool:Min:1
2024-10-20T10:58:12.091Z In(05) vmx VM Features Required: cpuid.aes - Bool:Min:1
2024-10-20T10:58:12.091Z In(05) vmx VM Features Required: cpuid.xsave - Bool:Min:1
2024-10-20T10:58:12.091Z In(05) vmx VM Features Required: cpuid.avx - Bool:Min:1
2024-10-20T10:58:12.091Z In(05) vmx VM Features Required: cpuid.f16c - Bool:Min:1
2024-10-20T10:58:12.091Z In(05) vmx VM Features Required: cpuid.rdrand - Bool:Min:1
2024-10-20T10:58:12.091Z In(05) vmx VM Features Required: cpuid.ss - Bool:Min:1
2024-10-20T10:58:12.091Z In(05) vmx VM Features Required: cpuid.fsgsbase - Bool:Min:1
2024-10-20T10:58:12.091Z In(05) vmx VM Features Required: cpuid.bmi1 - Bool:Min:1
2024-10-20T10:58:12.091Z In(05) vmx VM Features Required: cpuid.avx2 - Bool:Min:1
2024-10-20T10:58:12.091Z In(05) vmx VM Features Required: cpuid.smep - Bool:Min:1
2024-10-20T10:58:12.091Z In(05) vmx VM Features Required: cpuid.bmi2 - Bool:Min:1
2024-10-20T10:58:12.091Z In(05) vmx VM Features Required: cpuid.enfstrg - Bool:Min:1
2024-10-20T10:58:12.091Z In(05) vmx VM Features Required: cpuid.invpcid - Bool:Min:1
2024-10-20T10:58:12.091Z In(05) vmx VM Features Required: cpuid.rdseed - Bool:Min:1
2024-10-20T10:58:12.091Z In(05) vmx VM Features Required: cpuid.adx - Bool:Min:1
2024-10-20T10:58:12.091Z In(05) vmx VM Features Required: cpuid.smap - Bool:Min:1
2024-10-20T10:58:12.091Z In(05) vmx VM Features Required: cpuid.mdclear - Bool:Min:1
2024-10-20T10:58:12.091Z In(05) vmx VM Features Required: cpuid.stibp - Bool:Min:1
2024-10-20T10:58:12.091Z In(05) vmx VM Features Required: cpuid.fcmd - Bool:Min:1
2024-10-20T10:58:12.091Z In(05) vmx VM Features Required: cpuid.ssbd - Bool:Min:1
2024-10-20T10:58:12.091Z In(05) vmx VM Features Required: cpuid.xcr0_master_sse - Bool:Min:1
2024-10-20T10:58:12.091Z In(05) vmx VM Features Required: cpuid.xcr0_master_ymm_h - Bool:Min:1
2024-10-20T10:58:12.091Z In(05) vmx VM Features Required: cpuid.xsaveopt - Bool:Min:1
2024-10-20T10:58:12.091Z In(05) vmx VM Features Required: cpuid.lahf64 - Bool:Min:1
2024-10-20T10:58:12.091Z In(05) vmx VM Features Required: cpuid.abm - Bool:Min:1
2024-10-20T10:58:12.091Z In(05) vmx VM Features Required: cpuid.3dnprefetch - Bool:Min:1
2024-10-20T10:58:12.091Z In(05) vmx VM Features Required: cpuid.nx - Bool:Min:1
2024-10-20T10:58:12.091Z In(05) vmx VM Features Required: cpuid.pdpe1gb - Bool:Min:1
2024-10-20T10:58:12.091Z In(05) vmx VM Features Required: cpuid.rdtscp - Bool:Min:1
2024-10-20T10:58:12.091Z In(05) vmx VM Features Required: cpuid.lm - Bool:Min:1
2024-10-20T10:58:12.091Z In(05) vmx VM Features Required: cpuid.intel - Bool:Min:1
2024-10-20T10:58:12.091Z In(05) vmx VM Features Required: cpuid.ibrs - Bool:Min:1
2024-10-20T10:58:12.091Z In(05) vmx VM Features Required: cpuid.ibpb - Bool:Min:1
2024-10-20T10:58:12.091Z In(05) vmx VM Features Required: misc.rdcl_no - Bool:Min:1
2024-10-20T10:58:12.091Z In(05) vmx VM Features Required: misc.ibrs_all - Bool:Min:1
2024-10-20T10:58:12.091Z In(05) vmx VM Features Required: misc.rsba_no - Bool:Min:1
2024-10-20T10:58:12.091Z In(05) vmx VM Features Required: misc.mds_no - Bool:Min:1
2024-10-20T10:58:12.098Z In(05) vmx TOOLS received request in VMX to set option 'enableDnD' -> '1'
2024-10-20T10:58:12.098Z In(05) vmx 
2024-10-20T10:58:12.098Z In(05)+ vmx OvhdMem: Static (Power On) Overheads
2024-10-20T10:58:12.098Z In(05) vmx                                                       reserved      |          used
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem excluded                                  cur    max    avg |    cur    max    avg
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdUser_MainMem                    :  524288 524288      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdUser_VmxText                    :    7680   7680      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdUser_VmxTextLibs                :   17408  17408      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem Total excluded                      :  549376 549376      - |      -      -      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem Actual maximum                      :         549376        |             -
2024-10-20T10:58:12.098Z In(05)+ vmx 
2024-10-20T10:58:12.098Z In(05) vmx                                                       reserved      |          used
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem paged                                     cur    max    avg |    cur    max    avg
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdUser_STATS_vmm                  :       4      4      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdUser_STATS_device               :       2      2      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdUser_SvgaMobFallback            :   98304  98304      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdUser_DiskLibMemUsed             :    3075   3075      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdUser_SvgaSurfaceTable           :       6      6      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdUser_SvgaBESurfaceTable         :       4      4      - |      4      4      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdUser_SvgaSDirtyCache            :      96     96      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdUser_SvgaCursor                 :      10     10      - |     10     10      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdUser_SvgaPPNList                :     130    130      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdUser_VmxGlobals                 :   10240  10240      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdUser_VmxGlobalsLibs             :    3584   3584      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdUser_VmxHeap                    :    8704   8704      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdUser_VmxMks                     :      33     33      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdUser_VmxMksRenderOps            :     678    678      - |    492    492      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdUser_VmxMks3d                   :   98304  98304      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdUser_VmxMksScreenTemp           :   37122  37122      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdUser_VmxMksVnc                  :   38236  38236      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdUser_VmxMksScreen               :   65539  65539      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdUser_VmxMksSVGAVO               :    4096   4096      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdUser_VmxMksSwbCursor            :    4096   4096      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdUser_VmxPhysMemErrPages         :      10     10      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdUser_VmxSLEntryBuf              :     128    128      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdUser_VmxThreads                 :   35840  35840      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem Total paged                         :  408241 408241      - |    506    506      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem Actual maximum                      :         408241        |        408241
2024-10-20T10:58:12.098Z In(05)+ vmx 
2024-10-20T10:58:12.098Z In(05) vmx                                                       reserved      |          used
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem nonpaged                                  cur    max    avg |    cur    max    avg
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdUser_SharedArea                 :     149    149      - |    134    134      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdUser_BusMemTraceBitmap          :      22     22      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdUser_PFrame                     :    1268   2304      - |   1268   1268      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdUser_VIDE_KSEG                  :      16     16      - |     16     16      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdUser_VGA                        :      64     64      - |     64     64      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdUser_BalloonMPN                 :       1      1      - |      1      1      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdUser_P2MUpdateBuffer            :       3      3      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdUser_ServicesMPN                :       3      3      - |      2      2      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdUser_LocalApic                  :       2      2      - |      2      2      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdUser_VBIOS                      :       8      8      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdUser_LSIBIOS                    :       4      4      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdUser_LSIRings                   :       4      4      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdUser_SAS1068BIOS                :       4      4      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdUser_SBIOS                      :      16     16      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdUser_FlashRam                   :     128    128      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdUser_SVGAFB                     :   32768  32768      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdUser_SVGAMEM                    :      64    512      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdUser_HDAudioReg                 :       3      3      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdUser_EHCIRegister               :       1      1      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdUser_XhciRegister               :       1      1      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdUser_HyperV                     :       2      2      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdUser_ExtCfg                     :       4      4      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdUser_vhvCachedVMCS              :       2      2      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdUser_vhvNestedAPIC              :       2      2      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdUser_LBR                        :       2      2      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdUser_MonWired                   :      53     53      - |     53     53      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdUser_MonNuma                    :     252    252      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdUser_NVDC                       :       1      1      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdUser_PCIeMMIOArea               :      70     70      - |     35     35      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem Total nonpaged                      :   34917  36401      - |   1575   1575      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem Actual maximum                      :          34917        |         36401
2024-10-20T10:58:12.098Z In(05)+ vmx 
2024-10-20T10:58:12.098Z In(05) vmx                                                       reserved      |          used
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem anonymous                                 cur    max    avg |    cur    max    avg
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdMon_Alloc                       :     196    196      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdMon_BusMemFrame                 :     609    666      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdMon_BusMem2MInfo                :       8      8      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdMon_BusMem1GInfo                :       1      1      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdMon_BusMemZapListMPN            :       1      1      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdMon_BusMemPreval                :       8      8      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdMon_MonAS                       :       2      2      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdMon_GuestMem                    :      80     80      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdMon_TC                          :    1026   1026      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdMon_BusMemMonAS                 :       5      5      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdMon_PlatformMonAS               :       7      7      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdMon_HVNuma                      :       4      4      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdMon_HV                          :       2      2      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdMon_HVMSRBitmap                 :       1      1      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdMon_VHVGuestMSRBitmap           :       2      2      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdMon_VHV                         :       6      6      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdMon_Numa                        :      30     30      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdMon_NumaTextRodata              :     198    198      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdMon_NumaDataBss                 :      54     54      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdMon_BaseWired                   :      58     58      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdMon_Bootstrap                   :    2303   2303      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdMon_GPhysTraced                 :     272    272      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdMon_GPhysHWMMU                  :    1352   1352      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdMon_GPhysNoTrace                :     266    266      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdMon_PhysMemGart                 :     104    104      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdMon_PhysMemErr                  :       7      7      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem OvhdMon_VProbe                      :       1      1      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem Total anonymous                     :    6603   6660      - |      0      0      -
2024-10-20T10:58:12.098Z In(05) vmx OvhdMem Actual maximum                      :           6603        |          6660
2024-10-20T10:58:12.098Z In(05)+ vmx 
2024-10-20T10:58:12.098Z In(05) vmx VMMEM: Precise Reservation: 1756MB (MainMem=2048MB)
2024-10-20T10:58:12.098Z In(05) vmx VMXSTATS: Registering 48 stats: vmx.overheadMemSize
2024-10-20T10:58:12.098Z In(05) vmx Vix: [mainDispatch.c:1058]: VMAutomation_PowerOn. Powering on.
2024-10-20T10:58:12.098Z In(05) vmx VMX_PowerOn: ModuleTable_PowerOn = 1
2024-10-20T10:58:12.098Z No(00) vmx ConfigDB: Setting cleanShutdown = "FALSE"
2024-10-20T10:58:12.098Z No(00) vmx ConfigDB: Setting softPowerOff = "FALSE"
2024-10-20T10:58:12.112Z In(05) vcpu-0 VTHREAD 16080 "vcpu-0"
2024-10-20T10:58:12.112Z In(05) vmx ToolsISO: Refreshing imageName for 'windows9-64' (refreshCount=1, lastCount=1).
2024-10-20T10:58:12.112Z In(05) vmx ToolsISO: open of C:\Program Files (x86)\VMware\VMware Workstation\isoimages_manifest.txt.sig failed: Could not find the file
2024-10-20T10:58:12.112Z In(05) vmx ToolsISO: Unable to read signature file 'C:\Program Files (x86)\VMware\VMware Workstation\isoimages_manifest.txt.sig', ignoring.
2024-10-20T10:58:12.112Z In(05) vmx ToolsISO: Updated cached value for imageName to 'windows.iso'.
2024-10-20T10:58:12.112Z In(05) vmx ToolsISO: Selected Tools ISO 'windows.iso' for 'windows9-64' guest.
2024-10-20T10:58:12.112Z In(05) vmx Win32U_GetFileAttributes: GetFileAttributesExW("C:\Program Files (x86)\VMware\VMware Workstation\windows.iso", ...) failed, error: 2
2024-10-20T10:58:12.112Z In(05) vmx TOOLS updated cached value for isoImageExists to 0.
2024-10-20T10:58:12.112Z In(05) vmx VMXVmdb_SetToolsVersionStatus: status value set to 'ok', 'supportedOld', install impossible
2024-10-20T10:58:12.120Z In(05) vcpu-0 MonTimer APIC:0/0 vec: 0
2024-10-20T10:58:12.120Z In(05) vcpu-0 APIC: version = 0x15, max LVT = 6, LDR = 0x2000000, DFR = 0xffffffff
2024-10-20T10:58:12.120Z In(05) vcpu-0 Active HV capabilities
2024-10-20T10:58:12.120Z In(05) vcpu-0    Virtual interrupt delivery
2024-10-20T10:58:12.120Z In(05) vcpu-0    XAPIC MMIO virtualization
2024-10-20T10:58:12.120Z In(05) vcpu-0    Full decode
2024-10-20T10:58:12.120Z In(05) vcpu-0    Nested paging A/D bits
2024-10-20T10:58:12.120Z In(05) vcpu-0    Real-address mode
2024-10-20T10:58:12.120Z In(05) vcpu-0    Skip debug state
2024-10-20T10:58:12.120Z In(05) vcpu-0    X2APIC virtualization
2024-10-20T10:58:12.120Z In(05) vcpu-0    TPR MMIO virtualization
2024-10-20T10:58:12.120Z In(05) vcpu-0    Page-modification logging
2024-10-20T10:58:12.120Z In(05) vcpu-0    ENCLS exiting
2024-10-20T10:58:12.120Z In(05) vcpu-0    PAUSE-loop exiting
2024-10-20T10:58:12.120Z In(05) vcpu-0    TSC scaling
2024-10-20T10:58:12.120Z In(05) vcpu-0    Advanced exit information for EPT violations
2024-10-20T10:58:12.120Z In(05) vcpu-0    Mode-based execute control for nested paging
2024-10-20T10:58:12.120Z In(05) vcpu-0    EPT-violation virtualization exception
2024-10-20T10:58:12.120Z In(05) vcpu-0    Event injection with instruction length zero
2024-10-20T10:58:12.120Z In(05) vcpu-0 TSC scaling ratio: 0001_000000000000 (mult=2147483648, shift=31)
2024-10-20T10:58:12.120Z In(05) vcpu-0 CPU reset: hard (mode Emulation)
2024-10-20T10:58:12.120Z In(05) vcpu-1 VTHREAD 15120 "vcpu-1"
2024-10-20T10:58:12.120Z In(05) vcpu-1 MonTimer APIC:0/0 vec: 0
2024-10-20T10:58:12.120Z In(05) vcpu-1 APIC: version = 0x15, max LVT = 6, LDR = 0x1000000, DFR = 0xffffffff
2024-10-20T10:58:12.120Z In(05) vcpu-1 TSC scaling ratio: 0001_000000000000 (mult=2147483648, shift=31)
2024-10-20T10:58:12.120Z In(05) vcpu-1 CPU reset: hard (mode Emulation)
2024-10-20T10:58:12.120Z In(05) vcpu-0 GuestRpc: Successfully created RPCI listening socket.
2024-10-20T10:58:12.120Z In(05) vcpu-0 GuestRpc: Using vsocket for TCLO messaging is disabled.
2024-10-20T10:58:12.127Z In(05) vcpu-0 memoryHotplug: Node 0: Present: 2047 MB (100 %) Size:2047 MB (100 %)
2024-10-20T10:58:12.127Z In(05) vcpu-0 PIIX4: PM Resuming from suspend type 0x0, chipset.onlineStandby 0
2024-10-20T10:58:12.128Z In(05) vcpu-0 VNET: 'ethernet0' enable link state propagation, lsp.state = 5
2024-10-20T10:58:12.128Z In(05) vcpu-0 VNET: MACVNetConnectToNetwork 'ethernet0' lsp.state = 4
2024-10-20T10:58:12.129Z In(05) vcpu-0 VNET: MACVNetConnectToNetwork 'Ethernet0' notify available.
2024-10-20T10:58:12.129Z In(05) vcpu-0 HGFSPublish: publishing 0 shares
2024-10-20T10:58:12.130Z No(00) vcpu-0 ConfigDB: Unsetting "sensor.accelerometer"
2024-10-20T10:58:12.130Z No(00) vcpu-0 ConfigDB: Unsetting "sensor.ambientLight"
2024-10-20T10:58:12.130Z No(00) vcpu-0 ConfigDB: Unsetting "sensor.compass"
2024-10-20T10:58:12.130Z No(00) vcpu-0 ConfigDB: Unsetting "sensor.gyrometer"
2024-10-20T10:58:12.130Z No(00) vcpu-0 ConfigDB: Unsetting "sensor.inclinometer"
2024-10-20T10:58:12.130Z No(00) vcpu-0 ConfigDB: Unsetting "sensor.orientation"
2024-10-20T10:58:12.130Z In(05) vcpu-0 Win32U_GetFileAttributes: GetFileAttributesExW("C:\Users\<USER>\Documents\Virtual Machines\windows10\windows10.vmpl", ...) failed, error: 2
2024-10-20T10:58:12.130Z In(05) vcpu-0 PolicyVMXFindPolicyKey: policy file does not exist.
2024-10-20T10:58:12.138Z In(05) vcpu-0 VMXSTATS: Registering 49 stats: vmx.vigor.opsTotal
2024-10-20T10:58:12.138Z In(05) vcpu-0 VMXSTATS: Registering 50 stats: vmx.vigor.opsPerS
2024-10-20T10:58:12.138Z In(05) vcpu-0 VMXSTATS: Registering 51 stats: vmx.vigor.queriesPerS
2024-10-20T10:58:12.138Z In(05) vcpu-0 VMXSTATS: Registering 52 stats: vmx.poll.itersPerS
2024-10-20T10:58:12.138Z In(05) vcpu-0 VMXSTATS: Registering 53 stats: vmx.userRpc.opsPerS
2024-10-20T10:58:12.138Z In(05) vcpu-0 VMXSTATS: Registering 54 stats: vmx.metrics.lastUpdate
2024-10-20T10:58:12.138Z No(00) vcpu-0 Metrics lastUpdate (s): 679930
2024-10-20T10:58:12.138Z In(05) vcpu-0 Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=0, newAppState=1872, success=1 additionalError=0
2024-10-20T10:58:12.138Z In(05) vcpu-0 Vix: [mainDispatch.c:4129]: VMAutomationReportPowerStateChange: Reporting power state change (opcode=0, err=0).
2024-10-20T10:58:12.138Z In(05) vcpu-0 Vix: [mainDispatch.c:4129]: VMAutomationReportPowerStateChange: Reporting power state change (opcode=2, err=0).
2024-10-20T10:58:12.138Z In(05) vcpu-0 Transitioned vmx/execState/val to poweredOn
2024-10-20T10:58:12.138Z In(05) vcpu-0 Tools: Adding Tools inactivity timer.
2024-10-20T10:58:12.138Z In(05) vcpu-0 Intel VT: FlexPriority enabled, VPID enabled.
2024-10-20T10:58:12.138Z In(05) vmx VNET: MACVNetLinkStateEventHandler: event, up:1, adapter:0
2024-10-20T10:58:12.138Z In(05) vmx VNET: MACVNetLinkStateEventHandler: 'ethernet0' state from 4 to 6.
2024-10-20T10:58:12.211Z In(05) mks MKSControlMgr: connected
2024-10-20T10:58:12.222Z In(05) mks MKS-VMDB: VMDB requested a screenshot
2024-10-20T10:58:12.222Z In(05) svga MKSScreenShotMgr: Taking a screenshot
2024-10-20T10:58:12.227Z In(05) svga SWBScreen: Screen 0 Defined: xywh(0, 0, 640, 480) flags=0x3
2024-10-20T10:58:12.227Z In(05) mks KHBKL: Unable to parse keystring at: ''
2024-10-20T10:58:12.239Z In(05) mks SWBWindow: Number of MKSWindows changed: 0 rendering MKSWindow(s) of total 1.
2024-10-20T10:58:12.239Z In(05) mks SWBWindow: Window 1 Defined: src screenId=-1, src xywh(0, 0, 640, 480) dest xywh(0, 0, 640, 480) pixelScale=1, flags=0x10
2024-10-20T10:58:12.239Z In(05) mks GDI-Backend: successfully started by HWinMux to do window composition.
2024-10-20T10:58:12.243Z In(05) mks MKS-HWinMux: Started GDI presentation backend.
2024-10-20T10:58:12.262Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 2.
2024-10-20T10:58:12.262Z In(05) mks SWBWindow: Window 0 Defined: src screenId=-1, src xywh(0, 0, 640, 480) dest xywh(0, 0, 640, 480) pixelScale=1, flags=0xD
2024-10-20T10:58:12.325Z In(05) vcpu-0 SVGA: Registering MemSpace at 0xe8000000(0x0) and 0xfe000000(0x0)
2024-10-20T10:58:12.325Z In(05) vcpu-0 SVGA: FIFO is already mapped
2024-10-20T10:58:12.333Z In(05) vcpu-0 SVGA: Unregistering MemSpace at 0xe8000000(0xe8000000) and 0xfe000000(0xfe000000)
2024-10-20T10:58:12.333Z In(05) vcpu-0 SVGA: FIFO is already mapped
2024-10-20T10:58:12.472Z In(05) vcpu-0 SVGA: Registering MemSpace at 0xe8000000(0xe8000000) and 0xfe000000(0xfe000000)
2024-10-20T10:58:12.472Z In(05) vcpu-0 SVGA: FIFO is already mapped
2024-10-20T10:58:12.480Z In(05) vcpu-0 SVGA: Unregistering MemSpace at 0xe8000000(0xe8000000) and 0xfe000000(0xfe000000)
2024-10-20T10:58:12.480Z In(05) vcpu-0 SVGA: FIFO is already mapped
2024-10-20T10:58:12.480Z In(05) vcpu-0 SVGA: Registering IOSpace at 0x1070
2024-10-20T10:58:12.480Z In(05) vcpu-0 SVGA: Registering MemSpace at 0xe8000000(0xe8000000) and 0xfe000000(0xfe000000)
2024-10-20T10:58:12.480Z In(05) vcpu-0 SVGA: FIFO is already mapped
2024-10-20T10:58:12.488Z In(05) vcpu-1 CPU reset: soft (mode Emulation)
2024-10-20T10:58:12.514Z In(05) vmx VNET: MACVNetLinkStateTimerHandler: 'ethernet0' state from 6 to 1.
2024-10-20T10:58:12.514Z In(05) vcpu-0 SVGA: FIFO is already mapped
2024-10-20T10:58:12.524Z In(05) vcpu-0 SVGA: FIFO is already mapped
2024-10-20T10:58:12.818Z In(05) vcpu-0 DISKUTIL: scsi0:0 : geometry=5221/255/63
2024-10-20T10:58:12.818Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T10:58:13.243Z In(05) vcpu-1 CPU reset: soft (mode Emulation)
2024-10-20T10:58:13.252Z In(05) vcpu-0 BIOS-UUID is 56 4d 2b d7 e7 54 33 41-e0 75 e0 a2 de d9 08 d2
2024-10-20T10:58:13.356Z In(05) svga SWBScreen: Screen 0 Resized: xywh(0, 0, 720, 400) flags=0x3
2024-10-20T10:58:13.705Z In(05) vcpu-0 DDB: "longContentID" = "4718fc56aff352efbdc8d75c81c539ea" (was "********************************")
2024-10-20T10:58:13.707Z In(05) vcpu-0 SVGA: FIFO is already mapped
2024-10-20T10:58:13.707Z In(05) svga SVGA enabling SVGA
2024-10-20T10:58:13.707Z In(05) svga SWBScreen: Screen 0 Destroyed: xywh(0, 0, 720, 400) flags=0x3
2024-10-20T10:58:13.765Z In(05) svga SVGA-ScreenMgr: Screen type changed to RegisterMode
2024-10-20T10:58:13.765Z In(05) svga SWBScreen: Screen 1 Defined: xywh(0, 0, 1024, 768) flags=0x2
2024-10-20T10:58:17.715Z In(05) vcpu-1 CPU reset: soft (mode Emulation)
2024-10-20T10:58:19.485Z In(05) vcpu-0 Preparing for SPEC_CTRL Guest MSR write (0x48) passthrough.
2024-10-20T10:58:20.925Z In(05) vcpu-0 SVGA: Unregistering IOSpace at 0x1070
2024-10-20T10:58:20.925Z In(05) vcpu-0 SVGA: Unregistering MemSpace at 0xe8000000(0xe8000000) and 0xfe000000(0xfe000000)
2024-10-20T10:58:20.925Z In(05) vcpu-0 SVGA: FIFO is already mapped
2024-10-20T10:58:20.926Z In(05) vcpu-0 SVGA: Registering IOSpace at 0x1070
2024-10-20T10:58:20.926Z In(05) vcpu-0 SVGA: Registering MemSpace at 0xe8000000(0xe8000000) and 0xfe000000(0xfe000000)
2024-10-20T10:58:20.926Z In(05) vcpu-0 SVGA: FIFO is already mapped
2024-10-20T10:58:23.643Z In(05) vcpu-1 SCSI0: RESET BUS
2024-10-20T10:58:23.647Z In(05) vcpu-1 LSI: Invalid PageType [21] pageNo 0 Action 0
2024-10-20T10:58:23.727Z In(05) vcpu-1 SCSI scsi0:0: Unsupported command REPORT LUNS issued. --ok
2024-10-20T10:58:23.824Z In(05) vcpu-0 CDROM: Mode Sense for Unsupported Page 0x1B
2024-10-20T10:58:23.824Z In(05) vcpu-0 CDROM ide0:0: CMD 0x5a (MODE SENSE(10)) FAILED (key 0x5 asc 0x24 ascq 0)
2024-10-20T10:58:23.977Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T10:58:23.978Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x1c
2024-10-20T10:58:23.979Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-10-20T10:58:23.979Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-10-20T10:58:23.980Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T10:58:23.982Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T10:58:23.987Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T10:58:23.988Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T10:58:24.209Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T10:58:24.327Z In(05) vmx VNET: MACVNetLinkStateTimerHandler: 'ethernet0' state from 1 to 5.
2024-10-20T10:58:24.923Z In(05) vcpu-0 Guest: vm3d: WDDM Miniport Loading the release build type
2024-10-20T10:58:25.060Z Wa(03) vcpu-0 VIDE: (0x1f0) OUTB Cmd 0x2f, Aborting Unknown Atapi Command
2024-10-20T10:58:25.067Z In(05) vcpu-1 CDROM: Emulate GET CONFIGURATION RT 0 starting feature 0
2024-10-20T10:58:25.068Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 0
2024-10-20T10:58:25.068Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 0 starting feature 0
2024-10-20T10:58:25.069Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 0 starting feature 0
2024-10-20T10:58:25.070Z In(05) vcpu-0 CDROM: Unknown command 0xA4.
2024-10-20T10:58:25.070Z In(05) vcpu-0 CDROM ide0:0: CMD 0xa4 (*UNKNOWN (0xa4)*) FAILED (key 0x5 asc 0x20 ascq 0)
2024-10-20T10:58:25.074Z In(05) vcpu-0 SVGA: Unregistering IOSpace at 0x1070
2024-10-20T10:58:25.075Z In(05) vcpu-0 SVGA: Unregistering MemSpace at 0xe8000000(0xe8000000) and 0xfe000000(0xfe000000)
2024-10-20T10:58:25.075Z In(05) vcpu-0 SVGA: FIFO is already mapped
2024-10-20T10:58:25.076Z In(05) vcpu-0 SVGA: Registering IOSpace at 0x1070
2024-10-20T10:58:25.076Z In(05) vcpu-0 SVGA: Registering MemSpace at 0xe8000000(0xe8000000) and 0xfe000000(0xfe000000)
2024-10-20T10:58:25.077Z In(05) vcpu-0 SVGA: FIFO is already mapped
2024-10-20T10:58:25.077Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 1 starting feature 0
2024-10-20T10:58:25.077Z Wa(03) vcpu-0 VIDE: Truncating 0x5a from 65520 bytes to 32768
2024-10-20T10:58:25.579Z In(05) vcpu-0 Tools: Running status rpc handler: 0 => 1.
2024-10-20T10:58:25.579Z In(05) vcpu-0 Tools: Changing running status: 0 => 1.
2024-10-20T10:58:25.579Z In(05) vcpu-0 Tools: [RunningStatus] Last heartbeat value 1 (last received 0s ago)
2024-10-20T10:58:25.580Z In(05) vcpu-0 Tools: Removing Tools inactivity timer.
2024-10-20T10:58:33.910Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T10:58:34.846Z In(05) vcpu-1 Guest: vm3d: SVGA WDDM Display Only driver, Version: 8.15.01.0065, Build Number: 7718389
2024-10-20T10:58:34.847Z In(05) vcpu-1 Guest: vm3d: WDDM OS version: 10.0, build number: 17763, service pack version: 0.0, platform Id: 2, product type: 1, suite mask: 0x110
2024-10-20T10:58:34.847Z In(05) vcpu-1 SVGA: FIFO is already mapped
2024-10-20T10:58:34.848Z In(05) vcpu-1 SVGA: FIFO is already mapped
2024-10-20T10:58:34.848Z In(05) vcpu-1 SVGA: FIFO is already mapped
2024-10-20T10:58:34.848Z In(05) vcpu-1 SVGA: FIFO is already mapped
2024-10-20T10:58:34.890Z In(05) svga SWBScreen: Screen 1 Destroyed: xywh(0, 0, 1024, 768) flags=0x2
2024-10-20T10:58:34.891Z In(05) svga SWBScreen: Screen 1 Defined: xywh(0, 0, 1024, 768) flags=0x2
2024-10-20T10:58:34.898Z In(05) vcpu-1 Guest: vm3d: Modeset enabled, types available: CCD Multi
2024-10-20T10:58:34.899Z In(05) vcpu-1 Guest: vm3d: WDDM SVGA Grow OTable command not enabled.
2024-10-20T10:58:34.899Z In(05) vcpu-1 Guest: vm3d: WDDM SVGA IntraSurfaceCopy command not enabled.
2024-10-20T10:58:34.899Z In(05) vcpu-1 Guest: vm3d: WDDM Guest backed surface is enabled.
2024-10-20T10:58:34.918Z In(05) vcpu-1 Guest: vm3d: WDDM 3D is disabled.
2024-10-20T10:58:34.919Z In(05) vcpu-1 Guest: vm3d: WDDM DX10 context is disabled.
2024-10-20T10:58:34.919Z In(05) vcpu-1 Guest: vm3d: WDDM GL3 is disabled.
2024-10-20T10:58:34.919Z In(05) vcpu-1 Guest: vm3d: WDDM DX cap is disabled.
2024-10-20T10:58:34.920Z In(05) vcpu-1 Guest: vm3d: WDDM HP Queue is disabled.
2024-10-20T10:58:34.920Z In(05) vcpu-1 Guest: vm3d: WDDM Guest backed primary in aperture is disabled.
2024-10-20T10:58:34.921Z In(05) vcpu-1 Guest: vm3d: WDDM primary bounding box mem 131072KB.
2024-10-20T10:58:34.921Z In(05) vcpu-1 Guest: vm3d: WDDM VRAM 131072KB.
2024-10-20T10:58:34.925Z In(05) svga SWBScreen: Screen 1 Destroyed: xywh(0, 0, 1024, 768) flags=0x2
2024-10-20T10:58:34.926Z In(05) vcpu-1 Guest: vm3d: WDDM using 12KB memory for OTable.
2024-10-20T10:58:34.941Z In(05) vcpu-1 Guest: vm3d: tap: init
2024-10-20T10:58:34.942Z In(05) vcpu-1 Guest: vm3d: svgadevtap: Allocating server, size=15224 bytes
2024-10-20T10:58:35.167Z No(00) svga ConfigDB: Setting svga.guestBackedPrimaryAware = "TRUE"
2024-10-20T10:58:35.167Z In(05) svga SVGA-ScreenMgr: Screen type changed to ScreenTarget
2024-10-20T10:58:35.167Z In(05) svga SWBScreen: Screen 1 Defined: xywh(0, 0, 1024, 768) flags=0x2
2024-10-20T10:58:36.165Z In(05) vcpu-0 Guest MSR write (0x49: 0x1)
2024-10-20T10:58:41.610Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 0
2024-10-20T10:58:41.640Z In(05) vcpu-1 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 30
2024-10-20T10:58:41.640Z In(05) vcpu-1 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 31
2024-10-20T10:58:41.641Z In(05) vcpu-1 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 32
2024-10-20T10:58:41.641Z In(05) vcpu-1 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 33
2024-10-20T10:58:41.642Z In(05) vcpu-1 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 35
2024-10-20T10:58:41.642Z In(05) vcpu-1 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 36
2024-10-20T10:58:41.643Z In(05) vcpu-1 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 38
2024-10-20T10:58:41.643Z In(05) vcpu-1 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 42
2024-10-20T10:58:41.644Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 43
2024-10-20T10:58:41.645Z In(05) vcpu-1 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 45
2024-10-20T10:58:41.645Z In(05) vcpu-1 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 46
2024-10-20T10:58:41.645Z In(05) vcpu-1 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 47
2024-10-20T10:58:41.645Z In(05) vcpu-1 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 64
2024-10-20T10:58:41.646Z In(05) vcpu-1 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 65
2024-10-20T10:58:41.647Z In(05) vcpu-1 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 259
2024-10-20T10:58:47.391Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T10:58:48.022Z In(05) vmx GuestRpc: Got RPCI vsocket connection 5, assigned to channel 0.
2024-10-20T10:58:49.263Z In(05) vmx Guest: toolbox: Version: build-8068393
2024-10-20T10:58:49.376Z Wa(03) vcpu-1 GuestRpc: application toolbox, changing channel 65535 -> 0
2024-10-20T10:58:49.376Z In(05) vcpu-1 GuestRpc: Channel 0, guest application toolbox.
2024-10-20T10:58:49.376Z In(05) vcpu-1 Tools: [AppStatus] Last heartbeat value 2 (last received 13s ago)
2024-10-20T10:58:49.376Z In(05) vcpu-1 TOOLS: appName=toolbox, oldStatus=0, status=1, guestInitiated=0.
2024-10-20T10:58:49.712Z In(05) vmx TOOLS autoupgrade protocol version 2
2024-10-20T10:58:49.732Z In(05) vmx Tools: Changing running status: 1 => 2.
2024-10-20T10:58:49.732Z In(05) vmx Tools: [RunningStatus] Last heartbeat value 3 (last received 0s ago)
2024-10-20T10:58:49.869Z In(05) vmx TOOLS Received tools.set.versiontype rpc call, version = 10309, type = 1
2024-10-20T10:58:49.870Z In(05) vmx Tools_SetVersionAndType did nothing; new tools version (10309) and type (1) match old Tools version and type
2024-10-20T10:58:49.870Z In(05) vmx ToolsISO: Refreshing imageName for 'windows9-64' (refreshCount=1, lastCount=1).
2024-10-20T10:58:49.870Z In(05) vmx ToolsISO: open of C:\Program Files (x86)\VMware\VMware Workstation\isoimages_manifest.txt.sig failed: Could not find the file
2024-10-20T10:58:49.870Z In(05) vmx ToolsISO: Unable to read signature file 'C:\Program Files (x86)\VMware\VMware Workstation\isoimages_manifest.txt.sig', ignoring.
2024-10-20T10:58:49.871Z In(05) vmx ToolsISO: Updated cached value for imageName to 'windows.iso'.
2024-10-20T10:58:49.871Z In(05) vmx ToolsISO: Selected Tools ISO 'windows.iso' for 'windows9-64' guest.
2024-10-20T10:58:49.871Z In(05) vmx Win32U_GetFileAttributes: GetFileAttributesExW("C:\Program Files (x86)\VMware\VMware Workstation\windows.iso", ...) failed, error: 2
2024-10-20T10:58:49.871Z In(05) vmx TOOLS updated cached value for isoImageExists to 0.
2024-10-20T10:58:49.871Z In(05) vmx VMware Workstation did not ship with the tools ISO.
2024-10-20T10:58:49.871Z In(05) vmx TOOLS Setting autoupgrade-checked TRUE.
2024-10-20T10:58:49.887Z In(05) vcpu-0 Tools: State change '3' progress: last event 0, event 1, success 1.
2024-10-20T10:58:49.895Z In(05) vmx Tools_SetGuestResolution: Sending rpcMsg = Resolution_Set 1718 878
2024-10-20T10:58:50.126Z In(05) vcpu-1 Guest: DXUM_devapi: 2024-10-20T13:58:49.0910| Thread ID: 2684 |Application requested vm3ddevapi version 2
2024-10-20T10:58:50.304Z In(05) svga SWBScreen: Screen 1 Destroyed: xywh(0, 0, 1024, 768) flags=0x2
2024-10-20T10:58:50.306Z In(05) svga SWBScreen: Screen 1 Defined: xywh(0, 0, 1718, 878) flags=0x0
2024-10-20T10:58:52.813Z In(05) vmx TOOLS state change 3 returned status 1
2024-10-20T10:58:52.813Z In(05) vmx Tools: State change '3' progress: last event 1, event 2, success 1.
2024-10-20T10:58:52.813Z In(05) vmx Tools: State change '3' progress: last event 1, event 4, success 1.
2024-10-20T10:58:52.813Z In(05) vmx Vix: [mainDispatch.c:4129]: VMAutomationReportPowerStateChange: Reporting power state change (opcode=2, err=0).
2024-10-20T10:58:52.813Z In(05) vmx Tools: Changing running status: 2 => 1.
2024-10-20T10:58:52.813Z In(05) vmx Tools: [RunningStatus] Last heartbeat value 6 (last received 0s ago)
2024-10-20T10:58:59.455Z In(05) vcpu-0 VMMouse: CMD Read ID
2024-10-20T10:59:04.914Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-10-20T10:59:04.914Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-10-20T10:59:04.928Z In(05) vcpu-1 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-10-20T10:59:04.928Z In(05) vcpu-1 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-10-20T10:59:04.953Z In(05) vcpu-1 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-10-20T10:59:04.953Z In(05) vcpu-1 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-10-20T10:59:04.960Z In(05) vcpu-1 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-10-20T10:59:04.960Z In(05) vcpu-1 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-10-20T10:59:04.967Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-10-20T10:59:04.967Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-10-20T10:59:04.972Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-10-20T10:59:04.973Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-10-20T10:59:33.992Z In(05) vmx Tools_SetGuestResolution: Sending rpcMsg = Resolution_Set 1920 1080
2024-10-20T10:59:36.165Z In(05) vcpu-0 Guest: DXUM_devapi: 2024-10-20T13:59:35.0941| Thread ID: 4584 |Application requested vm3ddevapi version 2
2024-10-20T10:59:36.414Z In(05) svga SWBScreen: Screen 1 Destroyed: xywh(0, 0, 1718, 878) flags=0x0
2024-10-20T10:59:36.415Z In(05) svga SWBScreen: Screen 1 Defined: xywh(0, 0, 1920, 1080) flags=0x0
2024-10-20T10:59:38.149Z No(00) vmx ConfigDB: Setting gui.lastPoweredViewMode = "fullscreen"
2024-10-20T10:59:38.185Z In(05) vmx TOOLS received request in VMX to set option 'synctime' -> '1'
2024-10-20T10:59:38.185Z No(00) vmx ConfigDB: Setting tools.syncTime = "TRUE"
2024-10-20T10:59:38.191Z In(05) vmx VMXVmdb_SetCfgState: cfgReqPath=/vm/#_VMX/vmx/cfgState/req/#7/, remDevPath=/vm/#_VMX/vmx/vigor/setCfgStateReq/#d/in/
2024-10-20T10:59:38.277Z In(05) vmx TOOLS received request in VMX to set option 'synctime' -> '1'
2024-10-20T10:59:38.277Z No(00) vmx ConfigDB: Setting tools.syncTime = "TRUE"
2024-10-20T10:59:38.290Z No(00) vmx ConfigDB: Setting tools.upgrade.policy = "upgradeAtPowerCycle"
2024-10-20T10:59:48.549Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T10:59:51.594Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T10:59:58.076Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T10:59:58.837Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T10:59:59.494Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T10:59:59.786Z In(05) vcpu-1 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-10-20T10:59:59.786Z In(05) vcpu-1 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-10-20T10:59:59.790Z In(05) vcpu-1 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-10-20T10:59:59.790Z In(05) vcpu-1 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-10-20T10:59:59.812Z In(05) vcpu-1 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-10-20T10:59:59.812Z In(05) vcpu-1 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-10-20T10:59:59.903Z In(05) vcpu-1 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-10-20T10:59:59.904Z In(05) vcpu-1 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-10-20T10:59:59.910Z In(05) vcpu-1 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-10-20T10:59:59.911Z In(05) vcpu-1 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-10-20T11:00:00.010Z In(05) vcpu-1 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-10-20T11:00:00.010Z In(05) vcpu-1 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-10-20T11:00:02.071Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:00:02.073Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:00:10.070Z In(05) vcpu-1 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 0
2024-10-20T11:00:10.071Z In(05) vcpu-1 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 0
2024-10-20T11:00:10.082Z In(05) vcpu-1 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 0
2024-10-20T11:00:10.083Z In(05) vcpu-1 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 0
2024-10-20T11:00:10.084Z In(05) vcpu-1 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 45
2024-10-20T11:00:10.085Z In(05) vcpu-1 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 45
2024-10-20T11:00:10.085Z In(05) vcpu-1 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 47
2024-10-20T11:00:10.086Z In(05) vcpu-1 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 47
2024-10-20T11:00:10.086Z In(05) vcpu-1 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 43
2024-10-20T11:00:10.087Z In(05) vcpu-1 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 43
2024-10-20T11:00:10.087Z In(05) vcpu-1 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 42
2024-10-20T11:00:10.088Z In(05) vcpu-1 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 42
2024-10-20T11:00:10.088Z In(05) vcpu-1 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 59
2024-10-20T11:00:10.089Z In(05) vcpu-1 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 59
2024-10-20T11:00:10.089Z In(05) vcpu-1 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 65
2024-10-20T11:00:10.090Z In(05) vcpu-1 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 65
2024-10-20T11:00:10.090Z In(05) vcpu-1 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 0
2024-10-20T11:00:10.091Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 0
2024-10-20T11:00:10.104Z In(05) vcpu-1 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 0
2024-10-20T11:00:10.105Z In(05) vcpu-1 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 0
2024-10-20T11:00:10.105Z In(05) vcpu-1 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 45
2024-10-20T11:00:10.105Z In(05) vcpu-1 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 45
2024-10-20T11:00:10.106Z In(05) vcpu-1 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 47
2024-10-20T11:00:10.107Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 47
2024-10-20T11:00:10.107Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 43
2024-10-20T11:00:10.107Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 43
2024-10-20T11:00:10.109Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 42
2024-10-20T11:00:10.109Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 42
2024-10-20T11:00:10.110Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 59
2024-10-20T11:00:10.111Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 59
2024-10-20T11:00:10.111Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 65
2024-10-20T11:00:10.112Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 65
2024-10-20T11:00:10.112Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 0
2024-10-20T11:00:10.113Z In(05) vcpu-0 CDROM: Emulate GET CONFIGURATION RT 2 starting feature 0
2024-10-20T11:00:28.800Z In(05) vmx GuestRpc: Got RPCI vsocket connection 6, assigned to channel 1.
2024-10-20T11:00:32.939Z In(05) vmx GuestRpc: Got RPCI vsocket connection 7, assigned to channel 2.
2024-10-20T11:00:37.759Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:00:38.169Z In(05) vmx GuestRpcSendTimedOut: message to toolbox-dnd timed out.
2024-10-20T11:00:38.192Z In(05) vmx GuestRpcSendTimedOut: message to toolbox-dnd timed out.
2024-10-20T11:00:38.285Z In(05) vmx GuestRpcSendTimedOut: message to toolbox-dnd timed out.
2024-10-20T11:00:38.297Z In(05) vmx GuestRpcSendTimedOut: message to toolbox-dnd timed out.
2024-10-20T11:00:39.535Z In(05) vmx Guest: toolbox-dnd: Version: build-8068393
2024-10-20T11:00:39.558Z Wa(03) vcpu-0 GuestRpc: application toolbox-dnd, changing channel 65535 -> 1
2024-10-20T11:00:39.558Z In(05) vcpu-0 GuestRpc: Channel 1, guest application toolbox-dnd.
2024-10-20T11:00:39.558Z In(05) vcpu-0 TOOLS: appName=toolbox-dnd, oldStatus=0, status=1, guestInitiated=0.
2024-10-20T11:00:39.562Z In(05) vmx DnDCP: dndGuestVersion from vmdb failed, setting to 4
2024-10-20T11:00:39.562Z In(05) vmx DnDCP: set guest controllers to version 4
2024-10-20T11:00:39.688Z In(05) vmx DnDCP: set guest controllers to version 4
2024-10-20T11:00:39.689Z In(05) vmx DnDCP: set guest controllers to version 4
2024-10-20T11:00:39.962Z No(00) vmx ConfigDB: Setting unity.wasCapable = "TRUE"
2024-10-20T11:00:39.996Z In(05) vmx DnDCP: set guest controllers to version 4
2024-10-20T11:00:40.517Z In(05) vcpu-1 TOOLS call to ghi.guest.setDisplayScaling failed.
2024-10-20T11:00:47.895Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:00:53.738Z In(05) vmx DISKLIB-LIB   : numIOs = 50000 numMergedIOs = 11917 numSplitIOs = 23
2024-10-20T11:00:59.560Z In(05) vmx GuestRpcSendTimedOut: message to toolbox-dnd timed out.
2024-10-20T11:00:59.560Z In(05) vmx TOOLS: appName=toolbox-dnd, oldStatus=1, status=2, guestInitiated=0.
2024-10-20T11:01:04.890Z In(05) vmx GuestRpcSendTimedOut: message to toolbox-dnd timed out.
2024-10-20T11:01:05.362Z In(05) vcpu-0 TOOLS: appName=toolbox-dnd, oldStatus=2, status=1, guestInitiated=0.
2024-10-20T11:01:13.327Z In(05) mks MKS-VMDB: VMDB requested a screenshot
2024-10-20T11:01:13.327Z In(05) svga MKSScreenShotMgr: Taking a screenshot
2024-10-20T11:01:13.637Z In(05) mks SWBWindow: Number of MKSWindows changed: 2 rendering MKSWindow(s) of total 2.
2024-10-20T11:01:13.705Z In(05) mks SWBWindow: Window #0 validation failed: no valid host window or host surface.
2024-10-20T11:01:13.705Z In(05) mks SWBVmdb: Destroy SWB Window Id #0 because an invalid MKSWindow definition is received from UI over VMDB.
2024-10-20T11:01:13.705Z In(05) mks SWBWindow: Window 0 Destroyed: src screenId=-1, src xywh(0, 0, 1920, 1080) dest xywh(0, 0, 1920, 1080) pixelScale=1, flags=0xF
2024-10-20T11:01:13.707Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 1.
2024-10-20T11:01:13.707Z In(05) mks SWBWindow: Window #1 validation failed: no valid host window or host surface.
2024-10-20T11:01:13.707Z In(05) mks SWBVmdb: Destroy SWB Window Id #1 because an invalid MKSWindow definition is received from UI over VMDB.
2024-10-20T11:01:13.707Z In(05) mks SWBWindow: Window 1 Destroyed: src screenId=-1, src xywh(0, 0, 1920, 1080) dest xywh(0, 0, 1920, 1080) pixelScale=1, flags=0x18
2024-10-20T11:01:13.708Z In(05) mks GDI-Backend: stopped by HWinMux to do window composition.
2024-10-20T11:01:13.708Z In(05) mks SWBWindow: Number of MKSWindows changed: 0 rendering MKSWindow(s) of total 0.
2024-10-20T11:01:18.572Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 1.
2024-10-20T11:01:18.572Z In(05) mks SWBWindow: Window 0 Defined: src screenId=-1, src xywh(0, 0, 1920, 1080) dest xywh(0, 0, 1920, 1080) pixelScale=1, flags=0xF
2024-10-20T11:01:18.572Z In(05) mks GDI-Backend: successfully started by HWinMux to do window composition.
2024-10-20T11:01:18.578Z In(05) mks MKS-HWinMux: Started GDI presentation backend.
2024-10-20T11:01:18.580Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 2.
2024-10-20T11:01:18.580Z In(05) mks SWBWindow: Window 1 Defined: src screenId=-1, src xywh(0, 0, 1920, 1080) dest xywh(0, 0, 100, 100) pixelScale=1, flags=0x10
2024-10-20T11:01:37.898Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:01:38.022Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:01:45.949Z In(05) mks MKS-VMDB: VMDB requested a screenshot
2024-10-20T11:01:45.949Z In(05) svga MKSScreenShotMgr: Taking a screenshot
2024-10-20T11:01:47.473Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:01:47.714Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:01:49.266Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:01:52.892Z In(05) mks MKS-VMDB: VMDB requested a screenshot
2024-10-20T11:01:52.892Z In(05) svga MKSScreenShotMgr: Taking a screenshot
2024-10-20T11:01:55.290Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:01:56.208Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:01:56.586Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:01:57.195Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:01:59.760Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:02:00.121Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:02:00.380Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:02:09.067Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:02:10.253Z No(00) vmx ConfigDB: Setting gui.stretchGuestMode = "fullfill"
2024-10-20T11:02:10.289Z No(00) vmx ConfigDB: Setting gui.enableStretchGuest = "TRUE"
2024-10-20T11:02:10.324Z In(05) vmx TOOLS received request in VMX to set option 'synctime' -> '1'
2024-10-20T11:02:10.324Z No(00) vmx ConfigDB: Setting tools.syncTime = "TRUE"
2024-10-20T11:02:10.331Z In(05) vmx VMXVmdb_SetCfgState: cfgReqPath=/vm/#_VMX/vmx/cfgState/req/#d/, remDevPath=/vm/#_VMX/vmx/vigor/setCfgStateReq/#17/in/
2024-10-20T11:02:10.334Z In(05) vmx ToolsSetDisplayTopology: Sending rpcMsg = DisplayTopology_Set 1 , 0 0 1718 878
2024-10-20T11:02:10.391Z In(05) vmx TOOLS received request in VMX to set option 'synctime' -> '1'
2024-10-20T11:02:10.391Z No(00) vmx ConfigDB: Setting tools.syncTime = "TRUE"
2024-10-20T11:02:10.402Z No(00) vmx ConfigDB: Setting tools.upgrade.policy = "upgradeAtPowerCycle"
2024-10-20T11:02:10.463Z In(05) vcpu-0 Guest: DXUM_devapi: 2024-10-20T14:01:56.0151| Thread ID: 7628 |Application requested vm3ddevapi version 2
2024-10-20T11:02:10.608Z No(00) vmx ConfigDB: Unsetting "gui.enableStretchGuest"
2024-10-20T11:02:10.651Z In(05) vmx TOOLS received request in VMX to set option 'synctime' -> '1'
2024-10-20T11:02:10.652Z No(00) vmx ConfigDB: Setting tools.syncTime = "TRUE"
2024-10-20T11:02:10.661Z In(05) vmx VMXVmdb_SetCfgState: cfgReqPath=/vm/#_VMX/vmx/cfgState/req/#e/, remDevPath=/vm/#_VMX/vmx/vigor/setCfgStateReq/#1d/in/
2024-10-20T11:02:10.666Z In(05) vmx Tools_SetGuestResolution: Sending rpcMsg = Resolution_Set 1920 1080
2024-10-20T11:02:10.691Z In(05) svga SWBScreen: Screen 1 Destroyed: xywh(0, 0, 1920, 1080) flags=0x0
2024-10-20T11:02:10.695Z In(05) svga SWBScreen: Screen 1 Defined: xywh(0, 0, 1718, 878) flags=0x0
2024-10-20T11:02:10.746Z In(05) vmx TOOLS received request in VMX to set option 'synctime' -> '1'
2024-10-20T11:02:10.747Z No(00) vmx ConfigDB: Setting tools.syncTime = "TRUE"
2024-10-20T11:02:10.760Z No(00) vmx ConfigDB: Setting tools.upgrade.policy = "upgradeAtPowerCycle"
2024-10-20T11:02:11.027Z In(05) vcpu-1 TOOLS call to ghi.guest.setDisplayScaling failed.
2024-10-20T11:02:11.225Z In(05) vmx VNET: MACVNetLinkStateEventHandler: event, up:0, adapter:1
2024-10-20T11:02:11.225Z In(05) vmx VNET: MACVNetLinkStateEventHandler: 'ethernet0' state from 5 to 7.
2024-10-20T11:02:11.225Z In(05) vmx VNET: MACVNetLinkStateEventHandler: event, up:1, adapter:0
2024-10-20T11:02:11.225Z In(05) vmx VNET: MACVNetLinkStateEventHandler: 'ethernet0' state from 7 to 2.
2024-10-20T11:02:12.838Z In(05) vcpu-0 Guest: DXUM_devapi: 2024-10-20T14:01:58.0286| Thread ID: 7860 |Application requested vm3ddevapi version 2
2024-10-20T11:02:12.934Z In(05) svga SWBScreen: Screen 1 Destroyed: xywh(0, 0, 1718, 878) flags=0x0
2024-10-20T11:02:12.941Z In(05) svga SWBScreen: Screen 1 Defined: xywh(0, 0, 1920, 1080) flags=0x0
2024-10-20T11:02:13.180Z In(05) vcpu-0 TOOLS call to ghi.guest.setDisplayScaling failed.
2024-10-20T11:02:14.485Z No(00) vmx ConfigDB: Setting gui.enableStretchGuest = "TRUE"
2024-10-20T11:02:14.521Z In(05) vmx TOOLS received request in VMX to set option 'synctime' -> '1'
2024-10-20T11:02:14.522Z No(00) vmx ConfigDB: Setting tools.syncTime = "TRUE"
2024-10-20T11:02:14.528Z In(05) vmx VMXVmdb_SetCfgState: cfgReqPath=/vm/#_VMX/vmx/cfgState/req/#f/, remDevPath=/vm/#_VMX/vmx/vigor/setCfgStateReq/#23/in/
2024-10-20T11:02:14.530Z In(05) vmx ToolsSetDisplayTopology: Sending rpcMsg = DisplayTopology_Set 1 , 0 0 1718 878
2024-10-20T11:02:14.578Z In(05) vmx TOOLS received request in VMX to set option 'synctime' -> '1'
2024-10-20T11:02:14.578Z No(00) vmx ConfigDB: Setting tools.syncTime = "TRUE"
2024-10-20T11:02:14.598Z No(00) vmx ConfigDB: Setting tools.upgrade.policy = "upgradeAtPowerCycle"
2024-10-20T11:02:14.620Z In(05) vcpu-1 Guest: DXUM_devapi: 2024-10-20T14:01:59.0889| Thread ID: 7908 |Application requested vm3ddevapi version 2
2024-10-20T11:02:14.784Z In(05) svga SWBScreen: Screen 1 Destroyed: xywh(0, 0, 1920, 1080) flags=0x0
2024-10-20T11:02:14.790Z In(05) svga SWBScreen: Screen 1 Defined: xywh(0, 0, 1718, 878) flags=0x0
2024-10-20T11:02:15.151Z In(05) vcpu-0 TOOLS call to ghi.guest.setDisplayScaling failed.
2024-10-20T11:02:15.672Z In(05) vmx VNET: MACVNetLinkStateEventHandler: event, up:0, adapter:1
2024-10-20T11:02:15.672Z In(05) vmx VNET: MACVNetLinkStateEventHandler: 'ethernet0' state from 2 to 0.
2024-10-20T11:02:15.672Z In(05) vmx VNET: MACVNetLinkStateEventHandler: event, up:1, adapter:0
2024-10-20T11:02:15.672Z In(05) vmx VNET: MACVNetLinkStateEventHandler: 'ethernet0' state from 0 to 2.
2024-10-20T11:02:18.052Z In(05) vmx VNET: MACVNetLinkStateTimerHandler: 'ethernet0' state from 2 to 6.
2024-10-20T11:02:18.171Z In(05) vmx VNET: MACVNetLinkStateTimerHandler: 'ethernet0' state from 6 to 1.
2024-10-20T11:02:18.300Z No(00) vmx ConfigDB: Unsetting "gui.enableStretchGuest"
2024-10-20T11:02:18.331Z In(05) vmx TOOLS received request in VMX to set option 'synctime' -> '1'
2024-10-20T11:02:18.332Z No(00) vmx ConfigDB: Setting tools.syncTime = "TRUE"
2024-10-20T11:02:18.338Z In(05) vmx VMXVmdb_SetCfgState: cfgReqPath=/vm/#_VMX/vmx/cfgState/req/#10/, remDevPath=/vm/#_VMX/vmx/vigor/setCfgStateReq/#29/in/
2024-10-20T11:02:18.342Z In(05) vmx Tools_SetGuestResolution: Sending rpcMsg = Resolution_Set 1920 1080
2024-10-20T11:02:18.392Z In(05) vmx TOOLS received request in VMX to set option 'synctime' -> '1'
2024-10-20T11:02:18.393Z No(00) vmx ConfigDB: Setting tools.syncTime = "TRUE"
2024-10-20T11:02:18.407Z No(00) vmx ConfigDB: Setting tools.upgrade.policy = "upgradeAtPowerCycle"
2024-10-20T11:02:18.543Z In(05) vcpu-0 Guest: DXUM_devapi: 2024-10-20T14:02:03.0420| Thread ID: 8044 |Application requested vm3ddevapi version 2
2024-10-20T11:02:18.700Z In(05) svga SWBScreen: Screen 1 Destroyed: xywh(0, 0, 1718, 878) flags=0x0
2024-10-20T11:02:18.703Z In(05) svga SWBScreen: Screen 1 Defined: xywh(0, 0, 1920, 1080) flags=0x0
2024-10-20T11:02:19.002Z In(05) vcpu-1 TOOLS call to ghi.guest.setDisplayScaling failed.
2024-10-20T11:02:21.078Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:02:21.626Z In(05) vmx ToolsSetDisplayTopology: Sending rpcMsg = DisplayTopology_Set 1 , 0 0 1718 878
2024-10-20T11:02:21.649Z In(05) vmx Tools_SetGuestResolution: Sending rpcMsg = Resolution_Set 1714 874
2024-10-20T11:02:21.766Z In(05) vcpu-0 Guest: DXUM_devapi: 2024-10-20T14:02:06.0316| Thread ID: 3992 |Application requested vm3ddevapi version 2
2024-10-20T11:02:21.913Z In(05) svga SWBScreen: Screen 1 Destroyed: xywh(0, 0, 1920, 1080) flags=0x0
2024-10-20T11:02:21.915Z In(05) svga SWBScreen: Screen 1 Defined: xywh(0, 0, 1718, 878) flags=0x0
2024-10-20T11:02:22.226Z In(05) vcpu-0 TOOLS call to ghi.guest.setDisplayScaling failed.
2024-10-20T11:02:23.101Z In(05) vcpu-0 Guest: DXUM_devapi: 2024-10-20T14:02:07.0526| Thread ID: 6716 |Application requested vm3ddevapi version 2
2024-10-20T11:02:23.207Z In(05) svga SWBScreen: Screen 1 Destroyed: xywh(0, 0, 1718, 878) flags=0x0
2024-10-20T11:02:23.208Z In(05) svga SWBScreen: Screen 1 Defined: xywh(0, 0, 1714, 874) flags=0x0
2024-10-20T11:02:23.528Z In(05) vcpu-1 TOOLS call to ghi.guest.setDisplayScaling failed.
2024-10-20T11:02:24.555Z In(05) vmx VNET: MACVNetLinkStateTimerHandler: 'ethernet0' state from 1 to 5.
2024-10-20T11:02:24.918Z In(05) mks MKS-VMDB: VMDB requested a screenshot
2024-10-20T11:02:24.918Z In(05) svga MKSScreenShotMgr: Taking a screenshot
2024-10-20T11:02:25.278Z In(05) mks SWBWindow: Number of MKSWindows changed: 2 rendering MKSWindow(s) of total 2.
2024-10-20T11:02:25.340Z In(05) mks SWBWindow: Window #0 validation failed: no valid host window or host surface.
2024-10-20T11:02:25.340Z In(05) mks SWBVmdb: Destroy SWB Window Id #0 because an invalid MKSWindow definition is received from UI over VMDB.
2024-10-20T11:02:25.340Z In(05) mks SWBWindow: Window 0 Destroyed: src screenId=-1, src xywh(0, 0, 1714, 874) dest xywh(0, 0, 1714, 874) pixelScale=1, flags=0xF
2024-10-20T11:02:25.341Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 1.
2024-10-20T11:02:25.341Z In(05) mks SWBWindow: Window #1 validation failed: no valid host window or host surface.
2024-10-20T11:02:25.341Z In(05) mks SWBVmdb: Destroy SWB Window Id #1 because an invalid MKSWindow definition is received from UI over VMDB.
2024-10-20T11:02:25.341Z In(05) mks SWBWindow: Window 1 Destroyed: src screenId=-1, src xywh(0, 0, 1714, 874) dest xywh(0, 0, 1714, 874) pixelScale=1, flags=0x18
2024-10-20T11:02:25.343Z In(05) mks GDI-Backend: stopped by HWinMux to do window composition.
2024-10-20T11:02:25.344Z In(05) mks SWBWindow: Number of MKSWindows changed: 0 rendering MKSWindow(s) of total 0.
2024-10-20T11:03:13.934Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:03:42.152Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:03:43.708Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:03:44.957Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:03:46.357Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:03:47.256Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:03:50.409Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:03:52.904Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:03:54.199Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:03:55.415Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:03:57.922Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:04:00.259Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:04:01.388Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:04:02.172Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:04:03.917Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:04:06.011Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:04:08.234Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:04:10.304Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:04:11.769Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:04:12.888Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:04:13.876Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:04:14.898Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:04:15.877Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:04:16.706Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:04:19.501Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:04:22.348Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:04:23.369Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:04:24.348Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:04:26.301Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:04:28.826Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:04:30.503Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:04:31.940Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:04:32.985Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:04:33.869Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:04:35.548Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:04:36.968Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:04:40.988Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:04:45.050Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:04:45.830Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:04:45.881Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:04:49.096Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:04:51.881Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:04:58.292Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:05:00.277Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:05:02.758Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:05:05.258Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:05:08.412Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:05:09.870Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:05:12.807Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:05:15.603Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:05:17.935Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:05:20.029Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:05:22.708Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:05:25.201Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:05:28.187Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:05:30.865Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:05:32.868Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:05:34.892Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:05:36.225Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:05:37.465Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:05:39.555Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:05:41.625Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:05:42.748Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:05:43.949Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:05:46.265Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:05:48.488Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:05:50.061Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:05:51.337Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:05:52.426Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:05:53.154Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:05:57.068Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:06:01.041Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:06:03.499Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:06:05.551Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:06:08.413Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:06:11.187Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:06:13.141Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:06:14.921Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:06:17.089Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:07:07.629Z In(05) vmx GuestRpc: Got RPCI vsocket connection 8, assigned to channel 3.
2024-10-20T11:07:07.682Z In(05) vmx GuestRpc: Got error for channel 3 connection 8: Remote disconnected
2024-10-20T11:07:07.682Z In(05) vmx GuestRpc: GuestRpcResetVsockChannel: channel 3
2024-10-20T11:07:07.682Z In(05) vmx GuestRpc: Closing channel 3 connection 8
2024-10-20T11:07:47.137Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:08:02.810Z In(05) vmx DISKLIB-LIB   : numIOs = 100000 numMergedIOs = 21587 numSplitIOs = 320
2024-10-20T11:08:19.983Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:08:37.540Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:08:56.441Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:09:02.561Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:09:10.342Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:09:15.174Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:09:18.542Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:09:23.298Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:09:27.635Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:09:32.169Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:09:56.824Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:10:05.687Z In(05) vmx GuestRpc: Got RPCI vsocket connection 9, assigned to channel 3.
2024-10-20T11:10:05.734Z In(05) vmx GuestRpc: Got error for channel 3 connection 9: Remote disconnected
2024-10-20T11:10:05.734Z In(05) vmx GuestRpc: GuestRpcResetVsockChannel: channel 3
2024-10-20T11:10:05.734Z In(05) vmx GuestRpc: Closing channel 3 connection 9
2024-10-20T11:10:08.067Z In(05) vcpu-1 VMCIDatagram: Destination resource 13 unsupported.
2024-10-20T11:10:08.067Z In(05) vcpu-1 VMCIDatagram: Destination resource 13 unsupported.
2024-10-20T11:11:51.140Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:11:58.682Z In(05) vmx GuestRpc: Got RPCI vsocket connection 10, assigned to channel 3.
2024-10-20T11:11:58.687Z In(05) vmx GuestRpc: Got error for channel 3 connection 10: Remote disconnected
2024-10-20T11:11:58.687Z In(05) vmx GuestRpc: GuestRpcResetVsockChannel: channel 3
2024-10-20T11:11:58.687Z In(05) vmx GuestRpc: Closing channel 3 connection 10
2024-10-20T11:12:07.233Z In(05) vmx GuestRpc: Got RPCI vsocket connection 11, assigned to channel 3.
2024-10-20T11:12:07.235Z In(05) vmx GuestRpc: Got error for channel 3 connection 11: Remote disconnected
2024-10-20T11:12:07.236Z In(05) vmx GuestRpc: GuestRpcResetVsockChannel: channel 3
2024-10-20T11:12:07.236Z In(05) vmx GuestRpc: Closing channel 3 connection 11
2024-10-20T11:12:17.441Z In(05) vmx GuestRpc: Got RPCI vsocket connection 12, assigned to channel 3.
2024-10-20T11:12:17.442Z In(05) vmx GuestRpc: Got error for channel 3 connection 12: Remote disconnected
2024-10-20T11:12:17.443Z In(05) vmx GuestRpc: GuestRpcResetVsockChannel: channel 3
2024-10-20T11:12:17.443Z In(05) vmx GuestRpc: Closing channel 3 connection 12
2024-10-20T11:12:33.513Z In(05) vmx GuestRpc: Got RPCI vsocket connection 13, assigned to channel 3.
2024-10-20T11:12:33.551Z In(05) vmx GuestRpc: Got error for channel 3 connection 13: Remote disconnected
2024-10-20T11:12:33.551Z In(05) vmx GuestRpc: GuestRpcResetVsockChannel: channel 3
2024-10-20T11:12:33.551Z In(05) vmx GuestRpc: Closing channel 3 connection 13
2024-10-20T11:13:37.769Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:13:47.028Z In(05) vmx GuestRpc: Got RPCI vsocket connection 14, assigned to channel 3.
2024-10-20T11:13:47.031Z In(05) vmx GuestRpc: Got error for channel 3 connection 14: Remote disconnected
2024-10-20T11:13:47.031Z In(05) vmx GuestRpc: GuestRpcResetVsockChannel: channel 3
2024-10-20T11:13:47.031Z In(05) vmx GuestRpc: Closing channel 3 connection 14
2024-10-20T11:14:01.449Z In(05) vmx GuestRpc: Got RPCI vsocket connection 15, assigned to channel 3.
2024-10-20T11:14:01.450Z In(05) vmx GuestRpc: Got error for channel 3 connection 15: Remote disconnected
2024-10-20T11:14:01.450Z In(05) vmx GuestRpc: GuestRpcResetVsockChannel: channel 3
2024-10-20T11:14:01.450Z In(05) vmx GuestRpc: Closing channel 3 connection 15
2024-10-20T11:15:26.846Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:15:35.818Z In(05) vmx GuestRpc: Got RPCI vsocket connection 16, assigned to channel 3.
2024-10-20T11:15:35.821Z In(05) vmx GuestRpc: Got error for channel 3 connection 16: Remote disconnected
2024-10-20T11:15:35.821Z In(05) vmx GuestRpc: GuestRpcResetVsockChannel: channel 3
2024-10-20T11:15:35.821Z In(05) vmx GuestRpc: Closing channel 3 connection 16
2024-10-20T11:16:35.022Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:16:43.061Z In(05) vmx GuestRpc: Got RPCI vsocket connection 17, assigned to channel 3.
2024-10-20T11:16:43.064Z In(05) vmx GuestRpc: Got error for channel 3 connection 17: Remote disconnected
2024-10-20T11:16:43.064Z In(05) vmx GuestRpc: GuestRpcResetVsockChannel: channel 3
2024-10-20T11:16:43.064Z In(05) vmx GuestRpc: Closing channel 3 connection 17
2024-10-20T11:16:50.500Z In(05) vmx GuestRpcSendTimedOut: message to toolbox-dnd timed out.
2024-10-20T11:16:50.500Z In(05) vmx TOOLS: appName=toolbox-dnd, oldStatus=1, status=2, guestInitiated=0.
2024-10-20T11:16:52.905Z In(05) vcpu-0 TOOLS: appName=toolbox-dnd, oldStatus=2, status=1, guestInitiated=0.
2024-10-20T11:16:56.560Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:16:57.657Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:16:58.989Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:16:59.500Z In(05) vmx GuestRpc: Got RPCI vsocket connection 18, assigned to channel 3.
2024-10-20T11:16:59.503Z In(05) vmx GuestRpc: Got error for channel 3 connection 18: Remote disconnected
2024-10-20T11:16:59.503Z In(05) vmx GuestRpc: GuestRpcResetVsockChannel: channel 3
2024-10-20T11:16:59.503Z In(05) vmx GuestRpc: Closing channel 3 connection 18
2024-10-20T11:17:01.251Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:17:02.539Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:17:03.087Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:17:03.856Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:17:04.709Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:17:05.551Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:17:06.213Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:17:10.683Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:17:25.877Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:17:44.184Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:17:56.970Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:17:57.245Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:17:57.480Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:17:57.708Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:18:13.193Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:18:15.135Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:18:17.241Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:18:19.797Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:18:21.598Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:18:27.147Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:18:31.236Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:18:33.315Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:18:35.224Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:18:39.609Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:18:45.109Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:18:48.529Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:18:49.490Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:18:50.519Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:18:51.815Z In(05) vmx DISKLIB-LIB   : numIOs = 150000 numMergedIOs = 30184 numSplitIOs = 1058
2024-10-20T11:19:41.380Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:19:48.317Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:19:51.603Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:19:55.316Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:19:59.832Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:20:03.297Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:20:05.521Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:20:07.299Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:20:08.638Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:20:09.826Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:20:15.682Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:20:18.964Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:20:20.523Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:20:21.794Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:20:24.781Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:20:28.412Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:20:31.717Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:20:34.362Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:20:37.015Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:20:38.281Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:20:40.509Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:20:42.250Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:20:45.450Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:20:48.978Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:20:53.559Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:20:57.034Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:20:59.857Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:21:02.699Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:21:06.710Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:21:09.388Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:21:11.448Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:21:12.867Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:21:16.243Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:21:19.675Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:21:23.004Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:21:25.365Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:21:28.032Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:21:31.459Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:21:34.612Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:21:37.588Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:21:39.742Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:21:41.563Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:21:43.254Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:21:44.660Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:21:47.345Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:21:49.447Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:21:51.507Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:21:52.478Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:21:54.743Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:21:56.767Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:21:58.529Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:21:59.845Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:22:01.142Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:22:02.125Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:22:13.776Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:22:17.480Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:22:25.321Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:22:29.715Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:22:33.710Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:22:53.955Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:23:00.623Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:23:05.789Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:23:11.059Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:24:02.098Z In(05) vmx DISKLIB-LIB   : numIOs = 200000 numMergedIOs = 43264 numSplitIOs = 1170
2024-10-20T11:24:14.625Z In(05) PowerNotifyThread PowerNotify: System suspend detected.
2024-10-20T11:24:14.637Z In(05) PowerNotifyThread VMX successfully paused from PowerNotify.
2024-10-20T11:31:34.033Z In(05) PowerNotifyThread Result of IsSystemResumeAutomatic(): FALSE
2024-10-20T11:31:34.044Z In(05) PowerNotifyThread PowerNotify: System resume detected.
2024-10-20T11:31:34.060Z In(05) vmx VNET: MACVNetLinkStateEventHandler: event, up:0, adapter:1
2024-10-20T11:31:34.060Z In(05) vmx VNET: MACVNetLinkStateEventHandler: 'ethernet0' state from 5 to 7.
2024-10-20T11:31:34.060Z In(05) vmx VNET: MACVNetLinkStateEventHandler: event, up:1, adapter:0
2024-10-20T11:31:34.060Z In(05) vmx VNET: MACVNetLinkStateEventHandler: 'ethernet0' state from 7 to 2.
2024-10-20T11:31:34.528Z In(05) vmx VNET: MACVNetLinkStateEventHandler: event, up:0, adapter:1
2024-10-20T11:31:34.528Z In(05) vmx VNET: MACVNetLinkStateEventHandler: 'ethernet0' state from 2 to 0.
2024-10-20T11:31:34.528Z In(05) vmx VNET: MACVNetLinkStateEventHandler: event, up:1, adapter:0
2024-10-20T11:31:34.528Z In(05) vmx VNET: MACVNetLinkStateEventHandler: 'ethernet0' state from 0 to 2.
2024-10-20T11:31:35.527Z In(05) vmx VNET: MACVNetLinkStateEventHandler: event, up:0, adapter:1
2024-10-20T11:31:35.527Z In(05) vmx VNET: MACVNetLinkStateEventHandler: 'ethernet0' state from 2 to 0.
2024-10-20T11:31:35.527Z In(05) vmx VNET: MACVNetLinkStateEventHandler: event, up:1, adapter:0
2024-10-20T11:31:35.527Z In(05) vmx VNET: MACVNetLinkStateEventHandler: 'ethernet0' state from 0 to 2.
2024-10-20T11:31:37.007Z In(05) vmx VNET: MACVNetLinkStateEventHandler: event, up:0, adapter:1
2024-10-20T11:31:37.007Z In(05) vmx VNET: MACVNetLinkStateEventHandler: 'ethernet0' state from 2 to 0.
2024-10-20T11:31:37.007Z In(05) vmx VNET: MACVNetLinkStateEventHandler: event, up:1, adapter:0
2024-10-20T11:31:37.007Z In(05) vmx VNET: MACVNetLinkStateEventHandler: 'ethernet0' state from 0 to 2.
2024-10-20T11:31:39.060Z In(05) vmx GuestRpcSendTimedOut: message to toolbox-dnd timed out.
2024-10-20T11:31:39.060Z In(05) vmx TOOLS: appName=toolbox-dnd, oldStatus=1, status=2, guestInitiated=0.
2024-10-20T11:31:39.181Z In(05) vcpu-1 TOOLS: appName=toolbox-dnd, oldStatus=2, status=1, guestInitiated=0.
2024-10-20T11:31:51.898Z In(05) vmx VNET: MACVNetLinkStateTimerHandler: 'ethernet0' state from 2 to 6.
2024-10-20T11:31:52.017Z In(05) vmx VNET: MACVNetLinkStateTimerHandler: 'ethernet0' state from 6 to 1.
2024-10-20T11:31:59.393Z In(05) vmx VNET: MACVNetLinkStateTimerHandler: 'ethernet0' state from 1 to 5.
2024-10-20T11:32:04.600Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:32:33.526Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:32:33.749Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:32:38.810Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:32:39.562Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:32:41.142Z In(05) vmx GuestRpc: Got RPCI vsocket connection 19, assigned to channel 3.
2024-10-20T11:32:41.143Z In(05) vmx GuestRpc: Got error for channel 3 connection 19: Remote disconnected
2024-10-20T11:32:41.143Z In(05) vmx GuestRpc: GuestRpcResetVsockChannel: channel 3
2024-10-20T11:32:41.143Z In(05) vmx GuestRpc: Closing channel 3 connection 19
2024-10-20T11:32:49.872Z In(05) vmx DISKLIB-LIB   : numIOs = 250000 numMergedIOs = 46615 numSplitIOs = 1284
2024-10-20T11:32:50.593Z In(05) vmx GuestRpc: Got RPCI vsocket connection 20, assigned to channel 3.
2024-10-20T11:32:50.593Z In(05) vmx GuestRpc: Got error for channel 3 connection 20: Remote disconnected
2024-10-20T11:32:50.598Z In(05) vmx GuestRpc: GuestRpcResetVsockChannel: channel 3
2024-10-20T11:32:50.598Z In(05) vmx GuestRpc: Closing channel 3 connection 20
2024-10-20T11:35:31.085Z In(05) vmx MemSched: caller 0 numvm 2 locked pages: num 1433298 max 1570816
2024-10-20T11:35:31.085Z In(05) vmx MemSched: locked Page Limit: host 1661580 config 1587200
2024-10-20T11:35:31.085Z In(05) vmx MemSched: minmempct 50  timestamp 53648
2024-10-20T11:35:31.085Z In(05) vmx MemSched: VM 0 min 301359 max 563503 shares 524288 paged 408241 nonpaged 34917 anonymous 4298 locked 518768 touchedPct 83 dirtiedPct 25 timestamp 53648 vmResponsive is 1
2024-10-20T11:35:31.085Z In(05) vmx MemSched: VM 1 min 1064282 max 2112858 shares 2097152 paged 674445 nonpaged 6245 anonymous 9461 locked 914530 touchedPct 12 dirtiedPct 5 timestamp 53648 vmResponsive is 1
2024-10-20T11:35:31.085Z In(05) vmx MemSched: locked 518768 target 546328 balloon 0 0 104858 swapped 9530 0 allocd 0 512 state 0 100
2024-10-20T11:35:31.085Z In(05) vmx MemSched: states: 0 1711 : 1 108 : 2 0 : 3 0
2024-10-20T11:35:31.085Z In(05) vmx MemSched: Balloon enabled 1 guestType 4 maxSize 0
2024-10-20T11:35:52.176Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:35:56.303Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:36:34.221Z In(05) vmx GuestRpc: Got RPCI vsocket connection 21, assigned to channel 3.
2024-10-20T11:36:34.287Z In(05) vmx GuestRpc: Got error for channel 3 connection 21: Remote disconnected
2024-10-20T11:36:34.287Z In(05) vmx GuestRpc: GuestRpcResetVsockChannel: channel 3
2024-10-20T11:36:34.287Z In(05) vmx GuestRpc: Closing channel 3 connection 21
2024-10-20T11:36:35.823Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:36:52.011Z In(05) vmx GuestRpc: Got RPCI vsocket connection 22, assigned to channel 3.
2024-10-20T11:36:52.028Z In(05) vmx GuestRpc: Got error for channel 3 connection 22: Remote disconnected
2024-10-20T11:36:52.028Z In(05) vmx GuestRpc: GuestRpcResetVsockChannel: channel 3
2024-10-20T11:36:52.028Z In(05) vmx GuestRpc: Closing channel 3 connection 22
2024-10-20T11:37:33.354Z In(05) vmx DISKLIB-LIB   : numIOs = 300000 numMergedIOs = 53183 numSplitIOs = 1537
2024-10-20T11:39:11.965Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:39:15.936Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:39:33.389Z In(05) vmx GuestRpc: Got RPCI vsocket connection 23, assigned to channel 3.
2024-10-20T11:39:33.398Z In(05) vmx GuestRpc: Got error for channel 3 connection 23: Remote disconnected
2024-10-20T11:39:33.398Z In(05) vmx GuestRpc: GuestRpcResetVsockChannel: channel 3
2024-10-20T11:39:33.398Z In(05) vmx GuestRpc: Closing channel 3 connection 23
2024-10-20T11:39:47.800Z In(05) vmx GuestRpc: Got RPCI vsocket connection 24, assigned to channel 3.
2024-10-20T11:39:47.831Z In(05) vmx GuestRpc: Got error for channel 3 connection 24: Remote disconnected
2024-10-20T11:39:47.831Z In(05) vmx GuestRpc: GuestRpcResetVsockChannel: channel 3
2024-10-20T11:39:47.831Z In(05) vmx GuestRpc: Closing channel 3 connection 24
2024-10-20T11:39:53.403Z In(05) vmx GuestRpcSendTimedOut: message to toolbox-dnd timed out.
2024-10-20T11:39:53.403Z In(05) vmx TOOLS: appName=toolbox-dnd, oldStatus=1, status=2, guestInitiated=0.
2024-10-20T11:39:54.810Z In(05) vcpu-1 TOOLS: appName=toolbox-dnd, oldStatus=2, status=1, guestInitiated=0.
2024-10-20T11:40:25.517Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:40:30.336Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:40:40.876Z In(05) vmx GuestRpc: Got RPCI vsocket connection 25, assigned to channel 3.
2024-10-20T11:40:40.883Z In(05) vmx GuestRpc: Got error for channel 3 connection 25: Remote disconnected
2024-10-20T11:40:40.883Z In(05) vmx GuestRpc: GuestRpcResetVsockChannel: channel 3
2024-10-20T11:40:40.883Z In(05) vmx GuestRpc: Closing channel 3 connection 25
2024-10-20T11:42:51.148Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:42:55.869Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:43:10.430Z In(05) vmx GuestRpc: Got RPCI vsocket connection 26, assigned to channel 3.
2024-10-20T11:43:10.436Z In(05) vmx GuestRpc: Got error for channel 3 connection 26: Remote disconnected
2024-10-20T11:43:10.436Z In(05) vmx GuestRpc: GuestRpcResetVsockChannel: channel 3
2024-10-20T11:43:10.436Z In(05) vmx GuestRpc: Closing channel 3 connection 26
2024-10-20T11:44:17.196Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:44:26.218Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:44:38.665Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:44:39.936Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:44:49.823Z In(05) vmx GuestRpc: Got RPCI vsocket connection 27, assigned to channel 3.
2024-10-20T11:44:49.827Z In(05) vmx GuestRpc: Got error for channel 3 connection 27: Remote disconnected
2024-10-20T11:44:49.827Z In(05) vmx GuestRpc: GuestRpcResetVsockChannel: channel 3
2024-10-20T11:44:49.827Z In(05) vmx GuestRpc: Closing channel 3 connection 27
2024-10-20T11:45:30.436Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:45:31.764Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:45:39.397Z In(05) vmx GuestRpc: Got RPCI vsocket connection 28, assigned to channel 3.
2024-10-20T11:45:39.397Z In(05) vmx GuestRpc: Got error for channel 3 connection 28: Remote disconnected
2024-10-20T11:45:39.397Z In(05) vmx GuestRpc: GuestRpcResetVsockChannel: channel 3
2024-10-20T11:45:39.397Z In(05) vmx GuestRpc: Closing channel 3 connection 28
2024-10-20T11:45:57.038Z In(05) vmx GuestRpc: Got RPCI vsocket connection 29, assigned to channel 3.
2024-10-20T11:45:57.089Z In(05) vmx GuestRpc: Got error for channel 3 connection 29: Remote disconnected
2024-10-20T11:45:57.089Z In(05) vmx GuestRpc: GuestRpcResetVsockChannel: channel 3
2024-10-20T11:45:57.089Z In(05) vmx GuestRpc: Closing channel 3 connection 29
2024-10-20T11:46:06.699Z In(05) vmx DISKLIB-LIB   : numIOs = 350000 numMergedIOs = 65008 numSplitIOs = 1729
2024-10-20T11:46:08.243Z In(05) vmx GuestRpcSendTimedOut: message to toolbox-dnd timed out.
2024-10-20T11:46:08.243Z In(05) vmx TOOLS: appName=toolbox-dnd, oldStatus=1, status=2, guestInitiated=0.
2024-10-20T11:46:08.624Z In(05) vcpu-1 TOOLS: appName=toolbox-dnd, oldStatus=2, status=1, guestInitiated=0.
2024-10-20T11:46:42.166Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:46:45.338Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:46:56.309Z In(05) vmx GuestRpc: Got RPCI vsocket connection 30, assigned to channel 3.
2024-10-20T11:46:56.328Z In(05) vmx GuestRpc: Got error for channel 3 connection 30: Remote disconnected
2024-10-20T11:46:56.328Z In(05) vmx GuestRpc: GuestRpcResetVsockChannel: channel 3
2024-10-20T11:46:56.328Z In(05) vmx GuestRpc: Closing channel 3 connection 30
2024-10-20T11:47:17.189Z In(05) vmx GuestRpc: Got RPCI vsocket connection 31, assigned to channel 3.
2024-10-20T11:47:17.193Z In(05) vmx GuestRpc: Got error for channel 3 connection 31: Remote disconnected
2024-10-20T11:47:17.193Z In(05) vmx GuestRpc: GuestRpcResetVsockChannel: channel 3
2024-10-20T11:47:17.193Z In(05) vmx GuestRpc: Closing channel 3 connection 31
2024-10-20T11:47:36.674Z In(05) vmx GuestRpc: Got RPCI vsocket connection 32, assigned to channel 3.
2024-10-20T11:47:36.732Z In(05) vmx GuestRpc: Got error for channel 3 connection 32: Remote disconnected
2024-10-20T11:47:36.732Z In(05) vmx GuestRpc: GuestRpcResetVsockChannel: channel 3
2024-10-20T11:47:36.732Z In(05) vmx GuestRpc: Closing channel 3 connection 32
2024-10-20T11:47:47.480Z In(05) vmx GuestRpcSendTimedOut: message to toolbox-dnd timed out.
2024-10-20T11:47:47.480Z In(05) vmx TOOLS: appName=toolbox-dnd, oldStatus=1, status=2, guestInitiated=0.
2024-10-20T11:47:50.554Z In(05) vmx GuestRpc: Got RPCI vsocket connection 33, assigned to channel 3.
2024-10-20T11:47:50.575Z In(05) vmx GuestRpc: Got error for channel 3 connection 33: Remote disconnected
2024-10-20T11:47:50.575Z In(05) vmx GuestRpc: GuestRpcResetVsockChannel: channel 3
2024-10-20T11:47:50.575Z In(05) vmx GuestRpc: Closing channel 3 connection 33
2024-10-20T11:47:50.575Z In(05) vcpu-1 TOOLS: appName=toolbox-dnd, oldStatus=2, status=1, guestInitiated=0.
2024-10-20T11:47:59.697Z In(05) vmx GuestRpc: Got RPCI vsocket connection 34, assigned to channel 3.
2024-10-20T11:47:59.697Z In(05) vmx GuestRpc: Got error for channel 3 connection 34: Remote disconnected
2024-10-20T11:47:59.703Z In(05) vmx GuestRpc: GuestRpcResetVsockChannel: channel 3
2024-10-20T11:47:59.703Z In(05) vmx GuestRpc: Closing channel 3 connection 34
2024-10-20T11:48:07.483Z In(05) vmx GuestRpcSendTimedOut: message to toolbox-dnd timed out.
2024-10-20T11:48:07.483Z In(05) vmx TOOLS: appName=toolbox-dnd, oldStatus=1, status=2, guestInitiated=0.
2024-10-20T11:48:08.952Z In(05) vcpu-0 TOOLS: appName=toolbox-dnd, oldStatus=2, status=1, guestInitiated=0.
2024-10-20T11:48:15.664Z In(05) vmx GuestRpc: Got RPCI vsocket connection 35, assigned to channel 3.
2024-10-20T11:48:15.668Z In(05) vmx GuestRpc: Got error for channel 3 connection 35: Remote disconnected
2024-10-20T11:48:15.668Z In(05) vmx GuestRpc: GuestRpcResetVsockChannel: channel 3
2024-10-20T11:48:15.668Z In(05) vmx GuestRpc: Closing channel 3 connection 35
2024-10-20T11:49:01.317Z In(05) PowerNotifyThread PowerNotify: System suspend detected.
2024-10-20T11:49:01.443Z In(05) PowerNotifyThread VMX successfully paused from PowerNotify.
2024-10-20T11:49:38.515Z In(05) PowerNotifyThread Result of IsSystemResumeAutomatic(): FALSE
2024-10-20T11:49:38.524Z In(05) PowerNotifyThread PowerNotify: System resume detected.
2024-10-20T11:49:38.765Z In(05) vmx VNET: MACVNetLinkStateEventHandler: event, up:0, adapter:1
2024-10-20T11:49:38.765Z In(05) vmx VNET: MACVNetLinkStateEventHandler: 'ethernet0' state from 5 to 7.
2024-10-20T11:49:38.765Z In(05) vmx VNET: MACVNetLinkStateEventHandler: event, up:1, adapter:0
2024-10-20T11:49:38.765Z In(05) vmx VNET: MACVNetLinkStateEventHandler: 'ethernet0' state from 7 to 2.
2024-10-20T11:49:42.590Z In(05) vmx VNET: MACVNetLinkStateEventHandler: event, up:0, adapter:1
2024-10-20T11:49:42.592Z In(05) vmx VNET: MACVNetLinkStateEventHandler: 'ethernet0' state from 2 to 0.
2024-10-20T11:49:42.592Z In(05) vmx VNET: MACVNetLinkStateEventHandler: event, up:1, adapter:0
2024-10-20T11:49:42.592Z In(05) vmx VNET: MACVNetLinkStateEventHandler: 'ethernet0' state from 0 to 2.
2024-10-20T11:49:56.602Z In(05) vmx VNET: MACVNetLinkStateTimerHandler: 'ethernet0' state from 2 to 6.
2024-10-20T11:49:56.743Z In(05) vmx VNET: MACVNetLinkStateTimerHandler: 'ethernet0' state from 6 to 1.
2024-10-20T11:50:03.297Z In(05) vmx VNET: MACVNetLinkStateTimerHandler: 'ethernet0' state from 1 to 5.
2024-10-20T11:50:10.029Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:50:13.483Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:50:13.976Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:50:20.560Z In(05) vmx GuestRpc: Got RPCI vsocket connection 36, assigned to channel 3.
2024-10-20T11:50:20.565Z In(05) vmx GuestRpc: Got error for channel 3 connection 36: Remote disconnected
2024-10-20T11:50:20.565Z In(05) vmx GuestRpc: GuestRpcResetVsockChannel: channel 3
2024-10-20T11:50:20.565Z In(05) vmx GuestRpc: Closing channel 3 connection 36
2024-10-20T11:50:21.675Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:50:23.456Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:50:29.336Z In(05) vmx GuestRpc: Got RPCI vsocket connection 37, assigned to channel 3.
2024-10-20T11:50:29.337Z In(05) vmx GuestRpc: Got error for channel 3 connection 37: Remote disconnected
2024-10-20T11:50:29.337Z In(05) vmx GuestRpc: GuestRpcResetVsockChannel: channel 3
2024-10-20T11:50:29.337Z In(05) vmx GuestRpc: Closing channel 3 connection 37
2024-10-20T11:50:47.831Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:50:50.755Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:50:51.788Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:50:55.871Z In(05) vmx DISKLIB-LIB   : numIOs = 400000 numMergedIOs = 72552 numSplitIOs = 2073
2024-10-20T11:50:58.837Z In(05) vmx GuestRpc: Got RPCI vsocket connection 38, assigned to channel 3.
2024-10-20T11:50:58.841Z In(05) vmx GuestRpc: Got error for channel 3 connection 38: Remote disconnected
2024-10-20T11:50:58.841Z In(05) vmx GuestRpc: GuestRpcResetVsockChannel: channel 3
2024-10-20T11:50:58.841Z In(05) vmx GuestRpc: Closing channel 3 connection 38
2024-10-20T11:51:03.785Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:51:04.460Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:51:05.599Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:51:06.760Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:51:11.670Z In(05) vmx GuestRpc: Got RPCI vsocket connection 39, assigned to channel 3.
2024-10-20T11:51:11.671Z In(05) vmx GuestRpc: Got error for channel 3 connection 39: Remote disconnected
2024-10-20T11:51:11.671Z In(05) vmx GuestRpc: GuestRpcResetVsockChannel: channel 3
2024-10-20T11:51:11.671Z In(05) vmx GuestRpc: Closing channel 3 connection 39
2024-10-20T11:51:14.468Z In(05) PowerNotifyThread PowerNotify: System suspend detected.
2024-10-20T11:51:14.523Z In(05) PowerNotifyThread VMX successfully paused from PowerNotify.
2024-10-20T11:55:37.101Z In(05) PowerNotifyThread Result of IsSystemResumeAutomatic(): FALSE
2024-10-20T11:55:37.101Z In(05) PowerNotifyThread PowerNotify: System resume detected.
2024-10-20T11:55:37.138Z In(05) vmx GuestRpcSendTimedOut: message to toolbox-dnd timed out.
2024-10-20T11:55:37.138Z Wa(03) vmx TOOLS unity.launchmenu.open failed: failure of the transport layer
2024-10-20T11:55:37.147Z In(05) vmx VNET: MACVNetLinkStateEventHandler: event, up:0, adapter:1
2024-10-20T11:55:37.147Z In(05) vmx VNET: MACVNetLinkStateEventHandler: 'ethernet0' state from 5 to 7.
2024-10-20T11:55:37.147Z In(05) vmx VNET: MACVNetLinkStateEventHandler: event, up:1, adapter:0
2024-10-20T11:55:37.147Z In(05) vmx VNET: MACVNetLinkStateEventHandler: 'ethernet0' state from 7 to 2.
2024-10-20T11:55:39.870Z In(05) vmx VNET: MACVNetLinkStateEventHandler: event, up:0, adapter:1
2024-10-20T11:55:39.871Z In(05) vmx VNET: MACVNetLinkStateEventHandler: 'ethernet0' state from 2 to 0.
2024-10-20T11:55:39.871Z In(05) vmx VNET: MACVNetLinkStateEventHandler: event, up:1, adapter:0
2024-10-20T11:55:39.871Z In(05) vmx VNET: MACVNetLinkStateEventHandler: 'ethernet0' state from 0 to 2.
2024-10-20T11:55:42.141Z In(05) vmx GuestRpcSendTimedOut: message to toolbox-dnd timed out.
2024-10-20T11:55:42.141Z In(05) vmx TOOLS: appName=toolbox-dnd, oldStatus=1, status=2, guestInitiated=0.
2024-10-20T11:55:42.697Z In(05) vmx VNET: MACVNetLinkStateEventHandler: event, up:0, adapter:1
2024-10-20T11:55:42.697Z In(05) vmx VNET: MACVNetLinkStateEventHandler: 'ethernet0' state from 2 to 0.
2024-10-20T11:55:42.697Z In(05) vmx VNET: MACVNetLinkStateEventHandler: event, up:1, adapter:0
2024-10-20T11:55:42.697Z In(05) vmx VNET: MACVNetLinkStateEventHandler: 'ethernet0' state from 0 to 2.
2024-10-20T11:55:45.173Z In(05) vmx VNET: MACVNetLinkStateEventHandler: event, up:0, adapter:1
2024-10-20T11:55:45.174Z In(05) vmx VNET: MACVNetLinkStateEventHandler: 'ethernet0' state from 2 to 0.
2024-10-20T11:55:45.174Z In(05) vmx VNET: MACVNetLinkStateEventHandler: event, up:1, adapter:0
2024-10-20T11:55:45.174Z In(05) vmx VNET: MACVNetLinkStateEventHandler: 'ethernet0' state from 0 to 2.
2024-10-20T11:55:45.949Z In(05) vmx VNET: MACVNetLinkStateEventHandler: event, up:0, adapter:1
2024-10-20T11:55:45.949Z In(05) vmx VNET: MACVNetLinkStateEventHandler: 'ethernet0' state from 2 to 0.
2024-10-20T11:55:45.949Z In(05) vmx VNET: MACVNetLinkStateEventHandler: event, up:1, adapter:0
2024-10-20T11:55:45.959Z In(05) vmx VNET: MACVNetLinkStateEventHandler: 'ethernet0' state from 0 to 2.
2024-10-20T11:55:46.408Z In(05) vmx VNET: MACVNetLinkStateEventHandler: event, up:0, adapter:1
2024-10-20T11:55:46.408Z In(05) vmx VNET: MACVNetLinkStateEventHandler: 'ethernet0' state from 2 to 0.
2024-10-20T11:55:46.408Z In(05) vmx VNET: MACVNetLinkStateEventHandler: event, up:1, adapter:0
2024-10-20T11:55:46.408Z In(05) vmx VNET: MACVNetLinkStateEventHandler: 'ethernet0' state from 0 to 2.
2024-10-20T11:55:49.801Z In(05) vmx VNET: MACVNetLinkStateEventHandler: event, up:0, adapter:1
2024-10-20T11:55:49.801Z In(05) vmx VNET: MACVNetLinkStateEventHandler: 'ethernet0' state from 2 to 0.
2024-10-20T11:55:49.801Z In(05) vmx VNET: MACVNetLinkStateEventHandler: event, up:1, adapter:0
2024-10-20T11:55:49.801Z In(05) vmx VNET: MACVNetLinkStateEventHandler: 'ethernet0' state from 0 to 2.
2024-10-20T11:55:52.018Z In(05) vcpu-0 TOOLS: appName=toolbox-dnd, oldStatus=2, status=1, guestInitiated=0.
2024-10-20T11:55:58.117Z In(05) vmx VNET: MACVNetLinkStateTimerHandler: 'ethernet0' state from 2 to 6.
2024-10-20T11:55:58.639Z In(05) vmx VNET: MACVNetLinkStateTimerHandler: 'ethernet0' state from 6 to 1.
2024-10-20T11:56:05.097Z In(05) vmx VNET: MACVNetLinkStateTimerHandler: 'ethernet0' state from 1 to 5.
2024-10-20T11:56:15.495Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:56:15.884Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:56:23.440Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:56:23.567Z In(05) vmx GuestRpc: Got RPCI vsocket connection 40, assigned to channel 3.
2024-10-20T11:56:23.582Z In(05) vmx GuestRpc: Got error for channel 3 connection 40: Remote disconnected
2024-10-20T11:56:23.583Z In(05) vmx GuestRpc: GuestRpcResetVsockChannel: channel 3
2024-10-20T11:56:23.583Z In(05) vmx GuestRpc: Closing channel 3 connection 40
2024-10-20T11:57:01.263Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:57:03.034Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:57:12.209Z In(05) vmx GuestRpc: Got RPCI vsocket connection 41, assigned to channel 3.
2024-10-20T11:57:12.213Z In(05) vmx GuestRpc: Got error for channel 3 connection 41: Remote disconnected
2024-10-20T11:57:12.213Z In(05) vmx GuestRpc: GuestRpcResetVsockChannel: channel 3
2024-10-20T11:57:12.213Z In(05) vmx GuestRpc: Closing channel 3 connection 41
2024-10-20T11:57:38.273Z In(05) vmx DISKLIB-LIB   : numIOs = 450000 numMergedIOs = 81845 numSplitIOs = 2353
2024-10-20T11:57:38.718Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:57:39.265Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:57:46.769Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:57:46.779Z In(05) vmx GuestRpc: Got RPCI vsocket connection 42, assigned to channel 3.
2024-10-20T11:57:46.785Z In(05) vmx GuestRpc: Got error for channel 3 connection 42: Remote disconnected
2024-10-20T11:57:46.785Z In(05) vmx GuestRpc: GuestRpcResetVsockChannel: channel 3
2024-10-20T11:57:46.785Z In(05) vmx GuestRpc: Closing channel 3 connection 42
2024-10-20T11:57:48.835Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:57:58.121Z In(05) vmx GuestRpc: Got RPCI vsocket connection 43, assigned to channel 3.
2024-10-20T11:57:58.128Z In(05) vmx GuestRpc: Got error for channel 3 connection 43: Remote disconnected
2024-10-20T11:57:58.128Z In(05) vmx GuestRpc: GuestRpcResetVsockChannel: channel 3
2024-10-20T11:57:58.128Z In(05) vmx GuestRpc: Closing channel 3 connection 43
2024-10-20T11:58:09.236Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:58:09.576Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:58:16.074Z In(05) vmx GuestRpc: Got RPCI vsocket connection 44, assigned to channel 3.
2024-10-20T11:58:16.076Z In(05) vmx GuestRpc: Got error for channel 3 connection 44: Remote disconnected
2024-10-20T11:58:16.076Z In(05) vmx GuestRpc: GuestRpcResetVsockChannel: channel 3
2024-10-20T11:58:16.076Z In(05) vmx GuestRpc: Closing channel 3 connection 44
2024-10-20T11:58:24.245Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:58:25.615Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:58:30.301Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:58:32.032Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:58:33.867Z In(05) vmx GuestRpc: Got RPCI vsocket connection 45, assigned to channel 3.
2024-10-20T11:58:33.869Z In(05) vmx GuestRpc: Got error for channel 3 connection 45: Remote disconnected
2024-10-20T11:58:33.870Z In(05) vmx GuestRpc: GuestRpcResetVsockChannel: channel 3
2024-10-20T11:58:33.870Z In(05) vmx GuestRpc: Closing channel 3 connection 45
2024-10-20T11:58:35.851Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:58:37.254Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:58:39.553Z In(05) vmx GuestRpc: Got RPCI vsocket connection 46, assigned to channel 3.
2024-10-20T11:58:39.554Z In(05) vmx GuestRpc: Got error for channel 3 connection 46: Remote disconnected
2024-10-20T11:58:39.554Z In(05) vmx GuestRpc: GuestRpcResetVsockChannel: channel 3
2024-10-20T11:58:39.554Z In(05) vmx GuestRpc: Closing channel 3 connection 46
2024-10-20T11:58:45.022Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:58:45.356Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:58:51.363Z In(05) vmx GuestRpc: Got RPCI vsocket connection 47, assigned to channel 3.
2024-10-20T11:58:51.364Z In(05) vmx GuestRpc: Got error for channel 3 connection 47: Remote disconnected
2024-10-20T11:58:51.364Z In(05) vmx GuestRpc: GuestRpcResetVsockChannel: channel 3
2024-10-20T11:58:51.364Z In(05) vmx GuestRpc: Closing channel 3 connection 47
2024-10-20T11:59:01.122Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:59:06.658Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:59:07.366Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:59:14.283Z In(05) vmx GuestRpc: Got RPCI vsocket connection 48, assigned to channel 3.
2024-10-20T11:59:14.284Z In(05) vmx GuestRpc: Got error for channel 3 connection 48: Remote disconnected
2024-10-20T11:59:14.284Z In(05) vmx GuestRpc: GuestRpcResetVsockChannel: channel 3
2024-10-20T11:59:14.284Z In(05) vmx GuestRpc: Closing channel 3 connection 48
2024-10-20T11:59:26.891Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:59:27.854Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:59:34.632Z In(05) vmx GuestRpc: Got RPCI vsocket connection 49, assigned to channel 3.
2024-10-20T11:59:34.667Z In(05) vmx GuestRpc: Got error for channel 3 connection 49: Remote disconnected
2024-10-20T11:59:34.667Z In(05) vmx GuestRpc: GuestRpcResetVsockChannel: channel 3
2024-10-20T11:59:34.667Z In(05) vmx GuestRpc: Closing channel 3 connection 49
2024-10-20T11:59:50.879Z In(05) vmx Tools: sending 'OS_Suspend' (state = 5) state change request
2024-10-20T11:59:50.880Z In(05) vmx Tools: Changing running status: 1 => 2.
2024-10-20T11:59:50.880Z In(05) vmx Tools: [RunningStatus] Last heartbeat value 2921 (last received 0s ago)
2024-10-20T11:59:50.883Z In(05) vmx Vix: [vmxCommands.c:839]: VMAutomation_SuspendImpl: SoftSuspend succeeded.
2024-10-20T11:59:51.024Z In(05) vcpu-1 Tools: State change '5' progress: last event 0, event 1, success 1.
2024-10-20T11:59:51.123Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=83886080 logical sector size=512
2024-10-20T11:59:51.256Z In(05) vmx DISKLIB-LIB   : numIOs = 500000 numMergedIOs = 88817 numSplitIOs = 2649
2024-10-20T11:59:51.378Z In(05) vmx TOOLS state change 5 returned status 1
2024-10-20T11:59:51.378Z In(05) vmx Tools: State change '5' progress: last event 1, event 2, success 1.
2024-10-20T11:59:51.378Z In(05) vmx SUSPEND: Start suspend (flags=0)
2024-10-20T11:59:51.393Z In(05) svga MKSScreenShotMgr: Taking a screenshot
2024-10-20T11:59:51.418Z In(05) vcpu-0 Closing all the disks of the VM.
2024-10-20T11:59:51.420Z In(05) vcpu-0 Closing disk 'scsi0:0'
2024-10-20T11:59:51.458Z In(05) vcpu-0 Progress -1% (msg.checkpoint.saveStatus)
2024-10-20T11:59:51.459Z In(05) vcpu-0 Checkpointed in VMware Workstation, 17.5.2, build-23775571, Windows Host
2024-10-20T11:59:51.467Z In(05) vcpu-0 Win32U_GetFileAttributes: GetFileAttributesExW("C:\Users\<USER>\Documents\Virtual Machines\windows10\windows10-c5d0bb76.vmem", ...) failed, error: 2
2024-10-20T11:59:51.479Z In(05) vcpu-0 MainMem: Keep paging file 'C:\Users\<USER>\Documents\Virtual Machines\windows10\windows10-c5d0bb76.vmem' as memory image.
2024-10-20T11:59:51.479Z In(05) vcpu-0 Progress 0% (none)
2024-10-20T11:59:51.497Z In(05) vcpu-0 Progress 1% (none)
2024-10-20T11:59:51.501Z In(05) vcpu-0 Progress 2% (none)
2024-10-20T11:59:51.512Z In(05) vcpu-0 Progress 3% (none)
2024-10-20T11:59:51.521Z In(05) vcpu-0 Progress 4% (none)
2024-10-20T11:59:51.527Z In(05) vcpu-0 Progress 5% (none)
2024-10-20T11:59:51.533Z In(05) vcpu-0 Progress 6% (none)
2024-10-20T11:59:51.541Z In(05) vcpu-0 Progress 7% (none)
2024-10-20T11:59:51.546Z In(05) vcpu-0 Progress 8% (none)
2024-10-20T11:59:51.551Z In(05) vcpu-0 Progress 9% (none)
2024-10-20T11:59:51.557Z In(05) vcpu-0 Progress 10% (none)
2024-10-20T11:59:51.562Z In(05) vcpu-0 Progress 11% (none)
2024-10-20T11:59:51.568Z In(05) vcpu-0 Progress 12% (none)
2024-10-20T11:59:51.573Z In(05) vcpu-0 Progress 13% (none)
2024-10-20T11:59:51.582Z In(05) vcpu-0 Progress 14% (none)
2024-10-20T11:59:51.589Z In(05) vcpu-0 Progress 15% (none)
2024-10-20T11:59:51.598Z In(05) vcpu-0 Progress 16% (none)
2024-10-20T11:59:51.606Z In(05) vcpu-0 Progress 17% (none)
2024-10-20T11:59:51.613Z In(05) vcpu-0 Progress 18% (none)
2024-10-20T11:59:51.621Z In(05) vcpu-0 Progress 19% (none)
2024-10-20T11:59:51.630Z In(05) vcpu-0 Progress 20% (none)
2024-10-20T11:59:51.636Z In(05) vcpu-0 Progress 21% (none)
2024-10-20T11:59:51.642Z In(05) vcpu-0 Progress 22% (none)
2024-10-20T11:59:51.652Z In(05) vcpu-0 Progress 23% (none)
2024-10-20T11:59:51.666Z In(05) vcpu-0 Progress 24% (none)
2024-10-20T11:59:51.679Z In(05) vcpu-0 Progress 25% (none)
2024-10-20T11:59:51.690Z In(05) vcpu-0 Progress 26% (none)
2024-10-20T11:59:51.698Z In(05) vcpu-0 Progress 27% (none)
2024-10-20T11:59:51.706Z In(05) vcpu-0 Progress 28% (none)
2024-10-20T11:59:51.715Z In(05) vcpu-0 Progress 29% (none)
2024-10-20T11:59:51.724Z In(05) vcpu-0 Progress 30% (none)
2024-10-20T11:59:51.731Z In(05) vcpu-0 Progress 31% (none)
2024-10-20T11:59:51.740Z In(05) vcpu-0 Progress 32% (none)
2024-10-20T11:59:51.747Z In(05) vcpu-0 Progress 33% (none)
2024-10-20T11:59:51.757Z In(05) vcpu-0 Progress 34% (none)
2024-10-20T11:59:51.793Z In(05) vcpu-0 Progress 35% (none)
2024-10-20T11:59:51.801Z In(05) vcpu-0 Progress 36% (none)
2024-10-20T11:59:51.807Z In(05) vcpu-0 Progress 37% (none)
2024-10-20T11:59:51.835Z In(05) vcpu-0 Progress 38% (none)
2024-10-20T11:59:51.849Z In(05) vcpu-0 Progress 39% (none)
2024-10-20T11:59:51.867Z In(05) vcpu-0 Progress 40% (none)
2024-10-20T11:59:51.878Z In(05) vcpu-0 Progress 41% (none)
2024-10-20T11:59:51.887Z In(05) vcpu-0 Progress 42% (none)
2024-10-20T11:59:51.899Z In(05) vcpu-0 Progress 43% (none)
2024-10-20T11:59:51.912Z In(05) vcpu-0 Progress 44% (none)
2024-10-20T11:59:51.920Z In(05) vcpu-0 Progress 45% (none)
2024-10-20T11:59:51.928Z In(05) vcpu-0 Progress 46% (none)
2024-10-20T11:59:51.938Z In(05) vcpu-0 Progress 47% (none)
2024-10-20T11:59:51.952Z In(05) vcpu-0 Progress 48% (none)
2024-10-20T11:59:51.969Z In(05) vcpu-0 Progress 49% (none)
2024-10-20T11:59:51.977Z In(05) vcpu-0 Progress 50% (none)
2024-10-20T11:59:51.984Z In(05) vcpu-0 Progress 51% (none)
2024-10-20T11:59:51.993Z In(05) vcpu-0 Progress 52% (none)
2024-10-20T11:59:52.012Z In(05) vcpu-0 Progress 53% (none)
2024-10-20T11:59:52.029Z In(05) vcpu-0 Progress 54% (none)
2024-10-20T11:59:52.037Z In(05) vcpu-0 Progress 55% (none)
2024-10-20T11:59:52.045Z In(05) vcpu-0 Progress 56% (none)
2024-10-20T11:59:52.088Z In(05) vcpu-0 Progress 57% (none)
2024-10-20T11:59:52.097Z In(05) vcpu-0 Progress 58% (none)
2024-10-20T11:59:52.104Z In(05) vcpu-0 Progress 59% (none)
2024-10-20T11:59:52.111Z In(05) vcpu-0 Progress 60% (none)
2024-10-20T11:59:52.118Z In(05) vcpu-0 Progress 61% (none)
2024-10-20T11:59:52.126Z In(05) vcpu-0 Progress 62% (none)
2024-10-20T11:59:52.133Z In(05) vcpu-0 Progress 63% (none)
2024-10-20T11:59:52.142Z In(05) vcpu-0 Progress 64% (none)
2024-10-20T11:59:52.150Z In(05) vcpu-0 Progress 65% (none)
2024-10-20T11:59:52.157Z In(05) vcpu-0 Progress 66% (none)
2024-10-20T11:59:52.165Z In(05) vcpu-0 Progress 67% (none)
2024-10-20T11:59:52.172Z In(05) vcpu-0 Progress 68% (none)
2024-10-20T11:59:52.180Z In(05) vcpu-0 Progress 69% (none)
2024-10-20T11:59:52.188Z In(05) vcpu-0 Progress 70% (none)
2024-10-20T11:59:52.197Z In(05) vcpu-0 Progress 71% (none)
2024-10-20T11:59:52.204Z In(05) vcpu-0 Progress 72% (none)
2024-10-20T11:59:52.212Z In(05) vcpu-0 Progress 73% (none)
2024-10-20T11:59:52.219Z In(05) vcpu-0 Progress 74% (none)
2024-10-20T11:59:52.227Z In(05) vcpu-0 Progress 75% (none)
2024-10-20T11:59:52.235Z In(05) vcpu-0 Progress 76% (none)
2024-10-20T11:59:52.243Z In(05) vcpu-0 Progress 77% (none)
2024-10-20T11:59:52.280Z In(05) vcpu-0 Progress 78% (none)
2024-10-20T11:59:52.288Z In(05) vcpu-0 Progress 79% (none)
2024-10-20T11:59:52.296Z In(05) vcpu-0 Progress 80% (none)
2024-10-20T11:59:52.303Z In(05) vcpu-0 Progress 81% (none)
2024-10-20T11:59:52.312Z In(05) vcpu-0 Progress 82% (none)
2024-10-20T11:59:52.319Z In(05) vcpu-0 Progress 83% (none)
2024-10-20T11:59:52.329Z In(05) vcpu-0 Progress 84% (none)
2024-10-20T11:59:52.338Z In(05) vcpu-0 Progress 85% (none)
2024-10-20T11:59:52.345Z In(05) vcpu-0 Progress 86% (none)
2024-10-20T11:59:52.353Z In(05) vcpu-0 Progress 87% (none)
2024-10-20T11:59:52.361Z In(05) vcpu-0 Progress 88% (none)
2024-10-20T11:59:52.368Z In(05) vcpu-0 Progress 89% (none)
2024-10-20T11:59:52.378Z In(05) vcpu-0 Progress 90% (none)
2024-10-20T11:59:52.386Z In(05) vcpu-0 Progress 91% (none)
2024-10-20T11:59:52.395Z In(05) vcpu-0 Progress 92% (none)
2024-10-20T11:59:52.404Z In(05) vcpu-0 Progress 93% (none)
2024-10-20T11:59:52.413Z In(05) vcpu-0 Progress 94% (none)
2024-10-20T11:59:52.421Z In(05) vcpu-0 Progress 95% (none)
2024-10-20T11:59:52.430Z In(05) vcpu-0 Progress 96% (none)
2024-10-20T11:59:52.436Z In(05) vcpu-0 Progress 97% (none)
2024-10-20T11:59:52.444Z In(05) vcpu-0 Progress 98% (none)
2024-10-20T11:59:52.451Z In(05) vcpu-0 Progress 99% (none)
2024-10-20T11:59:52.482Z In(05) vcpu-0 Progress 100% (none)
2024-10-20T11:59:52.485Z No(00) vcpu-0 CheckpointTiming save: memory took 1021297 us
2024-10-20T11:59:52.514Z In(05) vcpu-0 SVGA: Guest reported SVGA driver: (0, 0, 0, 0)
2024-10-20T11:59:52.517Z In(05) vcpu-0 GuestRpc: Reinitializing Channel 0(toolbox)
2024-10-20T11:59:52.518Z In(05) vcpu-0 GuestMsg: Channel 0, Cannot unpost because the previous post is already completed
2024-10-20T11:59:52.518Z In(05) vcpu-0 Tools: [AppStatus] Last heartbeat value 2921 (last received 1s ago)
2024-10-20T11:59:52.518Z In(05) vcpu-0 TOOLS: appName=toolbox, oldStatus=1, status=0, guestInitiated=0.
2024-10-20T11:59:52.525Z In(05) vcpu-0 GuestRpc: Reinitializing Channel 1(toolbox-dnd)
2024-10-20T11:59:52.525Z In(05) vcpu-0 GuestMsg: Channel 1, Cannot unpost because the previous post is already completed
2024-10-20T11:59:52.525Z In(05) vcpu-0 TOOLS: appName=toolbox-dnd, oldStatus=1, status=0, guestInitiated=0.
2024-10-20T11:59:52.528Z In(05) vcpu-0 DEPLOYPKG: ToolsDeployPkgCptSave: state=0 err=0 (null msg)
2024-10-20T11:59:52.528Z In(05) vcpu-0 Progress 101% (none)
2024-10-20T11:59:52.535Z No(00) vcpu-0 ConfigDB: Setting checkpoint.vmState = "windows10-c5d0bb76.vmss"
2024-10-20T11:59:52.611Z In(05) vcpu-0 Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=1, newAppState=1875, success=1 additionalError=0
2024-10-20T11:59:52.611Z In(05) vcpu-0 SUSPEND: Completed suspend: 'Operation completed successfully' (0)
2024-10-20T11:59:52.611Z In(05) vcpu-0 Tools: State change '5' progress: last event 2, event 4, success 1.
2024-10-20T11:59:52.611Z In(05) vcpu-0 Tools: Changing running status: 2 => 1.
2024-10-20T11:59:52.611Z In(05) vcpu-0 Tools: [RunningStatus] Last heartbeat value 2921 (last received 2s ago)
2024-10-20T11:59:52.611Z No(00) vcpu-0 CheckpointTiming save: VMX took 1193076 us
2024-10-20T11:59:52.612Z In(05) vmx Stopping VCPU threads...
2024-10-20T11:59:52.624Z In(05) vmx MKSThread: Requesting MKS exit
2024-10-20T11:59:52.625Z In(05) vmx Stopping MKS/SVGA threads
2024-10-20T11:59:52.627Z In(05) svga SWBScreen: Screen 1 Destroyed: xywh(0, 0, 1714, 874) flags=0x0
2024-10-20T11:59:52.633Z In(05) svga SVGA thread is exiting the main loop
2024-10-20T11:59:52.633Z In(05) vmx MKS/SVGA threads are stopped
2024-10-20T11:59:52.636Z In(05) vmx 
2024-10-20T11:59:52.636Z In(05)+ vmx OvhdMem: Final (Power Off) Overheads
2024-10-20T11:59:52.636Z In(05) vmx                                                       reserved      |          used
2024-10-20T11:59:52.636Z In(05) vmx OvhdMem excluded                                  cur    max    avg |    cur    max    avg
2024-10-20T11:59:52.636Z In(05) vmx OvhdMem OvhdUser_MainMem                    :  524288 524288      - | 513039 520775      -
2024-10-20T11:59:52.636Z In(05) vmx OvhdMem OvhdUser_VmxText                    :    7680   7680      - |      0      0      -
2024-10-20T11:59:52.636Z In(05) vmx OvhdMem OvhdUser_VmxTextLibs                :   17408  17408      - |      0      0      -
2024-10-20T11:59:52.636Z In(05) vmx OvhdMem Total excluded                      :  549376 549376      - |      -      -      -
2024-10-20T11:59:52.636Z In(05) vmx OvhdMem Actual maximum                      :         549376        |             -
2024-10-20T11:59:52.636Z In(05)+ vmx 
2024-10-20T11:59:52.636Z In(05) vmx                                                       reserved      |          used
2024-10-20T11:59:52.636Z In(05) vmx OvhdMem paged                                     cur    max    avg |    cur    max    avg
2024-10-20T11:59:52.636Z In(05) vmx OvhdMem OvhdUser_STATS_vmm                  :       4      4      - |      0      0      -
2024-10-20T11:59:52.636Z In(05) vmx OvhdMem OvhdUser_STATS_device               :       2      2      - |      0      0      -
2024-10-20T11:59:52.636Z In(05) vmx OvhdMem OvhdUser_SvgaMobFallback            :   98304  98304      - |      0      0      -
2024-10-20T11:59:52.636Z In(05) vmx OvhdMem OvhdUser_DiskLibMemUsed             :    3075   3075      - |      0      0      -
2024-10-20T11:59:52.636Z In(05) vmx OvhdMem OvhdUser_SvgaSurfaceTable           :       6      6      - |      1      1      -
2024-10-20T11:59:52.636Z In(05) vmx OvhdMem OvhdUser_SvgaBESurfaceTable         :       4      4      - |      4      4      -
2024-10-20T11:59:52.636Z In(05) vmx OvhdMem OvhdUser_SvgaSDirtyCache            :      96     96      - |      0      0      -
2024-10-20T11:59:52.636Z In(05) vmx OvhdMem OvhdUser_SvgaCursor                 :      10     10      - |     10     10      -
2024-10-20T11:59:52.636Z In(05) vmx OvhdMem OvhdUser_SvgaPPNList                :     130    130      - |      0      0      -
2024-10-20T11:59:52.636Z In(05) vmx OvhdMem OvhdUser_VmxGlobals                 :   10240  10240      - |      0      0      -
2024-10-20T11:59:52.636Z In(05) vmx OvhdMem OvhdUser_VmxGlobalsLibs             :    3584   3584      - |      0      0      -
2024-10-20T11:59:52.636Z In(05) vmx OvhdMem OvhdUser_VmxHeap                    :    8704   8704      - |      0      0      -
2024-10-20T11:59:52.636Z In(05) vmx OvhdMem OvhdUser_VmxMks                     :      33     33      - |      1      1      -
2024-10-20T11:59:52.636Z In(05) vmx OvhdMem OvhdUser_VmxMksRenderOps            :     678    678      - |    492    492      -
2024-10-20T11:59:52.636Z In(05) vmx OvhdMem OvhdUser_VmxMks3d                   :   98304  98304      - |      0      0      -
2024-10-20T11:59:52.636Z In(05) vmx OvhdMem OvhdUser_VmxMksScreenTemp           :   37122  37122      - |      0    187      -
2024-10-20T11:59:52.636Z In(05) vmx OvhdMem OvhdUser_VmxMksVnc                  :   38236  38236      - |      0      0      -
2024-10-20T11:59:52.636Z In(05) vmx OvhdMem OvhdUser_VmxMksScreen               :   65539  65539      - |      0   2026      -
2024-10-20T11:59:52.636Z In(05) vmx OvhdMem OvhdUser_VmxMksSVGAVO               :    4096   4096      - |      0      0      -
2024-10-20T11:59:52.636Z In(05) vmx OvhdMem OvhdUser_VmxMksSwbCursor            :    4096   4096      - |      2      4      -
2024-10-20T11:59:52.636Z In(05) vmx OvhdMem OvhdUser_VmxPhysMemErrPages         :      10     10      - |      0      0      -
2024-10-20T11:59:52.636Z In(05) vmx OvhdMem OvhdUser_VmxSLEntryBuf              :     128    128      - |      0      0      -
2024-10-20T11:59:52.636Z In(05) vmx OvhdMem OvhdUser_VmxThreads                 :   35840  35840      - |      0      0      -
2024-10-20T11:59:52.636Z In(05) vmx OvhdMem Total paged                         :  408241 408241      - |    510   2725      -
2024-10-20T11:59:52.636Z In(05) vmx OvhdMem Actual maximum                      :         408241        |        408241
2024-10-20T11:59:52.636Z In(05)+ vmx 
2024-10-20T11:59:52.636Z In(05) vmx                                                       reserved      |          used
2024-10-20T11:59:52.636Z In(05) vmx OvhdMem nonpaged                                  cur    max    avg |    cur    max    avg
2024-10-20T11:59:52.637Z In(05) vmx OvhdMem OvhdUser_SharedArea                 :     149    149      - |    134    134      -
2024-10-20T11:59:52.637Z In(05) vmx OvhdMem OvhdUser_BusMemTraceBitmap          :      22     22      - |      0      0      -
2024-10-20T11:59:52.637Z In(05) vmx OvhdMem OvhdUser_PFrame                     :    1268   2304      - |   1268   1268      -
2024-10-20T11:59:52.637Z In(05) vmx OvhdMem OvhdUser_VIDE_KSEG                  :      16     16      - |     16     16      -
2024-10-20T11:59:52.637Z In(05) vmx OvhdMem OvhdUser_VGA                        :      64     64      - |     64     64      -
2024-10-20T11:59:52.637Z In(05) vmx OvhdMem OvhdUser_BalloonMPN                 :       1      1      - |      1      1      -
2024-10-20T11:59:52.637Z In(05) vmx OvhdMem OvhdUser_P2MUpdateBuffer            :       3      3      - |      0      0      -
2024-10-20T11:59:52.637Z In(05) vmx OvhdMem OvhdUser_ServicesMPN                :       3      3      - |      2      2      -
2024-10-20T11:59:52.637Z In(05) vmx OvhdMem OvhdUser_LocalApic                  :       2      2      - |      2      2      -
2024-10-20T11:59:52.637Z In(05) vmx OvhdMem OvhdUser_VBIOS                      :       8      8      - |      8      8      -
2024-10-20T11:59:52.637Z In(05) vmx OvhdMem OvhdUser_LSIBIOS                    :       4      4      - |      0      0      -
2024-10-20T11:59:52.637Z In(05) vmx OvhdMem OvhdUser_LSIRings                   :       4      4      - |      0      0      -
2024-10-20T11:59:52.637Z In(05) vmx OvhdMem OvhdUser_SAS1068BIOS                :       4      4      - |      2      2      -
2024-10-20T11:59:52.637Z In(05) vmx OvhdMem OvhdUser_SBIOS                      :      16     16      - |      0      0      -
2024-10-20T11:59:52.637Z In(05) vmx OvhdMem OvhdUser_FlashRam                   :     128    128      - |     58     58      -
2024-10-20T11:59:52.637Z In(05) vmx OvhdMem OvhdUser_SVGAFB                     :   32768  32768      - |    768    768      -
2024-10-20T11:59:52.637Z In(05) vmx OvhdMem OvhdUser_SVGAMEM                    :      64    512      - |      1      1      -
2024-10-20T11:59:52.637Z In(05) vmx OvhdMem OvhdUser_HDAudioReg                 :       3      3      - |      0      0      -
2024-10-20T11:59:52.637Z In(05) vmx OvhdMem OvhdUser_EHCIRegister               :       1      1      - |      0      0      -
2024-10-20T11:59:52.637Z In(05) vmx OvhdMem OvhdUser_XhciRegister               :       1      1      - |      0      0      -
2024-10-20T11:59:52.637Z In(05) vmx OvhdMem OvhdUser_HyperV                     :       2      2      - |      2      2      -
2024-10-20T11:59:52.637Z In(05) vmx OvhdMem OvhdUser_ExtCfg                     :       4      4      - |      2      2      -
2024-10-20T11:59:52.637Z In(05) vmx OvhdMem OvhdUser_vhvCachedVMCS              :       2      2      - |      0      0      -
2024-10-20T11:59:52.637Z In(05) vmx OvhdMem OvhdUser_vhvNestedAPIC              :       2      2      - |      0      0      -
2024-10-20T11:59:52.637Z In(05) vmx OvhdMem OvhdUser_LBR                        :       2      2      - |      0      0      -
2024-10-20T11:59:52.637Z In(05) vmx OvhdMem OvhdUser_MonWired                   :      53     53      - |     53     53      -
2024-10-20T11:59:52.637Z In(05) vmx OvhdMem OvhdUser_MonNuma                    :     252    252      - |      0      0      -
2024-10-20T11:59:52.637Z In(05) vmx OvhdMem OvhdUser_NVDC                       :       1      1      - |      0      0      -
2024-10-20T11:59:52.637Z In(05) vmx OvhdMem OvhdUser_PCIeMMIOArea               :      70     70      - |     35     35      -
2024-10-20T11:59:52.637Z In(05) vmx OvhdMem Total nonpaged                      :   34917  36401      - |   2416   2416      -
2024-10-20T11:59:52.637Z In(05) vmx OvhdMem Actual maximum                      :          34917        |         36401
2024-10-20T11:59:52.637Z In(05)+ vmx 
2024-10-20T11:59:52.637Z In(05) vmx                                                       reserved      |          used
2024-10-20T11:59:52.637Z In(05) vmx OvhdMem anonymous                                 cur    max    avg |    cur    max    avg
2024-10-20T11:59:52.637Z In(05) vmx OvhdMem OvhdMon_Alloc                       :     196    196      - |     98    129      -
2024-10-20T11:59:52.637Z In(05) vmx OvhdMem OvhdMon_BusMemFrame                 :     609    666      - |    609    609      -
2024-10-20T11:59:52.637Z In(05) vmx OvhdMem OvhdMon_BusMem2MInfo                :       8      8      - |      8      8      -
2024-10-20T11:59:52.637Z In(05) vmx OvhdMem OvhdMon_BusMem1GInfo                :       1      1      - |      1      1      -
2024-10-20T11:59:52.637Z In(05) vmx OvhdMem OvhdMon_BusMemZapListMPN            :       1      1      - |      1      1      -
2024-10-20T11:59:52.637Z In(05) vmx OvhdMem OvhdMon_BusMemPreval                :       8      8      - |      0      0      -
2024-10-20T11:59:52.637Z In(05) vmx OvhdMem OvhdMon_MonAS                       :       2      2      - |      1      1      -
2024-10-20T11:59:52.637Z In(05) vmx OvhdMem OvhdMon_GuestMem                    :      80     80      - |     60     60      -
2024-10-20T11:59:52.637Z In(05) vmx OvhdMem OvhdMon_TC                          :    1026   1026      - |    954    954      -
2024-10-20T11:59:52.637Z In(05) vmx OvhdMem OvhdMon_BusMemMonAS                 :       5      5      - |      5      5      -
2024-10-20T11:59:52.637Z In(05) vmx OvhdMem OvhdMon_PlatformMonAS               :       7      7      - |      4      4      -
2024-10-20T11:59:52.637Z In(05) vmx OvhdMem OvhdMon_HVNuma                      :       4      4      - |      0      0      -
2024-10-20T11:59:52.637Z In(05) vmx OvhdMem OvhdMon_HV                          :       2      2      - |      2      2      -
2024-10-20T11:59:52.637Z In(05) vmx OvhdMem OvhdMon_HVMSRBitmap                 :       1      1      - |      0      0      -
2024-10-20T11:59:52.637Z In(05) vmx OvhdMem OvhdMon_VHVGuestMSRBitmap           :       2      2      - |      0      0      -
2024-10-20T11:59:52.638Z In(05) vmx OvhdMem OvhdMon_VHV                         :       6      6      - |      0      0      -
2024-10-20T11:59:52.638Z In(05) vmx OvhdMem OvhdMon_Numa                        :      30     30      - |     14     27      -
2024-10-20T11:59:52.638Z In(05) vmx OvhdMem OvhdMon_NumaTextRodata              :     198    374      - |    176    352      -
2024-10-20T11:59:52.638Z In(05) vmx OvhdMem OvhdMon_NumaDataBss                 :      54     54      - |     52     52      -
2024-10-20T11:59:52.638Z In(05) vmx OvhdMem OvhdMon_NumaLargeData               :       0    512      - |      0      0      -
2024-10-20T11:59:52.640Z In(05) vmx OvhdMem OvhdMon_BaseWired                   :      56     58      - |     46     46      -
2024-10-20T11:59:52.640Z In(05) vmx OvhdMem OvhdMon_Bootstrap                   :       0   2303      - |      0    330      -
2024-10-20T11:59:52.640Z In(05) vmx OvhdMem OvhdMon_GPhysTraced                 :     272    272      - |    263    270      -
2024-10-20T11:59:52.640Z In(05) vmx OvhdMem OvhdMon_GPhysHWMMU                  :    1352   1352      - |   1165   1165      -
2024-10-20T11:59:52.640Z In(05) vmx OvhdMem OvhdMon_GPhysNoTrace                :     266    266      - |     71     71      -
2024-10-20T11:59:52.640Z In(05) vmx OvhdMem OvhdMon_PhysMemGart                 :     104    104      - |     96     96      -
2024-10-20T11:59:52.640Z In(05) vmx OvhdMem OvhdMon_PhysMemErr                  :       7      7      - |      0      0      -
2024-10-20T11:59:52.640Z In(05) vmx OvhdMem OvhdMon_VProbe                      :       1      1      - |      0      0      -
2024-10-20T11:59:52.640Z In(05) vmx OvhdMem Total anonymous                     :    4298   7348      - |   3626   4183      -
2024-10-20T11:59:52.640Z In(05) vmx OvhdMem Actual maximum                      :           4298        |          6660
2024-10-20T11:59:52.640Z In(05)+ vmx 
2024-10-20T11:59:52.640Z In(05) vmx VMMEM: Maximum Reservation: 1765MB (MainMem=2048MB)
2024-10-20T11:59:52.643Z In(05) vmx MemSched: BALLOON HIST [0, 524288]: 990 2917 44 0 0 0 0 0 0 0 0 0
2024-10-20T11:59:52.643Z In(05) vmx MemSched: BALLOON P50 10 P70 10 P90 10 MIN 0 MAX 57608
2024-10-20T11:59:52.643Z In(05) vmx MemSched: SWAP HIST [0, 524288]: 22 2811 147 3 0 0 0 0 0 0 0 0
2024-10-20T11:59:52.643Z In(05) vmx MemSched: SWAP P50 10 P70 10 P90 10 MIN 0 MAX 120195
2024-10-20T11:59:52.643Z In(05) vmx MemSched: LOCK HIST [0, 524288]: 0 7 2 2 3 5 5 55 33 488 2361 51
2024-10-20T11:59:52.643Z In(05) vmx MemSched: LOCK P50 100 P70 100 P90 100 MIN 1610 MAX 526792
2024-10-20T11:59:52.643Z In(05) vmx MemSched: LOCK_TARGET HIST [0, 524288]: 0 0 0 0 0 0 1 0 0 282 2678 959
2024-10-20T11:59:52.643Z In(05) vmx MemSched: LOCK_TARGET P50 100 P70 100 P90 100 MIN 301359 MAX 566901
2024-10-20T11:59:52.643Z In(05) vmx MemSched: ACTIVE_PCT HIST [0, 100]: 0 0 0 0 0 0 268 588 1168 634 303 0
2024-10-20T11:59:52.643Z In(05) vmx MemSched: ACTIVE_PCT P50 80 P70 90 P90 100 MIN 51 MAX 93
2024-10-20T11:59:52.643Z In(05) vmx MemSched: NUM_VMS HIST [0, 10]: 0 0 256 2705 0 0 0 0 0 0 0 0
2024-10-20T11:59:52.643Z In(05) vmx MemSched: NUM_VMS P50 30 P70 30 P90 30 MIN 1 MAX 2
2024-10-20T11:59:52.643Z In(05) vmx MemSched: HOSTLOCK HIST [0, 1579008]: 0 11 13 108 128 2 4 5 3 77 2610 0
2024-10-20T11:59:52.643Z In(05) vmx MemSched: HOSTLOCK P50 100 P70 100 P90 100 MIN 1610 MAX 1503634
2024-10-20T11:59:52.645Z In(05) vmx TOOLS received request in VMX to set option 'enableDnD' -> '0'
2024-10-20T11:59:52.647Z In(05) vmx GuestRpc: Attempt to send TCLO msg for 'toolbox' while GuestRpc is powering off.
2024-10-20T11:59:52.647Z In(05) vmx GuestRpc: Attempt to send TCLO msg for 'toolbox-dnd' while GuestRpc is powering off.
2024-10-20T11:59:52.647Z In(05) vmx TOOLS received request in VMX to set option 'copypaste' -> '0'
2024-10-20T11:59:52.647Z In(05) vmx GuestRpc: Attempt to send TCLO msg for 'toolbox' while GuestRpc is powering off.
2024-10-20T11:59:52.647Z In(05) vmx GuestRpc: Attempt to send TCLO msg for 'toolbox-dnd' while GuestRpc is powering off.
2024-10-20T11:59:52.652Z In(05) vmx HgfsServerManagerVigorExit: Destroy:
2024-10-20T11:59:52.653Z In(05) vmx Tools: ToolsRunningStatus_Exit, delayedRequest is 0x0
2024-10-20T11:59:52.653Z In(05) vmx Tools: Changing running status: 1 => 0.
2024-10-20T11:59:52.653Z In(05) vmx Tools: [RunningStatus] Last heartbeat value 2921 (last received 2s ago)
2024-10-20T11:59:52.654Z In(05) vmx TOOLS hostVerifiedSamlToken capability set to FALSE.
2024-10-20T11:59:52.655Z In(05) vmx GuestRpc: Closing channel 0 connection 5
2024-10-20T11:59:52.655Z Wa(03) vmx VMCI QueuePair: Couldn't inform guest about peer detach event.
2024-10-20T11:59:52.655Z In(05) vmx GuestRpc: Closing channel 1 connection 6
2024-10-20T11:59:52.655Z Wa(03) vmx VMCI QueuePair: Couldn't inform guest about peer detach event.
2024-10-20T11:59:52.656Z In(05) vmx GuestRpc: Closing channel 2 connection 7
2024-10-20T11:59:52.656Z Wa(03) vmx VMCI QueuePair: Couldn't inform guest about peer detach event.
2024-10-20T11:59:52.684Z In(05) vmx SOUNDLIB: Closing Wave sound backend.
2024-10-20T11:59:52.689Z In(05) mks MKSControlMgr: disconnected
2024-10-20T11:59:52.689Z In(05) mks MKS-RenderMain: Stopping MKSBasicOps
2024-10-20T11:59:52.689Z In(05) mks MKS-RenderMain: Stopping MKSBasicOps
2024-10-20T11:59:52.689Z In(05) mks MKS-RenderMain: Stopped MKSBasicOps
2024-10-20T11:59:52.694Z In(05) mks MKS PowerOff
2024-10-20T11:59:52.694Z In(05) mks MKS thread is exiting
2024-10-20T11:59:52.694Z In(05) svga SVGA thread is exiting
2024-10-20T11:59:52.697Z Wa(03) vmx 
2024-10-20T11:59:52.702Z In(05) vmx AIOWIN32C: asyncOps=543398 syncOps=9 bufSize=336Kb fixedOps=245845
2024-10-20T11:59:52.703Z In(05) aioCompletion AIO thread processed 543398 completions
2024-10-20T11:59:52.751Z In(05) deviceThread Device thread is exiting
2024-10-20T11:59:52.752Z In(05) vmx Vix: [mainDispatch.c:1171]: VMAutomationPowerOff: Powering off.
2024-10-20T11:59:52.756Z In(05) vmx Win32U_GetFileAttributes: GetFileAttributesExW("C:\Users\<USER>\Documents\Virtual Machines\windows10\windows10.vmpl", ...) failed, error: 2
2024-10-20T11:59:52.756Z In(05) vmx Policy_SavePolicyFile: invalid arguments to function.
2024-10-20T11:59:52.756Z In(05) vmx PolicyVMX_Exit: Could not write out policies: 15.
2024-10-20T11:59:52.757Z In(05) vmx WORKER: asyncOps=2 maxActiveOps=1 maxPending=1 maxCompleted=1
2024-10-20T11:59:52.762Z In(05) PowerNotifyThread PowerNotify thread exiting.
2024-10-20T11:59:53.153Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=1, newAppState=1875, success=1 additionalError=0
2024-10-20T11:59:53.153Z In(05) vmx Vix: [mainDispatch.c:4230]: VMAutomation: Ignoring ReportPowerOpFinished because the VMX is shutting down.
2024-10-20T11:59:53.158Z In(05) vmx VMXSTATS: Ready to cleanup and munmap 14BE1EB0000.
2024-10-20T11:59:53.159Z No(00) vmx ConfigDB: Setting cleanShutdown = "TRUE"
2024-10-20T11:59:53.169Z In(05) vmx Vix: [mainDispatch.c:4651]: VMAutomationProcessMessage: Abort the command. VMX is shutting down
2024-10-20T11:59:53.169Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=0, newAppState=1870, success=1 additionalError=0
2024-10-20T11:59:53.169Z In(05) vmx Vix: [mainDispatch.c:4230]: VMAutomation: Ignoring ReportPowerOpFinished because the VMX is shutting down.
2024-10-20T11:59:53.171Z In(05) vmx Transitioned vmx/execState/val to suspended
2024-10-20T11:59:53.171Z In(05) vmx VMX idle exit
2024-10-20T11:59:53.172Z In(05) vmx WQPoolFreePoll : pollIx = 3, signalHandle = 2408
2024-10-20T11:59:53.174Z In(05) vmx Vix: [mainDispatch.c:817]: VMAutomation_LateShutdown()
2024-10-20T11:59:53.174Z In(05) vmx Vix: [mainDispatch.c:772]: VMAutomationCloseListenerSocket. Closing listener socket.
2024-10-20T11:59:53.175Z In(05) vmx Flushing VMX VMDB connections
2024-10-20T11:59:53.176Z In(05) vmx VmdbDbRemoveCnx: Removing Cnx from Db for '/db/connection/#1/'
2024-10-20T11:59:53.177Z In(05) vmx VmdbCnxDisconnect: Disconnect: closed pipe for pub cnx '/db/connection/#1/' (0)
2024-10-20T11:59:53.177Z In(05) vmx VigorTransport_ServerDestroy: server destroyed.
2024-10-20T11:59:53.177Z In(05) vmx WQPoolFreePoll : pollIx = 2, signalHandle = 1068
2024-10-20T11:59:53.177Z In(05) vmx WQPoolFreePoll : pollIx = 1, signalHandle = 856
2024-10-20T11:59:53.195Z In(05) vmx VMX exit (0).
2024-10-20T11:59:53.195Z In(05) vmx OBJLIB-LIB: ObjLib cleanup done.
2024-10-20T11:59:53.195Z In(05) vmx AIOMGR-S : stat o=9 r=21 w=64 i=0 br=129160 bw=1312205
