#%% imports
import vedo as vd
import numpy as np
from vedo.pyplot import plot
from vedo import Latex


vd.settings.default_backend= 'vtk'

#%% Callbacks
msg = vd.Text2D(pos='bottom-left', font="VictorMono") # an empty text

def OnMouseMove(evt):                ### called every time mouse moves!
    global Xi

    if evt.object is None:          # mouse hits nothing, return.
        return                       

    pt  = evt.picked3d               # 3d coords of point under mouse
    X   = np.array([pt[0],pt[1],objective([pt[0],pt[1]])])  # X = (x,y,e(x,y))
    Xi = np.append(Xi,[X],axis=0)             # append to the list of points
    if len(Xi) > 1:               # need at least two points to compute a distance
        txt =(
            f"X:  {vd.precision(X,2)}\n"
            f"dX: {vd.precision(Xi[-1,0:2] - Xi[-2,0:2],2)}\n"
            f"dE: {vd.precision(Xi[-1,2] - Xi[-2,2],2)}\n"
        )
        ar = vd.Arrow(Xi[-2,:], Xi[-1,:], s=0.001, c='orange5')
        plt.add(ar) # add the arrow
    else:
        txt = f"X: {vd.precision(X,2)}"
    msg.text(txt)                    # update text message

    c = vd.Cylinder([np.append(Xi[-1,0:2], 0.0), Xi[-1,:]], r=0.01, c='orange5')
    plt.remove("Cylinder")    
    fp = fplt3d[0].flagpole(txt, point=X,s=0.08, c='k', font="Quikhand")
    fp.follow_camera()                 # make it always face the camera
    plt.remove("FlagPole") # remove the old flagpole

    plt.add(fp, c) # add the new flagpole and new cylinder
    plt.render()   # re-render the scene

def OnKeyPress(evt):               ### called every time a key is pressed
    if evt.keypress in ['c', 'C']: # reset Xi and the arrows
        Xi = np.empty((0, 3))
        plt.remove("Arrow").render()

def OnSliderAlpha(widget, event): ### called every time the slider is moved
    val = widget.value         # get the slider value
    fplt3d[0].alpha(val)       # set the alpha (transparency) value of the surface
    fplt3d[1].alpha(val)       # set the alpha (transparency) value of the isolines


#%% Optimization functions
def gradient_fd(func, X, h=0.001): # finite difference gradient
    x, y = X[0], X[1]
    gx = (func([x+h, y]) - func([x-h, y])) / (2*h)
    gy = (func([x, y+h]) - func([x, y-h])) / (2*h)
    return gx, gy

def Hessian_fd(func, X, h=0.001): # finite difference Hessian
    x, y = X[0], X[1]
    gxx = (func([x+h, y]) - 2*func([x, y]) + func([x-h, y])) / h**2
    gyy = (func([x, y+h]) - 2*func([x, y]) + func([x, y-h])) / h**2
    gxy = (func([x+h, y+h]) - func([x+h, y-h]) - func([x-h, y+h]) + func([x-h, y-h])) / (4*h**2)
    H = np.array([[gxx, gxy], [gxy, gyy]])
    return H

def gradient_descent(func, X): # compute gradient step direction
    g = gradient_fd(func, X)
    return -np.array(g)

def Newton(func, X):   # compute Newton step direction
    g = gradient_fd(func, X)
    H = Hessian_fd(func, X)
    d = -np.linalg.solve(H, np.array(g))
    return np.array(d[0],d[1])

def line_search(func, X, d, alpha=1.0): 
    # todo: implement line search
    return alpha

def step(func, X, search_direction_function):
    d = search_direction_function(func, X)
    alpha = line_search(func, X, d)
    return X + d*alpha

def optimize(func, X, search_direction_function, tol=1e-6, iter_max=10):
    for i in range(iter_max):
        X = step(func, X, search_direction_function)
        if np.linalg.norm(gradient_fd(func, X)) < tol:
            break
    return X


#%% Plotting

def objective(X):
    x, y = X[0], X[1]
    return np.sin(2*x*y) * np.cos(3*y)/2+1/2

Xi = np.empty((0, 3))
# test the optimization functions
X = optimize(objective, [0.6, 0.6], Newton, tol=1e-6, iter_max=100)

plt = vd.Plotter(bg2='lightblue')  # Create the plotter
fplt3d = plot(lambda x,y: objective([x,y]), c='terrain')      # create a plot from the function e. fplt3d is a list containing surface mesh, isolines, and axis
fplt2d = fplt3d.clone()            # clone the plot to create a 2D plot


fplt2d[0].lighting('off')          # turn off lighting for the 2D plot
fplt2d[0].vertices[:,2] = 0        # set the z-coordinate of the mesh to 0
fplt2d[1].vertices[:,2] = 0        # set the z-coordinate of the isolines to 0


plt.add_callback('mouse move', OnMouseMove) # add Mouse move callback
plt.add_callback('key press', OnKeyPress) # add Keyboard callback
plt.add_slider(OnSliderAlpha,0.,1.,1., title="Alpha") # add a slider for the alpha value of the surface
plt.show([fplt3d, fplt2d], msg, __doc__, viewup='z')
plt.close()
# %%
