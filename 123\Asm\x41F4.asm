;*********
;*********
.ORIG X41F4

GETNUM:

ST R0, R0_SAVE
ST R1, R1_SAVE
ST R3, R3_SAVE
ST R4, R4_SAVE
ST R5, R5_SAVE
ST R6, R6_SAVE
ST R7, R7_SAVE


MAIN_LOOP:

	;the reason we have 2 loops is because we have to account for the first input
	;the negative sign serves as a negative sign only in the first iteration
	LEA R0, FIRST_MSG
	PUTS

	AND R0, R0, #0
	AND R1, R1, #0
	AND R2, R2, #0
	AND R3, R3, #0
	AND R4, R4, #0
	AND R5, R5, #0
	AND R6, R6, #0
	ADD R1, R1, #10

	GETC
	OUT

	ADD R0, R0, #-10 ;check if the user pressed enter, if so we start over
	BRz MAIN_LOOP

	ADD R0, R0, #10
	ADD R6, R6, R0
	LD R4, NEGATIVE_SIGN
	ADD R6, R6, R4 ;check if the first char is the negative sign
	BRz NEGATIVE_NUMBER

	AND R6, R6, #0
	ADD R6, R6, R0
	BR AFTER_NEGATIVE_CHECK

NEGATIVE_NUMBER:

	ADD R3, R3, #1
	BR LOOP

AFTER_NEGATIVE_CHECK:

	LD R4, MINUS_ASCII ;change the value of R6 to the actual value of the input number
	ADD R6, R6, R4 ;if it's less than 48 then we did not get a number
	BRn FLAG_ILLEGAL1

	ADD R6, R6, #-9
	BRp FLAG_ILLEGAL1
	
	ADD R6, R6, #9 ;add back the 9 we took away
	ADD R2, R2, R6 ;add to the final input number
	BR LOOP

FLAG_ILLEGAL1:

	ADD R5, R5, #1
	BR LOOP

LOOP:

	GETC
	OUT

	ADD R0, R0, #-10 ;check if the user pressed enter, if so we start over
	BRz TEST

	AND R6, R6, #0
	ADD R6, R6, R2
	BRn LOOP

	ADD R0, R0, #10
	AND R6, R6, #0
	ADD R6, R6, R0

	AND R0, R0, #0
	ADD R0, R0, R2

	LD R4, MUL_PTR
	JSRR R4 ;this is here because, if the input number is negative, then we will start multiplying the number by 10 one step later

	NOT R0, R0
	ADD R0, R0, #1
	ADD R0, R0, R2
	BRn OVERFLOW_LOOP

	ADD R5, R5, #0
	BRp LOOP

	LD R4, MINUS_ASCII ;change the value of R6 to the actual value of the input number
	ADD R6, R6, R4 ;if it's less than 48 then we did not get a number
	BRn FLAG_ILLEGAL2

	ADD R6, R6, #-9
	BRp FLAG_ILLEGAL2
	
	ADD R6, R6, #9 ;add back the 9 we took away
	ADD R2, R2, R6 ;add to the final input number
	BRn LOOP

	BR LOOP

FLAG_ILLEGAL2:

	ADD R5, R5, #1
	BR LOOP
	
TEST:

	AND R6, R6, #0
	ADD R6, R6, R2
	ADD R5, R5, #0
	BRp ILLEGAL_MESSAGE
	
	ADD R3, R3, #0
	BRp FLIP

	BRz END_LOOP

ILLEGAL_MESSAGE:

	LEA R0, ILLEGAL_MSG
	PUTS
	BR MAIN_LOOP

OVERFLOW_LOOP:

	GETC
	OUT

	ADD R0, R0, #-10
	BRz OVERFLOW_YES
	
	BR OVERFLOW_LOOP

OVERFLOW_YES

	LEA R0, OVERFLOW_MSG
	PUTS
	BR MAIN_LOOP

FLIP:

	NOT R2, R2
	ADD R2, R2, #1

END_LOOP:

LD R0, R0_SAVE
LD R1, R1_SAVE
LD R3, R3_SAVE
LD R4, R4_SAVE
LD R5, R5_SAVE
LD R6, R6_SAVE
LD R7, R7_SAVE

RET
HALT
MUL_PTR .FILL X4000
R0_SAVE .FILL #0
R1_SAVE .FILL #0
R3_SAVE .FILL #0
R4_SAVE .FILL #0
R5_SAVE .FILL #0
R6_SAVE .FILL #0
R7_SAVE .FILL #0
NEGATIVE_SIGN .FILL #-45 ;ascii value of the negative sign -
TEST_MSG .STRINGZ "WE ARE TESTING FOR LEGITEMACY..."
MINUS_ASCII .FILL #-48 ;ascii value of the number 0
UPPERBOUND .fill #-32767 ;max value of a 16 bit number
LOWERBOUND .fill #-32768 ;min value of a 16 bit number
FIRST_MSG .STRINGZ "Enter a number: "
ILLEGAL_MSG .STRINGZ "Error! You did not enter a number. "
UNDERFLOW_MSG .STRINGZ "Error! Number underflowed! "
OVERFLOW_MSG .STRINGZ "Error! Number overflowed! Please enter again: "

.END