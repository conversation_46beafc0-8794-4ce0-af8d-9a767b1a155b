;214502080
;325812667
.ORIG X4320

PRINT_NUM:

ST R0, R0_SAVE
ST R1, R1_SAVE
ST R2, R2_SAVE
ST R3, R3_SAVE
ST R4, R4_SAVE
ST R5, R5_SAVE
ST R6, R6_SAVE
ST R7, R7_SAVE

	ADD R2, R2, #0
	BRn FLIP

	BR AFTER_FLIP

FLIP:

	LD R0, MINUS_ASCII_VALUE
	OUT

AFTER_FLIP:
	
	AND R1, R1, #0
	ADD R1, R1, #10
	AND R0, R0, #0
	ADD R0, R0, R2
	LD R5, DIV_PTR
	JSRR R5

	ST R3, LAST

	AND R4, R4, #0 
	AND R6, R6, #0
	ADD R6, R6, R2

LOOP:

	AND R0, R0, #0
	ADD R0, R0, R6 
	AND R6, R6, #0
	ADD R6, R6, R4

	LD R5, DIV_PTR
	JSRR R5	

	AND R4, R4, #0
	ADD R4, R4, R6
	ADD R4, R4, R3
	AND R6, R6, #0
	ADD R6, R6, R2
	BRz END_LOOP:

	AND R0, R0, #0
	ADD R0, R0, R4

	LD R5, MUL_PTR
	JSRR R5

	AND R4, R4, #0
	ADD R4, R4, R2
	BR LOOP

END_LOOP:

	ADD R6, R6, R4

PRINT_LOOP:

	AND R0, R0, #0
	ADD R0, R0, R6
	LD R5, DIV_PTR
	JSRR R5
	
	LD R4, ASCII_VALUE
	AND R0, R0, #0
	ADD R0, R0, R3
	ADD R0, R0, R4
	OUT
	
	AND R6, R6, #0
	ADD R6, R6, R2
	BRp PRINT_LOOP

	LD R0, LAST
	LD R3, ASCII_VALUE
	ADD R0, R0, R3
	OUT

LD R0, R0_SAVE
LD R1, R1_SAVE
LD R1, R1_SAVE
LD R2, R2_SAVE
LD R3, R3_SAVE
LD R4, R4_SAVE
LD R5, R5_SAVE
LD R6, R6_SAVE
LD R7, R7_SAVE

RET
HALT
MUL_PTR .FILL X4000
DIV_PTR .FILL X4064
GET_NUM_PTR .FILL X41F4
ASCII_VALUE .FILL #48
MINUS_ASCII_VALUE .FILL #45
LAST .FILL #0
R0_SAVE .FILL #0
R1_SAVE .FILL #0
R2_SAVE .FILL #0
R3_SAVE .FILL #0
R4_SAVE .FILL #0
R5_SAVE .FILL #0
R6_SAVE .FILL #0
R7_SAVE .FILL #0
.END