
# Code report

**Name:** <PERSON><PERSON>  
**ID Number:** 325812667  

---

## Overview
This document provides a detailed report on the implementation of various tasks in the optimization code. The code is designed to demonstrate optimization techniques such as gradient descent and <PERSON>'s method, along with their numerical and analytical derivatives. It also includes GUI-based interaction for visualization and testing.


## Task 1: Understand the Code

### Mouse Right Click Callback
I implemented a right-click mouse callback that randomly changes the surface color from a predefined rainbow palette:

```python
def OnRightButtonClick(evt):
    if evt.picked3d is None:
        return
    new_color = random.choice(rainbow_colors)
    print("New color:", new_color)
    fplt3d[0].c(new_color)
    plt.render()
```

**Effect:** On right-clicking, the surface color dynamically changes, providing visual feedback of the interaction.

---


### Real-Time Plot of Function Values from Mouse Path
Implemented a real-time plot using `vedo.plot` and `clone2d` to visualize the function values along the mouse-generated path (`Xi`):

```python
mouse_values.append(z)
if old_clone_xi is not None:
    plt.remove(old_clone_xi)

curve = plot(
    range(len(mouse_values)), mouse_values,
    title="Mouse Path f-values",
    xtitle="Index",
    ytitle="f(x,y)",
    c='magenta'
)

clone_xi = curve.clone2d(pos=(0.01, 0.7), size=0.35, ontop=True)
old_clone_xi = clone_xi
plt.add(clone_xi)
```

**Effect:** Each mouse movement updates the graph, visually demonstrating function values in real-time.

---

### GUI-based Function Switching
Added a GUI toggle button to switch between two objective functions (sin-cos and polynomial):

```python
def toggle_objective_mode(evt):
    global OBJECTIVE_MODE
    if OBJECTIVE_MODE == "sin-cos":
        OBJECTIVE_MODE = "poly"
    else:
        OBJECTIVE_MODE = "sin-cos"

    redraw_surface()
    plt.render()
```

**Effect:** Clicking the button "T" dynamically switches the objective, updating the visual surface immediately.

---

## Task 2: Gradient Descent

### Disabled Mouse Path Creation
Introduced a toggle (`MOUSE_CREATES_PATH`) to disable the mouse-created path:

```python
MOUSE_CREATES_PATH = False

if not MOUSE_CREATES_PATH:
    return
```

---

### Left Click to Set Starting Point
Implemented a left-click callback to clear previous paths and set the initial point for optimization:

```python
def OnLeftButtonClick(evt):
    if evt.picked3d is None:
        return

    point = evt.picked3d
    x, y = point[0], point[1]
    z = objective(x, y)

    global grad_candidate, newton_candidate, gradient_values, newton_values

    grad_candidate = np.array([x,y,z])
    newton_candidate = np.array([x,y,z])

    gradient_values = [z]
    newton_values = [z]

    plt.remove("Arrow", "Sphere").render()
```

---

### Run Gradient Descent with Adjustable Parameters
Added a button and sliders for gradient descent step size and number of iterations:

```python
def run_multi_steps(evt, state=None):
    global grad_candidate, gradient_values

    for _ in range(NUM_STEPS):
        x, y, _ = grad_candidate
        x, y = gradient_descent_step(x, y, step_size=GD_STEP_SIZE)
        z = objective(x, y)
        grad_candidate = np.array([x, y, z])
        gradient_values.append(z)

        plt.add(vd.Sphere(grad_candidate, r=0.05, c='red'))
        plt.render()
```

**Experiment:** Different step sizes were tested experimentally to find an optimal step size using visual feedback from the plotted graph.

---

## Task 3: Newton’s Method

### Maintain Two Paths
Separate arrays were maintained for gradient descent and Newton’s method paths (`Xi_grad`, `Xi_newton`). These paths allow independent tracking and visualization of each method's progression, aiding in comparative analysis.

### Simultaneous Visualization
Both gradient descent and Newton's method are executed simultaneously to visually compare their convergence properties clearly:
- **Gradient Descent Path:** Red
- **Newton’s Method Path:** Blue

### Dot Product Visualization
At each Newton step, the dot product between the gradient and Newton's direction is calculated to verify descent direction. A positive dot product indicates ascent (undesirable), while a negative dot product indicates descent (desirable):

```python
direction_approx = new_candidate[:2] - old_pt
dot_val = np.dot(grad_n, direction_approx)
dot_values.append(dot_val)
```

The dot product values are plotted separately, providing insight into the correctness and effectiveness of Newton’s method at each iteration.

---

## Task 4: Evaluation

### Line Search
Implemented a backtracking line search using the Armijo rule to dynamically adjust step sizes, ensuring sufficient decrease and improving convergence stability:

```python
def line_search(func, X, d, alpha=1.0):
    c = 1e-4
    grad = gradient_fd(func, X)
    f_val = func(X[0], X[1])

    while func(X[0] + alpha*d[0], X[1] + alpha*d[1]) > f_val + c*alpha*np.dot(grad, d):
        alpha *= 0.5
        if alpha < 1e-7:
            break
    return alpha
```

### Hessian Modification
Implemented two fallback strategies for addressing non-positive definite Hessians encountered during Newton's optimization:
1. **Gradient Descent Fallback:** Newton's direction is replaced with the gradient direction.
2. **Hessian Shift:** Adds a small shift to Hessian diagonal elements until it becomes positive definite, allowing stable Newton steps.

These modifications improve Newton’s method robustness, especially near saddle points or maxima.

### Automatic Stopping Criterion
The convergence criterion uses the gradient norm, stopping iteration when the gradient norm falls below a predefined tolerance (`TOL`) or when reaching a maximum iteration limit (`MAX_ITER`):

```python
def stopping_criterion(x, y, iteration):
    grad_norm = np.linalg.norm(gradient_fd(objective, [x,y]))
    return grad_norm < TOL or iteration >= MAX_ITER
```

This ensures efficient termination once convergence is achieved.

---

## Task 5: Numerical vs Analytical Derivatives

### Analytical Derivatives
Explicit analytical expressions were derived and implemented for both gradient and Hessian of the objective functions. These analytical implementations significantly improve computational speed and accuracy over finite-difference methods.

### Timing Comparison
A detailed performance analysis revealed analytical computation vastly outperforms numerical finite differences:

- Numerical Gradient Time: ~2317 ns
- Analytical Gradient Time: ~21 ns

This demonstrates the computational benefit of deriving analytical gradients.

### Accuracy vs Epsilon (ε)
The finite-difference approximation's accuracy was systematically tested against analytical results across various ε values. Lower ε values provided more accurate results, closely matching analytical gradients, thus highlighting the sensitivity and optimal selection of finite difference parameters:

```python
for eps in [1e-3, 1e-5, 1e-7]:
    numerical_grad = gradient_fd(objective, [0.5, 0.5], h=eps)
    analytical_grad = gradient_analytic(0.5, 0.5)
    diff = np.linalg.norm(numerical_grad - analytical_grad)
    print(f"ε: {eps}, diff: {diff}")
```

---

**Final Notes:**
This project provided hands-on experience with optimization algorithms, numerical methods, and interactive visualization, significantly enhancing intuitive and theoretical understanding of the underlying mathematical concepts.


## 🖼 Screenshot 1: Timing and Derivative Comparison

![Timing and Derivative Comparison](Timing_and_Derivative_Comparison.png)
---

## 🖼 Screenshot 2: Optimization Methods and Finite Difference Analysis

![Optimization Methods](Optimization_Methods.png)
---


## 🌀 GIF 1: Right Click, Transparency Slider & 2D Graph

![Right Button + Alpha + 2D Graph](right_button_alpha_graph.gif)

---

## 🌀 GIF 2: Gradient Descent Behavior

![Gradient Descent](ezgif-381f16cab33233.gif)

---

## 🌀 GIF 3: Newton's Method Behavior

![Newton's Method](ezgif-327be5f29d47d1.gif)


task 1.3


![Task 1.3](task1.3.gif)


![Timing Results](Timing_Results.png)





