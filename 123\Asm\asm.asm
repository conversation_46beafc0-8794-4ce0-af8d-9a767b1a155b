; 214502080
; 325812667
.ORIG X3000
main:
	
	LEA R0, ENTER_NUMBER			; WE LOAD THE ADDRESS OF THE ENTER_NUMBER STRING TO R0
	PUTS							; WE PRINT THE STRING THAT ITS ADDRESS IS SAVED IN R0
	
	LD R4, ARRAY_PRINTS_PTR			; WE LOAD INTO R4 THE value OF ARRAY_PRINTS_PTR THAT HOLDS THE ADDRESS
									; OF THE ARRAY ARRAY_PRINTS OF SIZE 3 ; THAT IN EACH INDEX HOLDS AN ADDRESS
									; TO A STRING 
	
	LEA R5, ENTER_COURSE_1			; WE LOAD THE ADDRESS OF STRING ENTER_COURSE_1 TO R5
	STR R5, R4, #0					; WE STORE THE ADDRESS IN ARRAY_PRINTS[0]
	
	LEA R5, ENTER_COURSE_2			; WE LOAD THE ADDRESS OF STRING ENTER_COURSE_2 TO R5
	STR R5, R4, #1					; WE STORE THE ADDRESS IN ARRAY_PRINTS[1]
	
	LEA R5, ENTER_COURSE_3			; WE LOAD THE ADDRESS OF STRING ENTER_COURSE_3 TO R5
	STR R5, R4, #2					; WE STORE THE ADDRESS IN ARRAY_PRINTS[2]
	
	LD R3, ARRAY_COURSES_PTR		; WE LOAD INTO R3 THE VALUE OF ARRAY_COURSES_PTR THAT HOLDS THE ADDRESS
									; OF THE ARRAY ARRAY_COURSES OF SIZE 6 ; THAT IN THE INDEX THA IS ODD
									; THE INDIX j - 1  IT HOLDS A POINTER TO THE BEGINNING OF THE LINKED LIST ( STUDENT STRUCT)
									; THE INDEX j IT HOLDS THE NUMBER OF STUDENTS IN THIS COURSE ( THE SIZE OF THE LINKED LIST)
									
	LDI R1, COURSE_1_PTR2			; R1 = MEM[MEM[COURSE_1_PTR2]] = MEM[COURSE_1_PTR] = THE ADDRESS OF COURSE_1
	STR R1, R3, #0					; WE STORE R1 IN ARRAY_COURSES[0]
	
	
	LDI R1, COURSE_2_PTR2			; R1 = MEM[MEM[COURSE_2_PTR2]] = MEM[COURSE_2_PTR] = THE ADDRESS OF COURSE_2
	STR R1, R3, #2					; WE STORE R1 IN ARRAY_COURSES[2]
	
	LDI R1, COURSE_3_PTR2			; R1 = MEM[MEM[COURSE_3_PTR2]] = MEM[COURSE_3_PTR] = THE ADDRESS OF COURSE_3
	STR R1, R3, #4					; WE STORE R1 IN ARRAY_COURSES[4]
	
	AND R6, R6, #0					;
	ADD R6, R6, #2					; R6 = 2 ; WE USE R6 AS A COUNTER FOR THE NUMBER OF COURSES WE HAVE
									; R6 = 2 CAUSE WE COUNT TILL R6 < 0
	
	; THIS loop GETS THE NUMBER OF STUDENTS IN EACH COURSE AND STORES THEM IN THE ARRAY ARRAY_COURSES
COURSES_NUMBER:
	LDI R4, GETNUM_PTR2				; R4 = MEM[MEM[GETNUM_PTR2]] = MEM[GETNUM_PTR] = THE ADDRESS OF SUBROUTINE GetNum
	JSRR R4							; WE JUMP TO SUBROUTINE GetNum
	
	STR R5, R3, #1					; MEM[R3+1] = R5 ; STORES THE NUMBER OF STUDENTS IN THE COURSE
									; IN THE ARRAY_COURSES ; IN ITS RIGHT INDIX
	
	ADD R6, R6, #-1					; R6--
	BRn END_COURSES_NUMBER			; IF R6 <= 0 ; WE JUMP TO END_COURSES_NUMBER
	
	ADD R3, R3, #2					; R3 += 2
	BR COURSES_NUMBER				; JUMP TO COURSES_NUMBER
	
END_COURSES_NUMBER:					; WE GET HERE WHEN WE GET ALL THE NUMBER OF STUDENTS IN EACH course
	LD R3, ARRAY_COURSES_PTR		; WE LOAD INTO R3 THE ADDRESS OF THE ARRAY_COURSES ; THE value STORED IN ARRAY_COURSES_PTR
	
	AND R6, R6, #0					;
	ADD R6, R6, #3					; R6 = 3
	
	
	
	; WE BUILD THE LINKED LIST OF EVERY COURSE
BUILD_LINKED_LIST:
	LDR R1, R3, #0					; R1 = [R3 + 0] ; WE LOAD TO R1 THE HEAD OF A LINKED LIST
	LDR R2, R3, #1					; R2 = [R3 + 1] ; WE LOAD TO R2 THE SIZE OF THE LINKED LIST ( NUMBER OF STUDENTS IN EACH COURSE )
	
	ADD R2, R2, #-1					; R2--	
	
	INNER_LOOP_MAIN:
		ADD R2, R2, #-1				; R2--
		BRn END_LIST				; WE check IF R2 < 0 ; IF TRUE THEN WE JUMP TO END_LIST
		
		ADD R4, R1, #7				;  R4 = R1 + 7 ; R4 = THE ADDRESS OF THE NEXT LINKE_LIST
		
		STR R4, R1, #6				; R1.NEXT = R4 ; WE CONNECT THE LISTS
		STR R1, R4, #5				; R4.PREVIOUS = R1 ; WE CONNECT THE LISTS
		
		LDR R1, R1, #6				; R1 = R1.NEXT
		BR INNER_LOOP_MAIN			; JUMP TO INNER_LOOP_MAIN
	
	END_LIST:						;
		ADD R6, R6, #-1				; R6--
		BRnz END_BUILD				; IF R6 <= 0 ; WE JUMP TO END_BUILD
		
		ADD R3, R3, #2				; R3 = R3 + 2
		BR BUILD_LINKED_LIST		; JUMP TO BUILD_LINKED_LIST

END_BUILD: ; END OF BUILD_LINKED_LIST
	LD R3, ARRAY_COURSES_PTR		; WE LOAD INTO R3 THE ADDRESS OF THE ARRAY_COURSES ; THE value STORED IN ARRAY_COURSES_PTR 
	LD R5, ARRAY_PRINTS_PTR			; WE LOAD INTO R5 THE value OF ARRAY_PRINTS_PTR THAT HOLDS THE ADDRESS
									; OF THE ARRAY ARRAY_PRINTS OF SIZE 3 ; THAT IN EACH INDEX HOLDS AN ADDRESS
									; TO A STRING
	
	
	
	; WE GET THE GRADES OF EACH STUDENTS IN EVERY COURSE; WE CALCULATE THE AVERAGE 
	; WE ALSO SORT THE LINKED LIST FROM MAX TO MIN BY AVERAGE ;
	; WE ALSO PUT THE 6 BEST STUDENTS IN THE ARRAY HIGHEST
	AND R6, R6, #0					;
	ADD R6, R6, #6					; R6 = 6 ; WE USE R6 AS A COUNTER FOR THE ARRAY
	
	AND R4, R4, #0					; R4 = 0
	
	LD R0, HIGHEST_SCORE_PTR		; WE LOAD INTO R0 THE VALUE STORED IN HIGHEST_SCORE_PTR WHICH HOLDS THE ADDRESS
									; OF HIGHEST_SCORE ARRAY
	
ZERO_IN_HIGHEST:					; HERE WE MAKE SURE THAT IN EACH INDIX IN THE ARRAY HIGHEST_SCORE
									; THE VALUE 0 IS STORED
	STR R4, R0, #0					; HIGHEST_SCORE[0] = MEM[R0] = R4 = 0
	
	ADD R6, R6, #-1					; R6--
	BRnz END_ZERO_IN				; IF R6 <= 0 JUMP TO END_ZERO_IN
	
	ADD R0, R0, #1					; R0++
	BR ZERO_IN_HIGHEST				; JUMP TO ZERO_IN_HIGHEST
	
END_ZERO_IN:
	AND R6, R6, #0					;
	ADD R6, R6, #3					; R6 = 3 ; COUNTER FOR ARRAY ARRAY_COURSES
	
GET_GRADES:
	LDR R1, R3, #0					; R1 = MEM[R3] = ARRAY_COURSES[0] ; THE HEAD OF THE LINKED LIST
	LDR R2, R3, #1					; R2 = MEM[R3 + 1] = ARRAY_COURSES[1] ; THE SIZE OF THE LINKED LIST
	
	LDR R0, R5, #0					; R0 = MEM[R5] = ARRAY_PRINTS[0] = THE ADDRESS OF STRING ENTER_COURSE_i
	PUTS							; WE PRINT THE STRING THAT IS POINTED TO BY R0
	
	LD R0, ENTER_ASCII_MAIN			; R0 = END OF LIN ASCII VALU = 10
	OUT								; WE PRINT END OF LINE TO THE SCREEN
	
	LD R0, HIGHEST_SCORE_PTR		; WE LOAD TO R0 THE ADDRESS OF array HIGHEST_SCORE WHICH IS STORED IN Label	
									; HIGHEST_SCORE_PTR
	
	LDI R4, GETSTUDENTGRADES_PTR2	; R4 = MEM[MEM[GETNUM_PTR2]] = MEM[GETSTUDENTGRADES_PTR] = THE ADDRESS OF
									; THE SUBROUTINE GetStudentGrades
	JSRR R4							; WE JUMP TO SUBROUTINE GetStudentGrades
	
	LDI R4, AVERAGECALCULATOR_PTR2	; R4 = MEM[MEM[AVERAGECALCULATOR_PTR2]] = MEM[AVERAGECALCULATOR_PTR] 
									; = THE ADDRESS OF THE SUBROUTINE AverageCalculator
	JSRR R4							; WE JUMP TO SUBROUTINE AverageCalculator
	
	LDI R4, BUBBLESORT_PTR3			; R4 = MEM[MEM[BUBBLESORT_PTR3]] = MEM[BUBBLESORT_PTR2] 
									; = THE ADDRESS OF THE LABEL BUBBLESORT_PTR
	LDR R4, R4, #0					; R4 = MEM[R4] = MEM[BUBBLESORT_PTR] = THE ADDRESS OF SUBROUTINE
									; BubbleSort
	JSRR R4							; WE JUMP TO SUBROUTINE BubbleSort
	
	LDI R4, BESTSTUDENT_PTR3		; R4 = MEM[MEM[BESTSTUDENT_PTR3]] = MEM[BESTSTUDENT_PTR2] 
									; = THE ADDRESS OF THE LABEL BESTSTUDENT_PTR
	LDR R4, R4, #0					; R4 = MEM[R4] = MEM[BESTSTUDENT_PTR] = THE ADDRESS OF SUBROUTINE
									; BestStudent
	JSRR R4							; WE JUMP TO SUBROUTINE BestStudent
	
	ADD R6, R6, #-1					; R6--
	BRnz END_GET_GRADES				; IF R6 <= 0 ; WE JUMP TO END_GET_GRADES
	
	ADD R5, R5, #1					; R5++
	ADD R3, R3, #2					; R3 += 2
	BR GET_GRADES					; JUMP TO GET_GRADES
	
END_GET_GRADES:						; END OF THE LOOP GET_GRADES
	LEA R0, PRINT_HIGHEST			; WE LOAD TO R0 THE ADDRESS OF THE STRING PRINT_HIGHEST
	PUTS							; WE PRINT THE STRING
	
	AND R6, R6, #0					;
	ADD R6, R6, #6					; R6 = 6
	
	LD R3, HIGHEST_SCORE_PTR		; R3 = MEM[HIGHEST_SCORE_PTR] = THE ADDRESS OF THE ARRAY HIGHEST_SCORE
	
PRINT_HIGHEST_STUDENTS:				; THIS LOOP PRINTS THE BEST 6 STUDENTS WITH THE HIGHEST GRADES
	LDI R5, PRINTNUM_PTR3			; R5 = MEM[MEM[PRINTNUM_PTR3]] = MEM[PRINTNUM_PTR2] =
									; THE ADDRESS OF THE LABEL PRINTNUM_PTR
	LDR R5, R5, #0					; R5 = MEM[R5] = MEM[PRINTNUM_PTR] = THE ADDRESS OF SUBROUTINE PrintNum
	
	LDR R0, R3, #0					; R0 = MEM[R3] = HIGHEST_SCORE[0]
	JSRR R5							; WE JUMP TO SUBROUTINE PrintNum
	
	ADD R6, R6, #-1					; R6--
	BRnz END_PRINT_HIGHEST			; IF R6 <= 0 ; WE JUMP TO END_PRINT_HIGHEST
	
	LD R0, SPACE_ASCII_MAIN			; R0 = THE ASCII VALUE OF SPACE = 32
	OUT								; PRINT A SPACE TO THE SCREEN
	
	ADD R3, R3, #1					; R3++
	BR PRINT_HIGHEST_STUDENTS		; JUMP TO PRINT_HIGHEST_STUDENTS
	
END_PRINT_HIGHEST:
	
	
HALT
	
	ENTER_ASCII_MAIN		.fill #10						; THE ASCII VALUES OF END LINE
	
	COURSE_1_PTR2			.fill COURSE_1_PTR				; THE ADDRESS OF LABEL COURSE_1_PTR
	COURSE_2_PTR2			.fill COURSE_2_PTR				; THE ADDRESS OF LABEL COURSE_2_PTR
	COURSE_3_PTR2			.fill COURSE_3_PTR				; THE ADDRESS OF LABEL COURSE_3_PTR
	ARRAY_COURSES_PTR		.fill ARRAY_COURSES				; THE ADDRESS OF ARRAY_COURSES
	ARRAY_PRINTS_PTR		.fill ARRAY_PRINTS				; THE ADDRESS OF ARRAY_PRINTS
	HIGHEST_SCORE_PTR		.fill HIGHEST_SCORE				; THE ADDRESS OF HIGHEST_SCORE
	
	; WE DO THAT BECAUSE OF THE PCOFFSET
	GETSTUDENTGRADES_PTR2	.fill GETSTUDENTGRADES_PTR		; THE ADDRESS OF SUBROUTINE GetStudentGrades
	AVERAGECALCULATOR_PTR2  .fill AVERAGECALCULATOR_PTR 	; THE ADDRESS OF SUBROUTINE AverageCalculator 
	BUBBLESORT_PTR3			.fill BUBBLESORT_PTR2			; THE ADDRESS OF SUBROUTINE BubbleSort
	BESTSTUDENT_PTR3		.fill BESTSTUDENT_PTR2			; THE ADDRESS OF SUBROUTINE BestStudent
	GETNUM_PTR2				.fill GETNUM_PTR				; THE ADDRESS OF SUBROUTINE GetNum
	PRINTNUM_PTR3			.fill PRINTNUM_PTR2				; THE ADDRESS OF SUBROUTINE PrintNum
	
	ENTER_NUMBER			.stringz "Enter the number of students in each course: "
	ENTER_COURSE_1			.stringz "Enter the student grades in course 1: "
	ENTER_COURSE_2			.stringz "Enter the student grades in course 2: "
	ENTER_COURSE_3			.stringz "Enter the student grades in course 3: "
	PRINT_HIGHEST			.stringz "The six highest scores are: "	
	
	SPACE_ASCII_MAIN		.fill #32					; THE ASCII VALUE OF SPACE
	
	ARRAY_PRINTS			.blkw #3 					; an array of size 6 ; its purpose is to hold and ADDRESS to the stringz we want to print
	ARRAY_COURSES			.blkw #6 #0					; an array of size 6 that holds 0 in every index
	HIGHEST_SCORE			.blkw #6 #0					; an array of size 6 that holds 0 in every index ; it holds the best 6 students
	
	COURSE_1_PTR			.fill COURSE_1				; THE ADDRESS OF COURSE_1
	COURSE_2_PTR			.fill COURSE_2				; THE ADDRESS OF COURSE_2
	COURSE_3_PTR			.fill COURSE_3				; THE ADDRESS OF COURSE_3
	
	COURSE_1				.blkw #70 #0				; WE MAKE SPACE FOR THE FIRST COURSE LINKED LIST
	COURSE_2				.blkw #70 #0				; WE MAKE SPACE FOR THE SECOND COURSE LINKED LIST
	COURSE_3				.blkw #70 #0				; WE MAKE SPACE FOR THE THIRD COURSE LINKED LIST
	
		
	GETSTUDENTGRADES_PTR	.fill GetStudentGrades		; THE ADDRESS OF SUBROUTINE GetStudentGrades
	AVERAGECALCULATOR_PTR   .fill AverageCalculator 	; THE ADDRESS OF SUBROUTINE AverageCalculator 
	BUBBLESORT_PTR2			.fill BUBBLESORT_PTR		; THE ADDRESS OF SUBROUTINE BubbleSort
	BESTSTUDENT_PTR2		.fill BESTSTUDENT_PTR		; THE ADDRESS OF SUBROUTINE BestStudent
	GETNUM_PTR				.fill GetNum				; THE ADDRESS OF SUBROUTINE GetNum
	PRINTNUM_PTR2			.fill PRINTNUM_PTR			; THE ADDRESS OF SUBROUTINE PrintNum
	
; ------------------------------------------------------------ ;
; ------------------------ QUESTION 1 ------------------------ ;
; --------------------- GetStudentGrades --------------------- ;
; ------------------------------------------------------------ ;
	
GetStudentGrades:
	; save the value of registers ;
	ST R0, R0_SAVE_GetStudentGrades
	ST R1, R1_SAVE_GetStudentGrades
	ST R2, R2_SAVE_GetStudentGrades
	ST R3, R3_SAVE_GetStudentGrades
	ST R4, R4_SAVE_GetStudentGrades
	ST R5, R5_SAVE_GetStudentGrades
	ST R6, R6_SAVE_GetStudentGrades
	ST R7, R7_SAVE_GetStudentGrades
	; ------------------------- ;
	
	AND R6, R6, 0		;
	ADD R6, R6, 3		; R6 = 3
	
LOOP_GET:
	LEA R4, GetNum		; LOAD THE ADDRESS OF SUBROUTINE GETNUM TO R4
	JSRR R4				; JUMP TO SUBROUTINE USING JSRR 
	
	STR R5, R1, #0		; MEM[R1] = R5
	ADD R6, R6, #-1		; R6--
	BRn NEXT_STUDENT	; IF R6 < 0 THEN JUMP TO NEXT_STUDENT 
	
	ADD R1, R1, #1		; R1++
	
	BR LOOP_GET			; JUMP TO LOOP_GET

NEXT_STUDENT:
	ADD R2, R2, #-1		; R2--
	BRz END_GET			; IF R2 == 0 WE JUMP TO END_GET
	
	AND R6, R6, 0		;
	ADD R6, R6, 3		; R6 = 3
	
	LDR R1, R1, #3		; R1 = R1->NEXT
	BR LOOP_GET			; WE JUMP TO GET_NUM
	
END_GET:
	
	
	; ------------------------- ;
	; load the value of registers ;
	LD R0, R0_SAVE_GetStudentGrades
	LD R1, R1_SAVE_GetStudentGrades
	LD R2, R2_SAVE_GetStudentGrades
	LD R3, R3_SAVE_GetStudentGrades
	LD R4, R4_SAVE_GetStudentGrades
	LD R5, R5_SAVE_GetStudentGrades
	LD R6, R6_SAVE_GetStudentGrades
	LD R7, R7_SAVE_GetStudentGrades
RET
	
; LABELS FOR GetStudentGrades ;
	R0_SAVE_GetStudentGrades	.fill #0
	R1_SAVE_GetStudentGrades	.fill #0
	R2_SAVE_GetStudentGrades	.fill #0
	R3_SAVE_GetStudentGrades	.fill #0
	R4_SAVE_GetStudentGrades	.fill #0
	R5_SAVE_GetStudentGrades	.fill #0
	R6_SAVE_GetStudentGrades	.fill #0
	R7_SAVE_GetStudentGrades	.fill #0
	
	
; ENDS HERE ;
; ------------------------------------------------------------ ;
; -------------------------- GETNUM -------------------------- ;
; ------------------------------------------------------------ ;
	
; THIS SUBROUTINE GETS A NUMBER FROM THE USER IN ASCII 
; CONVERTS IT TO DECIMAL AND STORES THE RETURN VALUE IN R5
; IN R6 IS STORED THE COUNTER FOR THE LOOPS IN WHICH WE CALL THE GetNum
GetNum:
	; save the value of registers ;
	ST R0, R0_SAVE_GETNUM
	ST R1, R1_SAVE_GETNUM
	ST R2, R2_SAVE_GETNUM
	ST R3, R3_SAVE_GETNUM
	ST R4, R4_SAVE_GETNUM
	ST R6, R6_SAVE_GETNUM
	ST R7, R7_SAVE_GETNUM
	; ------------------------- ;
	
	AND R5, R5, #0				; R5 = 0
	
GET_NUM:

	GETC						; read a single character from user
	OUT							; write a single character to display
	
	LD R3, SPACE_ASCII_GETNUM	; R3 = -32
	ADD R4, R0, R3				; WE CHECK IF R0 == R3
	BRz GET_NUM					; IF TRUE THEN WE GOT A SPACE SO WE JUMP TO GET_NUM
	
	LD R3, ENTER_GETNUM			; R3 = -10
	ADD R4, R0, R3 				; WE CHECK IF R0 == R3, IF TRUE THEN WE GOT END OF LINE
	BRz GETNUM_RESULT			; IF TRUE WE JUMP TO END_LINE
	
	LD R3, NUMBER_0_GETNUM		; R3 = -48
	ADD R4, R0, R3				; CAUSE WE KNOW THAT THE INPUT IS CORRECT ; R4 = R0 - 48

GOT_A_NUM:
	
	ADD R3, R5, R5				; R3 = 2*R5
	ADD R3, R3, R3				; R3 = 4*R5
	ADD R3, R3, R3				; R3 = 8*R5
	ADD R5, R5, R5				; R5 = 2*R5
	ADD R5, R5, R3				; R5 = 10*R5
	
	ADD R5, R5, R4				; R5 = R5 + R4 
	
	GETC						; read a single character from user
	OUT							; write a single character to display
	
	LD R3, NUMBER_0_GETNUM		; R3 = -48
	ADD R4, R0, R3				; R4 = R0 - R3
	BRzp GOT_A_NUM				; IF R4 >= 0 THEN WE GOT A NUMBER, SO WE JUMP TO GOT_A_NUM
	
	ADD R2, R6, #-1				; R2 = R6 - 1
	BRzp GETNUM_RESULT			; IF R2 >= 0 ; JUMP TP GETNUM_RESULT
	
	LD R3, SPACE_ASCII_GETNUM	; R3 = -32
	ADD R4, R0, R3				; R4 = R0 + R3 = R0 -32
	BRzp GET_NUM				; IF R4 >= 0 ; JUMP TO GET_NUM
	
GETNUM_RESULT:
	
	; ------------------------- ;
	; load the value of registers ;
	LD R0, R0_SAVE_GETNUM
	LD R1, R1_SAVE_GETNUM
	LD R2, R2_SAVE_GETNUM
	LD R3, R3_SAVE_GETNUM
	LD R4, R4_SAVE_GETNUM
	LD R6, R6_SAVE_GETNUM
	LD R7, R7_SAVE_GETNUM
RET
; LABELS FOR GetNum ;
	R0_SAVE_GETNUM			.fill #0
	R1_SAVE_GETNUM			.fill #0
	R2_SAVE_GETNUM			.fill #0
	R3_SAVE_GETNUM			.fill #0
	R4_SAVE_GETNUM			.fill #0
	R6_SAVE_GETNUM			.fill #0
	R7_SAVE_GETNUM			.fill #0

	NUMBER_0_GETNUM			.fill #-48	; the negative ascii value of '0'
	SPACE_ASCII_GETNUM		.fill #-32
	ENTER_GETNUM			.fill #-10	; the negative ascii value of 'enter'
	
		
; ENDS HERE ;
; ------------------------------------------------------------ ;
; ------------------------ QUESTION 2 ------------------------ ;
; -------------------- AverageCalculator --------------------- ;
; ------------------------------------------------------------ ;

; WE STORE THE ADDRESS OF BestStudent AND BubbleSort BECAUSE OF PC OFFSET SO WE DONT HAVE ANY PROBLEM JUMPING ;
	BESTSTUDENT_PTR 	.fill BestStudent
	BUBBLESORT_PTR		.fill BubbleSort

AverageCalculator:
	; save the value of registers ;
	ST R0, R0_SAVE_AverageCalculator
	ST R1, R1_SAVE_AverageCalculator
	ST R2, R2_SAVE_AverageCalculator
	ST R3, R3_SAVE_AverageCalculator
	ST R4, R4_SAVE_AverageCalculator
	ST R5, R5_SAVE_AverageCalculator
	ST R6, R6_SAVE_AverageCalculator
	ST R7, R7_SAVE_AverageCalculator
	; ------------------------- ;
	AND R5, R5, #0			; R5 = 0
	
CALCULATE_AVERAGE:
	AND R3, R3, #0			; R3 = 0
	
	LDR R6, R1, #0			; R6 = R1->HW0
	ADD R3, R6, R3			; R3 = R6 + R3
	
	LDR R6, R1, #1			; R6 = R1->HW1
	ADD R3, R6, R3			; R3 = R6 + R3
	
	LDR R6, R1, #2			; R6 = R1->HW2
	ADD R3, R6, R3			; R3 = R6 + R3
	
	LDR R6, R1, #3			; R6 = R1->MIDDLE_TEST
	ADD R3, R6, R3			; R3 = R6 + R3
	
DIVIDE:  					; Label for the DIVIDE loop 
							; Check if R3 - 4 is negative
    ADD R4, R3, #-4
    BRn DONE    			; If result is negative, we are done
							
    ADD R3, R3, #-4			; R3 = R3 - 4
    ADD R5, R5, #1			; R5++
    BRnzp DIVIDE 			; Loop back to try subtracting again
	
DONE:
	
	STR R5, R1, #4			; R1->AVERAGE = R5
	
	AND R5, R5, #0			; R5 = 0
	
	ADD R2, R2, #-1			; R2--
	BRz END_CAL				; IF R2 == 0 ; THEN WE GOT TO THE END OF THE LINKED LIST
	
	LDR R1, R1, #6			; R1 = R1->NEXT
	BR CALCULATE_AVERAGE	; JUMP TO CALCULATE_AVERAGE
	
END_CAL:

	; ------------------------- ;
	; load the value of registers ;
	LD R0, R0_SAVE_AverageCalculator
	LD R1, R1_SAVE_AverageCalculator
	LD R2, R2_SAVE_AverageCalculator
	LD R3, R3_SAVE_AverageCalculator
	LD R4, R4_SAVE_AverageCalculator
	LD R5, R5_SAVE_AverageCalculator
	LD R6, R6_SAVE_AverageCalculator
	LD R7, R7_SAVE_AverageCalculator
RET
; LABELS FOR AverageCalculator ;
	R0_SAVE_AverageCalculator	.fill #0
	R1_SAVE_AverageCalculator	.fill #0
	R2_SAVE_AverageCalculator	.fill #0
	R3_SAVE_AverageCalculator	.fill #0
	R4_SAVE_AverageCalculator	.fill #0
	R5_SAVE_AverageCalculator	.fill #0
	R6_SAVE_AverageCalculator	.fill #0
	R7_SAVE_AverageCalculator	.fill #0
;ENDS HERE;


; ------------------------------------------------------------ ;
; ------------------------ QUESTION 3 ------------------------ ;
; ----------------------- BubbleSort ------------------------- ;
; ------------------------------------------------------------ ;

; HERE WE WRITE LABELS THAT STORES THE ADDRESS OF THE SUBROUTINE TO SOLVE THE PCOFFSET PROBLEM ;
	PRINTNUM_PTR 	.fill PrintNum
	

BubbleSort:
	ST R0, R0_SAVE_BubbleSort	; store the values of R0-R7 , excluding R2
	ST R1, R1_SAVE_BubbleSort
	ST R2, R2_SAVE_BubbleSort
	ST R3, R3_SAVE_BubbleSort
	ST R4, R4_SAVE_BubbleSort
	ST R5, R5_SAVE_BubbleSort
	ST R6, R6_SAVE_BubbleSort
	ST R7, R7_SAVE_BubbleSort
	
;--------------------------------------;
	ADD R2, R2, #-1 			; R2--
	
	AND R6, R6, #0				; R6 = 0 ; WE USE R6 AS FLAG FOR SWAPS, IF R6 = 0 THEN THERES NO SWAPS OCCURED
	
BUBBLE_LOOP:
	LDR R3, R1, #6				; R3 = R1 ->NEXT
	
	LDR R5, R3, #4				; R5 = MEM[R3 + 4] = R3.AVERAGE
	
	NOT R5, R5					;
	ADD R5, R5, #1				; R5 = -R5
	
	LDR R4, R1, #4				; R4 = MEM[R1 + 4] = R1.AVERAGE
	
	ADD R5, R5, R4				; IF R4 - R5 < 0 THEN WE NEED TO SWAP	
	BRn BUBBLE_SWAP				; IF TRUE WE JUMP TO BUBBLE_SWAP
	
RET_SWAP:					
	ADD R2, R2, #-1				; R2--
	BRz CHECK_FLAG				; WE CHECK IF R2 == 0 ; THEN WE GOT TO THE NED OF THE LINKED LIST ; IF TRUE WE JUMP TO CHECK_FLAG ; WE CHECK THE FLAG R6
	
NEXT_CHECK:
	
	LDR R1, R1, #6				; R1 = R1->NEXT
	BR BUBBLE_LOOP				; JUMP TO BUBBLE_LOOP
	
BUBBLE_SWAP:					; IN THIS LABEL WE SWAP THE VALUES BETWEEN R1 AND R3
	LDR R4, R1, #4				; R4 = R1->AVERAGE
	LDR R5, R3, #4				; R5 = R3->AVERAGE
	
	STR R4, R3, #4				; R1->AVERAGE = R5
	STR R5, R1, #4				; R3->AVERAGE = R4
	
	LDR R4, R1, #3				; R4 = R1->MIDDLE_TEST
	LDR R5, R3, #3				; R5 = R3->MIDDLE_TEST
	
	STR R4, R3, #3				; R3->MIDDLE_TEST = R4
	STR R5, R1, #3				; R1->MIDDLE_TEST = R5
		
	LDR R4, R1, #2				; R4 = R1->HW2
	LDR R5, R3, #2				; R5 = R3->HW2
	
	STR R4, R3, #2				; R3->HW2 = R4
	STR R5, R1, #2				; R1->HW2 = R5
	
	LDR R4, R1, #1				; R4 = R1->HW1
	LDR R5, R3, #1				; R5 = R3->HW1
	
	STR R4, R3, #1				; R3->HW1 = R4
	STR R5, R1, #1				; R1->HW1 = R5
	
	LDR R4, R1, #0				; R4 = R1->HW0
	LDR R5, R3, #0				; R5 = R3->HW0
	
	STR R4, R3, #0				; R3->HW0 = R4
	STR R5, R1, #0				; R1->HW0 = R5
	
	ADD R6, R6, #1				; R6++
	
	BR RET_SWAP					; JUMP TO RET_SWAP

CHECK_FLAG:						; IN THIS LABEL WE CHEKC IF THE FLAG R6 == 0 OR NOT
	ADD R6, R6, #-1				; WE CHECK IF R6 == 0 ; (R6 - 1 ) < 0
	BRn END_BUBBLE				; IF TRUE THEN WE DIDNT DO ANY SWAP SO WE EXIT THE loop
	
	LD R1, R1_SAVE_BubbleSort	; LOAD THE ORIGINAL VALUE OF R1 ; THE HEAD OF THE LINKED LIST
	LD R2, R2_SAVE_BubbleSort	; LOAD THE ORIGINAL VALUE OF R2 ; THE SIZE OF THE LINKED LIST
	
	ADD R2, R2, #-1				; R2--
	
	AND R6, R6, #0				; R6 = 0
	
	BR BUBBLE_LOOP				; CONTINUE THE BUBBLE LOOP

END_BUBBLE:
	
	
;--------------------------------------;
	LD R0, R0_SAVE_BubbleSort	; load the original values of R0-R7 , excluding R2
	LD R1, R1_SAVE_BubbleSort
	LD R2, R2_SAVE_BubbleSort
	LD R3, R3_SAVE_BubbleSort
	LD R4, R4_SAVE_BubbleSort
	LD R5, R5_SAVE_BubbleSort
	LD R6, R6_SAVE_BubbleSort
	LD R7, R7_SAVE_BubbleSort
RET
; LABELS FO BUBBLE SORT ;
	R0_SAVE_BubbleSort 				.fill #0
	R1_SAVE_BubbleSort 				.fill #0
	R2_SAVE_BubbleSort 				.fill #0
	R3_SAVE_BubbleSort				.fill #0
	R4_SAVE_BubbleSort 				.fill #0
	R5_SAVE_BubbleSort 				.fill #0
	R6_SAVE_BubbleSort 				.fill #0
	R7_SAVE_BubbleSort 				.fill #0
; ENDS HERE ;

; ------------------------------------------------------------ ;
; ------------------------ QUESTION 4 ------------------------ ;
; ----------------------- BestStudent ------------------------ ;
; ------------------------------------------------------------ ;



BestStudent:
	; save the value of registers ;
	ST R0, R0_SAVE_BestStudent
	ST R1, R1_SAVE_BestStudent
	ST R2, R2_SAVE_BestStudent
	ST R3, R3_SAVE_BestStudent
	ST R4, R4_SAVE_BestStudent
	ST R5, R5_SAVE_BestStudent 
	ST R6, R6_SAVE_BestStudent
	ST R7, R7_SAVE_BestStudent
	; ------------------------- ;
	
	ADD R2, R2, #-1				; we check the size of the linked list
	BRn END_BEST
	
	AND R6, R6, #0				; R6 = 6 ; the size of the array
	ADD R6, R6, #6				;
	
LOOP_BEST:
	
	LDR R3, R0, #0				; R3 = MEM[R0] 
	
	NOT R3, R3					;
	ADD R3, R3, #1				; R3 = -R3
	
	LDR R4, R1, #4				; R4 = MEM[R1 + 4] ; R4 = AVERAGE OF THE STUDENT
	
	ADD R3, R3, R4				; R3 = R4 - R3 ; WE Check IF R4 > R3
	BRp FOUND					; IF ITS TRUE THEN WE NEED TO INSERT R4 IN THE ARRAY SO WE JUMP TO Label FOUND
	BRz NEXT_LINKED				; IF R4 == R3 , WE JUMP TO NEXT_LINKED AND CONTINUE CHECKING
	
	ADD R6, R6, #-1				; R6--
	BRnz END_BEST				; IF R6 <= 0, WE JUMP TO END_BEST, THERES NO MORE COMPARISON 
	
	ADD R0, R0, #1				; R1++ , WE MOVE ON THE ARRAY, WE KNOW WE CAN DO THAT CAUSE WE CHECKED R6, THE FLAG FOR SIZE OF array
	
	BR LOOP_BEST				; CONTINUE THE loop
	
NEXT_LINKED:
				
	ADD R2, R2, #-1				; R2-- ;
	BRn END_BEST				; WE CHECK IF WE GOT TO THE END OF THE LINKED LIST
	
	LDR R1, R1, #6				; R1 = R1->NEXT ;
	
	LD R0, R0_SAVE_BestStudent	; LOAD THE ORIGINAL VALUE OF R0 ; THE START OF THE ARRAY
	
	AND R6, R6, #0				; 
	ADD R6, R6, #6				; R6 = 6
	
	BR LOOP_BEST				; CONTINUE THE loop
	
FOUND:
	
	LD R0, R0_SAVE_BestStudent	; LOAD THE ORIGINAL VALUE OF R0 ; THE START OF THE ARRAY
	
	STR R4, R0, #5				; MEM[R0+5] = R4 ; WE INSERT THE VALUE OF AVERAGE TO THE END OF THE ARRAY
								; AND THEN WE SWAP WITH THE VALUE NEXT TO IT TILL WE GET THE AVERAGE
								; TO ITS RIGHT PLACE
							
	AND R7, R7, #0				; 
	ADD R7, R7, #4				; R7 = 4
	
	INNER_LOOP:
		ADD R5, R0, R7			; R5 = R0 + R7
		
		LDR R4, R5, #1			; R4 = MEM[R5 + 1]
		LDR R3, R5, #0			; R3 = MEM[R5]
		
		NOT R3, R3
		ADD R3, R3, #1			; R3 = -R3
		
		ADD R3, R3, R4			; WE CHECK IF R4 > R3
		BRp SWAP				; IF R4 > R3, THEN WE SWAP BETWEEN THEM
		BRnz NEXT_LINKED		; ELSE WE JUMP TO NEXT_LINKED
	
	SWAP:
		LDR R3, R5, #0			; R3 = MEM[R5]
		
		STR R3, R5, #1			; MEM[R5 + 1] = R3
		STR R4, R5, #0			; MEM[R5] = R4
		
		ADD R7, R7, #-1			; R7--
		BRn NEXT_LINKED			; IF R7 < 0 , THEN WE STOP THE INNER loop, AND JUMP TO NEXT_LINKED
		BR INNER_LOOP			; ELSE WE CONTINUE
		
		
END_BEST:
	
	; ------------------------- ;
	; load the value of registers ;
	LD R0, R0_SAVE_BestStudent
	LD R1, R1_SAVE_BestStudent
	LD R2, R2_SAVE_BestStudent
	LD R3, R3_SAVE_BestStudent
	LD R4, R4_SAVE_BestStudent
	LD R5, R5_SAVE_BestStudent
	LD R6, R6_SAVE_BestStudent
	LD R7, R7_SAVE_BestStudent
RET
; LABELS FOR BestStudent ;
	R0_SAVE_BestStudent			.fill #0
	R1_SAVE_BestStudent			.fill #0
	R2_SAVE_BestStudent			.fill #0
	R3_SAVE_BestStudent			.fill #0
	R4_SAVE_BestStudent			.fill #0
	R5_SAVE_BestStudent			.fill #0
	R6_SAVE_BestStudent			.fill #0
	R7_SAVE_BestStudent			.fill #0
; ENDS HERE ;


; ------------------------------------------------------------ ;
; ------------------------- PrintNum ------------------------- ;
; ------------------------------------------------------------ ;
; ------------------------------------------------------------ ;

; SUBROUTINE PrintNum FROM HW2 ;
PrintNum:
	; save the value of registers ;
	ST R0, R0_SAVE_PRINTNUM
	ST R1, R1_SAVE_PRINTNUM
	ST R2, R2_SAVE_PRINTNUM
	ST R3, R3_SAVE_PRINTNUM
	ST R4, R4_SAVE_PRINTNUM
	ST R5, R5_SAVE_PRINTNUM
	ST R6, R6_SAVE_PRINTNUM
	ST R7, R7_SAVE_PRINTNUM
	; ------------------------- ;
	
	AND R1, R1, #0			; R1=0 
	ADD R1, R1, #10			; R1=10 ; WE USE R1 AS THE DIVIDER IN SUBROUTINE DIV
	AND R4, R4, #0			; R4 = 0 ;WE USE R4 AS A FLAG , IF R0 IS A NEGATIVE NUMBER WE WILL HOLD IN IT 1 OTHERWISE 0
	ADD R0, R0, #0			; R0 = R0 ;WE CHECK IF THE INPUT NUMBER IS NEGATIVE
	BRzp POSITIVE_NUM		;IF THE GIVEN NUMBER IS'NT NEGATIVE WE JUMP TO THE LABEL POSITIVE_NUM
	
	; WE CHECK IF R0 == -32768 ; IF WE GOT HERE WE KNOW THAT R0 IS NEGATIVE
	LD R3, MAX_VALUE		; WE LOAD THE VALUE 32767 TO R3
	ADD R3, R3, R0			; R3 = R0 + 32767
	ADD R3, R3, #1			; R3 = R3 + 1
	BRp CONVERT				; IF R3 == 0 THEN R0 = -32768 ; ELSE WE JUMP TO CONVERT
	LEA R0, MIN_VALUE		; WE LOAD TO R0 THE STRING "-32768"
	PUTS					; WE PRINT THE STRING USING R0
	BR END_PRINT			; WE JUMP TO THE END OF THE SUBROUTINE
	
CONVERT:
	ADD R4, R4, #1			; R4 = 1 ; IF R0 IS NEGATIVE WE ADD 1 TO THE FLAG R4
	ADD R0, R0, #-1			; R0 = R0 - 1
	NOT R0, R0				; R0 = NOT R0 ;WE CONVERT R0 FROM NEGATIVE TO POSITIVE

	
POSITIVE_NUM:				; WE JUMP HERE IF THE GIVEN NUMBER IS POSITIVE
	LEA R5, Div 			; WE LOAD TO R5 THE ADRESS OF THE DIV SUBROUTINE USING LEA AND THE NAME OF THE SUBROUTINE
	LEA R6, ARRAY			; R6 = &ARRAY ; WE LOAD TO THE R6 THE ADRESS OF THE ARRAY LABEL
	
; THIS LOOP STORES THE DIGITS OF R0 IN ARRAY BY USING THE SUB DIV. IT DIVIDES R0 BY R1=10, AND PUTS THE REMAINDER IN 
; R3 THEN WE STORE IT TO THE ARRAY IN THE APPROPRIATE INDEX 
; WE RUN THIS LOOP TILL R2 == 0
LOOP_PRINT:					
	JSRR R5					; WE JUMP TO THE SUB DIV ADRESS THAT IS STORED IN R5
	LD R0, NUMBER_0_PRINT	; R0 = 48
	ADD R3, R3, R0			; R3 = R3 + (R0=48) ; WE CHANGE THE VALUE OF THE INTEGER TO ITS ASCII VALUE
	STR R3, R6, #0			; MEM[R] = R3 
	ADD R6, R6, #1			; R6++ ; WE MOVE THE MEMORY BY 1 
	ADD R0, R2, #0			; R0 = R2 ; WE PUT IN R0 THE RESULT OF THE DIVISION
	BRp LOOP_PRINT			; IF THE RESULT OF THE DIVISION IS ZERO WE STOP THE LOOP
	
	ADD R6, R6, #-1			; R6-- 
	
	LEA R3, ARRAY			; R3 HOLDS THE address OF ARRAY
	NOT R3, R3				; R3 = NOT R3 ;WE CONVERT R3 TO ITS NEGATIVE value
	ADD R3, R3, #1			; R3 = R3 + 1
	
	ADD R3, R3, R6			; WE MAKE R3 AS A COUNNTER FOR THE NUMBER OF DIGITS IN ARRAY
	
	ADD R4, R4, #0			; WE CHECK IF R4 IS ZERO,IF SO WE JUMP TO PRINT_NUMBER
	BRz PRINT_NUMBER		; IF R4 = 1 THEN THE INPUT IS NEGATIVE, SO WE PRINT '-'
	
	LD R0, MINUS_PRINT		; R0 = 45 ; 45 IS THE ASCII VALUE OF "-"
	OUT						; WE PRINT THE STORED VALUE IN R0 

; THIS LOOP PRINTS THE DIGITS OF THE GIVEN NUMBER THAT WE STORED IN THE ARRAY LABLE IN ORDER FROM LEFT TO RIGHT	
PRINT_NUMBER:				 
	LDR R0, R6, #0			; R0 = MEM[R6]
	OUT						; WE PRINT THE SAVED VALUE IN R0
	ADD R6, R6, #-1			; R6-- ; WE MOVE THE MEMORY ADDRESS BACK BY 1
	ADD R3, R3, #-1			; R3-- ; WE DECREMENT THE COUNTER FOR THE DIGITS IN ARRAY
	BRzp PRINT_NUMBER		; IF R3>=0 WE CONTINUE THE LOOP, ELSE WE STOP

END_PRINT:

	; ------------------------- ;
	; load the value of registers ;
	LD R0, R0_SAVE_PRINTNUM
	LD R1, R1_SAVE_PRINTNUM
	LD R2, R2_SAVE_PRINTNUM
	LD R3, R3_SAVE_PRINTNUM
	LD R4, R4_SAVE_PRINTNUM
	LD R5, R5_SAVE_PRINTNUM
	LD R6, R6_SAVE_PRINTNUM
	LD R7, R7_SAVE_PRINTNUM
	
RET
; LABELS FOR PrintNum ;
	R0_SAVE_PRINTNUM .fill #0
	R1_SAVE_PRINTNUM .fill #0
	R2_SAVE_PRINTNUM .fill #0
	R3_SAVE_PRINTNUM .fill #0
	R4_SAVE_PRINTNUM .fill #0
	R5_SAVE_PRINTNUM .fill #0
	R6_SAVE_PRINTNUM .fill #0
	R7_SAVE_PRINTNUM .fill #0
	
	ARRAY 			 .blkw #5 #0 		; an array of size 5 that holds 0 in every index
	MAX_VALUE		 .fill #32767		; THE MAX VALUE OF POSITIVE NUMBERS
	MIN_VALUE		 .stringz "-32768"	; STRING THAT CONTAINS THE MIN VALUE AVAILABLE
	
	NUMBER_0_PRINT 	 .fill #48		    ; the ascii value of '0'
	MINUS_PRINT 	 .fill #45	 		; the ascii value of '-'
; ENDS HERE ;


; ------------------------------------------------------------ ;
; --------------------------- DIV ---------------------------- ;
; ------------------------------------------------------------ ;
; ------------------------------------------------------------ ;

; function div from HW1 ;

Div:
	ST R0,R0_SAVE	; store the values of R0-R7 , excluding R2, R3
	ST R1,R1_SAVE
	ST R4,R4_SAVE
	ST R5,R5_SAVE
	ST R6,R6_SAVE
	ST R7,R7_SAVE
;--------------------------------------;
	
	; Initialize registers
	AND R3, R3, #0	; R3 <- 0 ; Clear R3, it Will hold the remainder.
	AND R2, R2, #0	; R2 <- 0 ; Clear R2, it Will later hold the result.
	ADD R2, R2, #-1 ; R2 <- -1 , in case we have an error
	ADD R4, R2, #2	; (R4 <- R2(-1) + 2 )= 1	; R4 is a counter for the number of negative numbers we have, we Initialize R4 to 1, assuming R1 is negative.
	
	; Check if R1 is zero or negative.
	ADD R6, R1, #0	;  R6 <- R1 ; we use R6 as a counter for the DIV_while
	BRn R1_NEGATIVE	; if R1 is positive we go to label R1_NEGATIVE
	BRz ERROR_DIV0	; if R1 is zero we go the ERROR_DIV0, the result R2 should be -1, we did put R2 = -1
	NOT R6, R6 	; (R6 <- R6') 
	ADD R6, R6, #1	; (R6 <- R6 + 1) ; Convert R1 to negative if it's positive. using 2's complement conversion
	ADD R4, R4, #-1	; R4 <- R4 - 1 = 1 ; we Decrement R4(counter) as R1 is positive.
	
R1_NEGATIVE:	; label if the R1 is negative
	ADD R5, R0, #0	; R5 <- R0; we check if the R0 is positive or zero
	BRzp DIV_WHILE ; If R0 is positive we dont change its sign
	ADD R4, R4, #1 ; R4 <- R4 + 1; we add 1 if R0 is negeative
	ADD R5, R0, #-1	; (R5 <- R5 - 1) == (R0 - 1)
	NOT R5, R5 	; (R5 <- R5') == (-R0), Convert R0 to positive if it's negative for the loop to work. using 2's complement
	
	; calculate the division using a loop
DIV_WHILE:
	ADD R2, R2, #1 ; we add 1 each time we enter the loop , it indicates to the result of the division
	ADD R5, R5, R6 ; R5 <- R5 - R6 ,  we reduce R5 by R6 each time we are in the loop
	BRzp DIV_WHILE ; as long as R5 is positive we continue the DIV_while
	
	; Calculate the remainder.
	ADD R6, R6, #-1	; (R6 <- R6 + 1) ; we Convert R6 back to positive. using 2's complement
	NOT R6, R6 	; (R6 <- R6') 
	ADD R3, R5, R6 ; we calculate the remainder
	
	; adjust the sign of the result
	AND R4, R4, #1	; we check if R4 is even or odd
	BRz RESULT	; if R4 is even we dont need to change R2 to negative so we jump to result 
	NOT R2, R2	; R2 <- R2' ; we convert R2 from positive to negative using 2s complement
	ADD R2, R2, #1	; R2 <- R2 - 1
	BRnzp RESULT
		
ERROR_DIV0:
	ADD R3, R2, #0 ; R3 <- R2 = -1 ; we have an error ( division by zero )
	
RESULT:	

;--------------------------------------;
	LD R0,R0_SAVE	; load the original values of R0-R7 , excluding R2, R3
	LD R1,R1_SAVE
	LD R4,R4_SAVE
	LD R5,R5_SAVE
	LD R6,R6_SAVE
	LD R7,R7_SAVE	
RET
; LABELS FOR DIV ;
	R0_SAVE .fill #0
	R1_SAVE .fill #0
	R4_SAVE .fill #0
	R5_SAVE .fill #0
	R6_SAVE .fill #0
	R7_SAVE .fill #0	
; ENDS HERE ;


.END