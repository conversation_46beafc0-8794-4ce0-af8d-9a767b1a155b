2025-01-12T21:07:00.656Z In(05) vmx Log for VMware Workstation pid=10944 version=17.5.2 build=build-23775571 option=Release
2025-01-12T21:07:00.656Z In(05) vmx The host is x86_64.
2025-01-12T21:07:00.656Z In(05) vmx Host codepage=windows-1255 encoding=windows-1255
2025-01-12T21:07:00.656Z In(05) vmx Host is Windows 11 Pro, 64-bit (Build 26100.2605)
2025-01-12T21:07:00.656Z In(05) vmx Host offset from UTC is -02:00.
2025-01-12T21:07:00.637Z In(05) vmx VTHREAD 4304 "vmx"
2025-01-12T21:07:00.641Z In(05) vmx LOCALE windows-1255 -> NULL User=409 System=40d
2025-01-12T21:07:00.641Z In(05) vmx Msg_SetLocaleEx: HostLocale=windows-1255 UserLocale=NULL
2025-01-12T21:07:00.641Z In(05) vmx DictionaryLoad: Cannot open file "C:\Users\<USER>\AppData\Roaming\VMware\config.ini": The system cannot find the file specified.
2025-01-12T21:07:00.641Z In(05) vmx Msg_Reset:
2025-01-12T21:07:00.641Z In(05) vmx [msg.dictionary.load.openFailed] Cannot open file "C:\Users\<USER>\AppData\Roaming\VMware\config.ini": The system cannot find the file specified.
2025-01-12T21:07:00.641Z In(05) vmx ----------------------------------------
2025-01-12T21:07:00.641Z In(05) vmx ConfigDB: Failed to load C:\Users\<USER>\AppData\Roaming\VMware\config.ini
2025-01-12T21:07:00.641Z In(05) vmx Win32U_GetFileAttributes: GetFileAttributesExW("C:\Users\<USER>\Desktop\Kali 2024 x64 Customized by zSecurity v1.2.vmwarevm\Kali 2024 x64 Customized by zSecurity v1.2.vmpl", ...) failed, error: 2
2025-01-12T21:07:00.641Z In(05) vmx OBJLIB-LIB: Objlib initialized.
2025-01-12T21:07:00.641Z In(05) vmx DictionaryLoad: Cannot open file "C:\Users\<USER>\AppData\Roaming\VMware\config.ini": The system cannot find the file specified.
2025-01-12T21:07:00.641Z In(05) vmx [msg.dictionary.load.openFailed] Cannot open file "C:\Users\<USER>\AppData\Roaming\VMware\config.ini": The system cannot find the file specified.
2025-01-12T21:07:00.641Z In(05) vmx PREF Optional preferences file not found at C:\Users\<USER>\AppData\Roaming\VMware\config.ini. Using default values.
2025-01-12T21:07:00.656Z In(05) vmx SSL Error: error:80000002:system library::No such file or directory
2025-01-12T21:07:00.656Z In(05) vmx SSL Error: error:10000080:BIO routines::no such file
2025-01-12T21:07:00.656Z In(05) vmx SSL Error: error:07000072:configuration file routines::no such file
2025-01-12T21:07:00.656Z Wa(03) vmx SSLConfigLoad: Failed to load OpenSSL config file.
2025-01-12T21:07:00.656Z In(05) vmx lib/ssl: OpenSSL using default provider
2025-01-12T21:07:00.656Z In(05) vmx lib/ssl: Client usage
2025-01-12T21:07:00.656Z In(05) vmx lib/ssl: protocol list tls1.2
2025-01-12T21:07:00.656Z In(05) vmx lib/ssl: protocol min 0x303 max 0x303
2025-01-12T21:07:00.656Z In(05) vmx lib/ssl: protocol list tls1.2 (openssl flags 0x36000000)
2025-01-12T21:07:00.656Z In(05) vmx lib/ssl: cipher list ECDHE+AESGCM:RSA+AESGCM:ECDHE+AES:RSA+AES
2025-01-12T21:07:00.656Z In(05) vmx lib/ssl: cipher suites TLS_AES_128_GCM_SHA256:TLS_AES_256_GCM_SHA384
2025-01-12T21:07:00.656Z In(05) vmx lib/ssl: curves list prime256v1:secp384r1:secp521r1
2025-01-12T21:07:00.656Z In(05) vmx lib/ssl: Server usage
2025-01-12T21:07:00.656Z In(05) vmx lib/ssl: protocol list tls1.2
2025-01-12T21:07:00.656Z In(05) vmx lib/ssl: protocol min 0x303 max 0x303
2025-01-12T21:07:00.656Z In(05) vmx lib/ssl: protocol list tls1.2 (openssl flags 0x36000000)
2025-01-12T21:07:00.656Z In(05) vmx lib/ssl: cipher list ECDHE+AESGCM:RSA+AESGCM:ECDHE+AES:RSA+AES
2025-01-12T21:07:00.656Z In(05) vmx lib/ssl: cipher suites TLS_AES_128_GCM_SHA256:TLS_AES_256_GCM_SHA384
2025-01-12T21:07:00.656Z In(05) vmx lib/ssl: curves list prime256v1:secp384r1:secp521r1
2025-01-12T21:07:00.656Z In(05) vmx Hostname=DESKTOP-Q1DBRSH
2025-01-12T21:07:00.672Z In(05) vmx IP=fe80::77:3f79:dcd3:7165%16
2025-01-12T21:07:00.672Z In(05) vmx IP=fe80::7e94:4b4a:8f20:117e%7
2025-01-12T21:07:00.672Z In(05) vmx IP=fe80::857d:fad3:4e57:9c68%4
2025-01-12T21:07:00.672Z In(05) vmx IP=************
2025-01-12T21:07:00.672Z In(05) vmx IP=************
2025-01-12T21:07:00.672Z In(05) vmx IP=***********
2025-01-12T21:07:00.704Z In(05) vmx System uptime 82851591000 us
2025-01-12T21:07:00.704Z In(05) vmx Command line: "C:\Program Files (x86)\VMware\VMware Workstation\x64\vmware-vmx.exe" "-T" "querytoken" "-s" "vmx.stdio.keep=TRUE" "-#" "product=1;name=VMware Workstation;version=17.5.2;buildnumber=23775571;licensename=VMware Workstation;licenseversion=17.0;" "-@" "pipe=\\.\pipe\vmxc21add496e5deeb1;msgs=ui" "C:\Users\<USER>\Desktop\Kali 2024 x64 Customized by zSecurity v1.2.vmwarevm\Kali 2024 x64 Customized by zSecurity v1.2.vmx"
2025-01-12T21:07:00.704Z In(05) vmx Msg_SetLocaleEx: HostLocale=windows-1255 UserLocale=NULL
2025-01-12T21:07:00.720Z In(05) vmx WQPoolAllocPoll : pollIx = 1, signalHandle = 840
2025-01-12T21:07:00.720Z In(05) vmx WQPoolAllocPoll : pollIx = 2, signalHandle = 848
2025-01-12T21:07:00.720Z In(05) vmx VigorTransport listening on fd 812
2025-01-12T21:07:00.720Z In(05) vmx Vigor_Init 1
2025-01-12T21:07:00.720Z In(05) vmx Connecting 'ui' to pipe '\\.\pipe\vmxc21add496e5deeb1' with user '(null)'
2025-01-12T21:07:00.736Z In(05) vmx VMXVmdb: Local connection timeout: 60000 ms.
2025-01-12T21:07:00.789Z In(05) vmx VmdbAddConnection: cnxPath=/db/connection/#1/, cnxIx=1
2025-01-12T21:07:00.790Z In(05) vmx Vix: [mainDispatch.c:488]: VMAutomation: Initializing VMAutomation.
2025-01-12T21:07:00.790Z In(05) vmx Vix: [mainDispatch.c:740]: VMAutomationOpenListenerSocket() listening
2025-01-12T21:07:00.803Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=0, newAppState=1870, success=1 additionalError=0
2025-01-12T21:07:00.803Z In(05) vmx Transitioned vmx/execState/val to poweredOff
2025-01-12T21:07:00.803Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=1, newAppState=1873, success=1 additionalError=0
2025-01-12T21:07:00.803Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=2, newAppState=1877, success=1 additionalError=0
2025-01-12T21:07:00.803Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=3, newAppState=1881, success=1 additionalError=0
2025-01-12T21:07:00.868Z In(05) vmx IOPL_VBSRunning: VBS is set to 0
2025-01-12T21:07:00.868Z In(05) vmx IOCTL_VMX86_SET_MEMORY_PARAMS already set
2025-01-12T21:07:00.868Z In(05) vmx FeatureCompat: No EVC masks.
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID vendor: GenuineIntel
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID family: 0x6 model: 0x7e stepping: 0x5
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID codename: Ice Lake-U/Y
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID name: Intel(R) Core(TM) i3-1005G1 CPU @ 1.20GHz
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 00000000, 0: 0x0000001b 0x756e6547 0x6c65746e 0x49656e69
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 00000001, 0: 0x000706e5 0x00100800 0x7ffafbbf 0xbfebfbff
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 00000002, 0: 0x00feff01 0x000000f0 0x00000000 0x00000000
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 00000003, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 00000004, 0: 0x1c004121 0x02c0003f 0x0000003f 0x00000000
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 00000004, 1: 0x1c004122 0x01c0003f 0x0000003f 0x00000000
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 00000004, 2: 0x1c004143 0x01c0003f 0x000003ff 0x00000000
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 00000004, 3: 0x1c03c163 0x03c0003f 0x00000fff 0x00000006
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 00000004, 4: 0x00000000 0x00000000 0x00000000 0x00000000
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 00000005, 0: 0x00000040 0x00000040 0x00000003 0x11121020
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 00000006, 0: 0x0017aff7 0x00000002 0x00000009 0x00000000
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 00000007, 0: 0x00000000 0xf2bf27ef 0x40405f4e 0xbc000410
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 00000008, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 00000009, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 0000000a, 0: 0x08300805 0x00000000 0x0000000f 0x00008604
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 0000000b, 0: 0x00000001 0x00000002 0x00000100 0x00000000
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 0000000b, 1: 0x00000004 0x00000004 0x00000201 0x00000000
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 0000000b, 2: 0x00000000 0x00000000 0x00000002 0x00000000
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 0000000c, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 0000000d, 0: 0x000002e7 0x00000a80 0x00000a88 0x00000000
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 0000000d, 1: 0x0000000f 0x00000a00 0x00002100 0x00000000
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 0000000d, 2: 0x00000100 0x00000240 0x00000000 0x00000000
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 0000000d, 3: 0x00000000 0x00000000 0x00000000 0x00000000
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 0000000d, 4: 0x00000000 0x00000000 0x00000000 0x00000000
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 0000000d, 5: 0x00000040 0x00000440 0x00000000 0x00000000
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 0000000d, 6: 0x00000200 0x00000480 0x00000000 0x00000000
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 0000000d, 7: 0x00000400 0x00000680 0x00000000 0x00000000
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 0000000d, 8: 0x00000080 0x00000000 0x00000001 0x00000000
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 0000000d, 9: 0x00000008 0x00000a80 0x00000000 0x00000000
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 0000000d, a: 0x00000000 0x00000000 0x00000000 0x00000000
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 0000000d, b: 0x00000000 0x00000000 0x00000000 0x00000000
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 0000000d, c: 0x00000000 0x00000000 0x00000000 0x00000000
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 0000000d, d: 0x00000008 0x00000000 0x00000001 0x00000000
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 0000000e, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 0000000f, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 0000000f, 1: 0x00000000 0x00000000 0x00000000 0x00000000
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 00000010, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 00000010, 1: 0x00000000 0x00000000 0x00000000 0x00000000
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 00000010, 2: 0x00000000 0x00000000 0x00000000 0x00000000
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 00000010, 3: 0x00000000 0x00000000 0x00000000 0x00000000
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 00000011, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 00000012, 0: 0x00000063 0x00000001 0x00000000 0x00002f1f
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 00000012, 1: 0x000000b6 0x00000000 0x000002e7 0x00000000
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 00000012, 2: 0x50180001 0x00000000 0x0bc00001 0x00000000
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 00000012, 3: 0x00000000 0x00000000 0x00000000 0x00000000
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 00000013, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 00000014, 0: 0x00000001 0x0000000f 0x00000007 0x00000000
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 00000014, 1: 0x02490002 0x003f1fff 0x00000000 0x00000000
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 00000015, 0: 0x00000002 0x0000003e 0x0249f000 0x00000000
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 00000016, 0: 0x000004b0 0x00000d48 0x00000064 0x00000000
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 00000017, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 00000018, 0: 0x00000007 0x00000000 0x00000000 0x00000000
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 00000018, 1: 0x00000000 0x00080007 0x00000001 0x00004122
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 00000018, 2: 0x00000000 0x0010000f 0x00000001 0x00004125
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 00000018, 3: 0x00000000 0x00040001 0x00000010 0x00004024
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 00000018, 4: 0x00000000 0x00040006 0x00000008 0x00004024
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 00000018, 5: 0x00000000 0x00080008 0x00000001 0x00004124
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 00000018, 6: 0x00000000 0x00080007 0x00000080 0x00004043
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 00000018, 7: 0x00000000 0x00080009 0x00000080 0x00004043
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 00000019, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 0000001a, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 0000001b, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 80000000, 0: 0x80000008 0x00000000 0x00000000 0x00000000
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 80000001, 0: 0x00000000 0x00000000 0x00000121 0x2c100800
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 80000002, 0: 0x65746e49 0x2952286c 0x726f4320 0x4d542865
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 80000003, 0: 0x33692029 0x3030312d 0x20314735 0x20555043
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 80000004, 0: 0x2e312040 0x48473032 0x0000007a 0x00000000
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 80000005, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 80000006, 0: 0x00000000 0x00000000 0x01006040 0x00000000
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 80000007, 0: 0x00000000 0x00000000 0x00000000 0x00000100
2025-01-12T21:07:00.884Z In(05) vmx hostCPUID level 80000008, 0: 0x00003027 0x00000000 0x00000000 0x00000000
2025-01-12T21:07:00.884Z In(05) vmx CPUID differences from hostCPUID.
2025-01-12T21:07:00.884Z In(05) vmx Physical APIC IDs: 0-3
2025-01-12T21:07:00.884Z In(05) vmx Physical X2APIC IDs: 0-3
2025-01-12T21:07:00.884Z In(05) vmx Common: MSR       0x3a =            0x60005
2025-01-12T21:07:00.884Z In(05) vmx Common: MSR      0x480 =   0xda050000000013
2025-01-12T21:07:00.884Z In(05) vmx Common: MSR      0x481 =       0xff00000016
2025-01-12T21:07:00.884Z In(05) vmx Common: MSR      0x482 = 0xfff9fffe0401e172
2025-01-12T21:07:00.884Z In(05) vmx Common: MSR      0x483 =  0x37fffff00036dff
2025-01-12T21:07:00.884Z In(05) vmx Common: MSR      0x484 =    0x6ffff000011ff
2025-01-12T21:07:00.884Z In(05) vmx Common: MSR      0x485 =         0x7004c1e7
2025-01-12T21:07:00.884Z In(05) vmx Common: MSR      0x486 =         0x80000021
2025-01-12T21:07:00.884Z In(05) vmx Common: MSR      0x487 =         0xffffffff
2025-01-12T21:07:00.884Z In(05) vmx Common: MSR      0x488 =             0x2000
2025-01-12T21:07:00.884Z In(05) vmx Common: MSR      0x489 =           0x772fff
2025-01-12T21:07:00.884Z In(05) vmx Common: MSR      0x48a =               0x2e
2025-01-12T21:07:00.884Z In(05) vmx Common: MSR      0x48b = 0x335fbfff00000000
2025-01-12T21:07:00.884Z In(05) vmx Common: MSR      0x48c =      0xf0106734141
2025-01-12T21:07:00.884Z In(05) vmx Common: MSR      0x48d =       0xff00000016
2025-01-12T21:07:00.884Z In(05) vmx Common: MSR      0x48e = 0xfff9fffe04006172
2025-01-12T21:07:00.884Z In(05) vmx Common: MSR      0x48f =  0x37fffff00036dfb
2025-01-12T21:07:00.884Z In(05) vmx Common: MSR      0x490 =    0x6ffff000011fb
2025-01-12T21:07:00.884Z In(05) vmx Common: MSR      0x491 =                0x1
2025-01-12T21:07:00.884Z In(05) vmx Common: MSR      0x492 =                  0
2025-01-12T21:07:00.884Z In(05) vmx Common: MSR 0xc0010114 =                  0
2025-01-12T21:07:00.884Z In(05) vmx Common: MSR       0xce =         0x80000000
2025-01-12T21:07:00.884Z In(05) vmx Common: MSR      0x10a =               0x2b
2025-01-12T21:07:00.884Z In(05) vmx Common: MSR      0x122 =                  0
2025-01-12T21:07:00.884Z In(05) vmx VMMon_GetkHzEstimate: Calculated 1190391 kHz
2025-01-12T21:07:00.884Z In(05) vmx TSC Hz estimates: vmmon 1190391000, remembered 0, osReported 1190000000. Using 1190391000 Hz.
2025-01-12T21:07:00.884Z In(05) vmx TSC first measured delta 85
2025-01-12T21:07:00.884Z In(05) vmx TSC min delta 51
2025-01-12T21:07:00.884Z In(05) vmx PTSC: RefClockToPTSC 0 @ 10000000Hz -> 0 @ 1190391000Hz
2025-01-12T21:07:00.884Z In(05) vmx PTSC: RefClockToPTSC ((x * 3994289386) >> 25) + -13509414082587
2025-01-12T21:07:00.884Z In(05) vmx PTSC: tscOffset -13523040623782
2025-01-12T21:07:00.884Z In(05) vmx PTSC: using TSC
2025-01-12T21:07:00.884Z In(05) vmx PTSC: hardware TSCs are synchronized.
2025-01-12T21:07:00.884Z In(05) vmx PTSC: hardware TSCs may have been adjusted by the host.
2025-01-12T21:07:00.884Z In(05) vmx PTSC: current PTSC=49007
2025-01-12T21:07:00.900Z In(05) vmx WQPoolAllocPoll : pollIx = 3, signalHandle = 1092
2025-01-12T21:07:00.936Z In(05) vmx ConfigCheck: No rules file found. Checks are disabled.
2025-01-12T21:07:00.936Z In(05) vmx changing directory to C:\Users\<USER>\Desktop\Kali 2024 x64 Customized by zSecurity v1.2.vmwarevm\.
2025-01-12T21:07:00.936Z In(05) vmx Config file: C:\Users\<USER>\Desktop\Kali 2024 x64 Customized by zSecurity v1.2.vmwarevm\Kali 2024 x64 Customized by zSecurity v1.2.vmx
2025-01-12T21:07:00.938Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=1, newAppState=1875, success=1 additionalError=0
2025-01-12T21:07:00.938Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=2, newAppState=1878, success=1 additionalError=0
2025-01-12T21:07:00.950Z In(05) vmx LogRotation: Rotating to a new log file (keepOld 3) took 0.010348 seconds.
2025-01-12T21:07:00.964Z No(00) vmx LogVMXReplace: Successful switching from temporary to permanent log file took 0.033766 seconds.
2025-01-12T21:07:00.979Z No(00) vmx ConfigDB: Setting ide1:0.fileName = "auto detect"
2025-01-12T21:07:00.995Z Wa(03) vmx PowerOn
2025-01-12T21:07:00.995Z In(05) vmx VMX_PowerOn: VMX build 23775571, UI build 23775571
2025-01-12T21:07:00.995Z In(05) vmx HostWin32: WIN32 NUMA node 0, CPU mask 0x000000000000000f
2025-01-12T21:07:01.007Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=0, newAppState=1871, success=1 additionalError=0
2025-01-12T21:07:01.013Z In(05) vmx VMXSTATS: Successfully created stats file 'Kali 2024 x64 Customized by zSecurity v1.2.scoreboard'
2025-01-12T21:07:01.013Z In(05) vmx VMXSTATS: Update Product Information: VMware Workstation	17.5.2	build-23775571	Release  TotalBlockSize: 56
2025-01-12T21:07:01.019Z In(05) vmx HOST Windows version 10.0, build 26100, platform 2, ""
2025-01-12T21:07:01.019Z In(05) vmx DICT --- GLOBAL SETTINGS C:\ProgramData\VMware\VMware Workstation\settings.ini
2025-01-12T21:07:01.019Z In(05) vmx DICT          printers.enabled = "FALSE"
2025-01-12T21:07:01.019Z In(05) vmx DICT --- NON PERSISTENT (null)
2025-01-12T21:07:01.019Z In(05) vmx DICT --- USER PREFERENCES C:\Users\<USER>\AppData\Roaming\VMware\preferences.ini
2025-01-12T21:07:01.019Z In(05) vmx DICT pref.keyboardAndMouse.vmHotKey.enabled = "FALSE"
2025-01-12T21:07:01.019Z In(05) vmx DICT pref.keyboardAndMouse.vmHotKey.count = "0"
2025-01-12T21:07:01.019Z In(05) vmx DICT pref.wspro.firstRunDismissedVersion = "17.5.2"
2025-01-12T21:07:01.019Z In(05) vmx DICT pref.updatesVersionIgnore.numItems = "2"
2025-01-12T21:07:01.019Z In(05) vmx DICT pref.updatesVersionIgnore0.key = <not printed>
2025-01-12T21:07:01.019Z In(05) vmx DICT pref.updatesVersionIgnore0.value = "35f211f8-915f-4000-8d14-a379e4990924"
2025-01-12T21:07:01.019Z In(05) vmx DICT   pref.lastUpdateCheckSec = "1736715995"
2025-01-12T21:07:01.019Z In(05) vmx DICT pref.ws.session.window.count = "1"
2025-01-12T21:07:01.019Z In(05) vmx DICT pref.ws.session.window0.tab.count = "1"
2025-01-12T21:07:01.019Z In(05) vmx DICT pref.ws.session.window0.tab0.cnxType = "vmdb"
2025-01-12T21:07:01.019Z In(05) vmx DICT pref.ws.session.window0.tab1.cnxType = "vmdb"
2025-01-12T21:07:01.019Z In(05) vmx DICT pref.ws.session.window0.sidebar = "TRUE"
2025-01-12T21:07:01.019Z In(05) vmx DICT pref.ws.session.window0.sidebar.width = "200"
2025-01-12T21:07:01.019Z In(05) vmx DICT pref.ws.session.window0.statusBar = "TRUE"
2025-01-12T21:07:01.019Z In(05) vmx DICT pref.ws.session.window0.tabs = "TRUE"
2025-01-12T21:07:01.019Z In(05) vmx DICT pref.ws.session.window0.thumbnailBar = "FALSE"
2025-01-12T21:07:01.019Z In(05) vmx DICT pref.ws.session.window0.thumbnailBar.size = "100"
2025-01-12T21:07:01.019Z In(05) vmx DICT pref.ws.session.window0.thumbnailBar.view = "opened-vms"
2025-01-12T21:07:01.019Z In(05) vmx DICT pref.ws.session.window0.placement.left = "32"
2025-01-12T21:07:01.019Z In(05) vmx DICT pref.ws.session.window0.placement.top = "32"
2025-01-12T21:07:01.019Z In(05) vmx DICT pref.ws.session.window0.placement.right = "956"
2025-01-12T21:07:01.019Z In(05) vmx DICT pref.ws.session.window0.placement.bottom = "575"
2025-01-12T21:07:01.019Z In(05) vmx DICT pref.ws.session.window0.maximized = "TRUE"
2025-01-12T21:07:01.019Z In(05) vmx DICT  pref.fullscreen.autohide = "TRUE"
2025-01-12T21:07:01.019Z In(05) vmx DICT pref.ws.session.window0.tab2.cnxType = "vmdb"
2025-01-12T21:07:01.019Z In(05) vmx DICT  pref.sharedFolder.maxNum = "1"
2025-01-12T21:07:01.019Z In(05) vmx DICT pref.sharedFolder0.vmPath = "/vm/#3babcb943734f8d6/"
2025-01-12T21:07:01.019Z In(05) vmx DICT pref.sharedFolder0.guestName = "Shared"
2025-01-12T21:07:01.019Z In(05) vmx DICT pref.sharedFolder0.hostPath = "/Volumes/Data/Shared"
2025-01-12T21:07:01.019Z In(05) vmx DICT pref.sharedFolder0.enabled = "FALSE"
2025-01-12T21:07:01.019Z In(05) vmx DICT  hint.vmui.showNewUSBDevs = "FALSE"
2025-01-12T21:07:01.019Z In(05) vmx DICT             hints.hideAll = "FALSE"
2025-01-12T21:07:01.019Z In(05) vmx DICT pref.ws.session.window0.tab0.dest = ""
2025-01-12T21:07:01.019Z In(05) vmx DICT pref.ws.session.window0.tab0.file = ""
2025-01-12T21:07:01.019Z In(05) vmx DICT pref.ws.session.window0.tab0.type = "home"
2025-01-12T21:07:01.019Z In(05) vmx DICT pref.ws.session.window0.tab0.focused = "TRUE"
2025-01-12T21:07:01.019Z In(05) vmx DICT pref.updatesVersionIgnore1.key = <not printed>
2025-01-12T21:07:01.019Z In(05) vmx DICT pref.updatesVersionIgnore1.value = "bb61d294-c7fd-4b93-bdb3-48a9b5b74f44"
2025-01-12T21:07:01.019Z In(05) vmx DICT         vmWizard.guestKey = "debian10-64"
2025-01-12T21:07:01.019Z In(05) vmx DICT vmWizard.installMediaType = "later"
2025-01-12T21:07:01.019Z In(05) vmx DICT pref.ws.session.window0.tab3.cnxType = "vmdb"
2025-01-12T21:07:01.019Z In(05) vmx DICT --- USER DEFAULTS C:\Users\<USER>\AppData\Roaming\VMware\config.ini
2025-01-12T21:07:01.019Z In(05) vmx DICT --- HOST DEFAULTS C:\ProgramData\VMware\VMware Workstation\config.ini
2025-01-12T21:07:01.019Z In(05) vmx DICT         authd.client.port = "902"
2025-01-12T21:07:01.019Z In(05) vmx DICT           authd.proxy.nfc = "vmware-hostd:ha-nfc"
2025-01-12T21:07:01.019Z In(05) vmx DICT installerDefaults.autoSoftwareUpdateEnabled = "yes"
2025-01-12T21:07:01.019Z In(05) vmx DICT installerDefaults.autoSoftwareUpdateEnabled.epoch = "5203"
2025-01-12T21:07:01.019Z In(05) vmx DICT installerDefaults.componentDownloadEnabled = "yes"
2025-01-12T21:07:01.019Z In(05) vmx DICT installerDefaults.dataCollectionEnabled = "yes"
2025-01-12T21:07:01.019Z In(05) vmx DICT installerDefaults.dataCollectionEnabled.epoch = "5203"
2025-01-12T21:07:01.019Z In(05) vmx DICT --- SITE DEFAULTS C:\ProgramData\VMware\VMware Workstation\config.ini
2025-01-12T21:07:01.019Z In(05) vmx DICT         authd.client.port = "902"
2025-01-12T21:07:01.019Z In(05) vmx DICT           authd.proxy.nfc = "vmware-hostd:ha-nfc"
2025-01-12T21:07:01.019Z In(05) vmx DICT installerDefaults.autoSoftwareUpdateEnabled = "yes"
2025-01-12T21:07:01.019Z In(05) vmx DICT installerDefaults.autoSoftwareUpdateEnabled.epoch = "5203"
2025-01-12T21:07:01.019Z In(05) vmx DICT installerDefaults.componentDownloadEnabled = "yes"
2025-01-12T21:07:01.019Z In(05) vmx DICT installerDefaults.dataCollectionEnabled = "yes"
2025-01-12T21:07:01.019Z In(05) vmx DICT installerDefaults.dataCollectionEnabled.epoch = "5203"
2025-01-12T21:07:01.019Z In(05) vmx DICT --- NONPERSISTENT
2025-01-12T21:07:01.019Z In(05) vmx DICT            vmx.stdio.keep = "TRUE"
2025-01-12T21:07:01.019Z In(05) vmx DICT             gui.available = "TRUE"
2025-01-12T21:07:01.019Z In(05) vmx DICT --- COMMAND LINE
2025-01-12T21:07:01.019Z In(05) vmx DICT            vmx.stdio.keep = "TRUE"
2025-01-12T21:07:01.019Z In(05) vmx DICT             gui.available = "TRUE"
2025-01-12T21:07:01.019Z In(05) vmx DICT --- RECORDING
2025-01-12T21:07:01.019Z In(05) vmx DICT            vmx.stdio.keep = "TRUE"
2025-01-12T21:07:01.019Z In(05) vmx DICT             gui.available = "TRUE"
2025-01-12T21:07:01.019Z In(05) vmx DICT --- CONFIGURATION C:\Users\<USER>\Desktop\Kali 2024 x64 Customized by zSecurity v1.2.vmwarevm\Kali 2024 x64 Customized by zSecurity v1.2.vmx 
2025-01-12T21:07:01.019Z In(05) vmx DICT            config.version = "8"
2025-01-12T21:07:01.019Z In(05) vmx DICT         virtualHW.version = "21"
2025-01-12T21:07:01.019Z In(05) vmx DICT        pciBridge0.present = "TRUE"
2025-01-12T21:07:01.019Z In(05) vmx DICT        pciBridge4.present = "TRUE"
2025-01-12T21:07:01.019Z In(05) vmx DICT     pciBridge4.virtualDev = "pcieRootPort"
2025-01-12T21:07:01.019Z In(05) vmx DICT      pciBridge4.functions = "8"
2025-01-12T21:07:01.019Z In(05) vmx DICT        pciBridge5.present = "TRUE"
2025-01-12T21:07:01.019Z In(05) vmx DICT     pciBridge5.virtualDev = "pcieRootPort"
2025-01-12T21:07:01.019Z In(05) vmx DICT      pciBridge5.functions = "8"
2025-01-12T21:07:01.019Z In(05) vmx DICT        pciBridge6.present = "TRUE"
2025-01-12T21:07:01.019Z In(05) vmx DICT     pciBridge6.virtualDev = "pcieRootPort"
2025-01-12T21:07:01.019Z In(05) vmx DICT      pciBridge6.functions = "8"
2025-01-12T21:07:01.019Z In(05) vmx DICT        pciBridge7.present = "TRUE"
2025-01-12T21:07:01.019Z In(05) vmx DICT     pciBridge7.virtualDev = "pcieRootPort"
2025-01-12T21:07:01.019Z In(05) vmx DICT      pciBridge7.functions = "8"
2025-01-12T21:07:01.019Z In(05) vmx DICT             vmci0.present = "TRUE"
2025-01-12T21:07:01.019Z In(05) vmx DICT             hpet0.present = "TRUE"
2025-01-12T21:07:01.019Z In(05) vmx DICT                     nvram = "Kali 2024 x64 Customized by zSecurity v1.2.nvram"
2025-01-12T21:07:01.019Z In(05) vmx DICT virtualHW.productCompatibility = "hosted"
2025-01-12T21:07:01.019Z In(05) vmx DICT        powerType.powerOff = "soft"
2025-01-12T21:07:01.019Z In(05) vmx DICT         powerType.powerOn = "soft"
2025-01-12T21:07:01.019Z In(05) vmx DICT         powerType.suspend = "soft"
2025-01-12T21:07:01.019Z In(05) vmx DICT           powerType.reset = "soft"
2025-01-12T21:07:01.019Z In(05) vmx DICT               displayName = "Kali 2024 x64 Customized by zSecurity v1.2"
2025-01-12T21:07:01.019Z In(05) vmx DICT usb.vbluetooth.startConnected = "TRUE"
2025-01-12T21:07:01.019Z In(05) vmx DICT                   guestOS = "debian12-64"
2025-01-12T21:07:01.019Z In(05) vmx DICT            tools.syncTime = "TRUE"
2025-01-12T21:07:01.019Z In(05) vmx DICT      tools.upgrade.policy = "upgradeAtPowerCycle"
2025-01-12T21:07:01.019Z In(05) vmx DICT          sound.autoDetect = "TRUE"
2025-01-12T21:07:01.019Z In(05) vmx DICT            sound.fileName = "-1"
2025-01-12T21:07:01.019Z In(05) vmx DICT             sound.present = "TRUE"
2025-01-12T21:07:01.019Z In(05) vmx DICT                   memsize = "8192"
2025-01-12T21:07:01.019Z In(05) vmx DICT          scsi0.virtualDev = "lsilogic"
2025-01-12T21:07:01.019Z In(05) vmx DICT             scsi0.present = "TRUE"
2025-01-12T21:07:01.019Z In(05) vmx DICT          scsi0:0.fileName = "disk-000001.vmdk"
2025-01-12T21:07:01.019Z In(05) vmx DICT         ide1:0.deviceType = "cdrom-raw"
2025-01-12T21:07:01.019Z In(05) vmx DICT           ide1:0.fileName = "auto detect"
2025-01-12T21:07:01.019Z In(05) vmx DICT            ide1:0.present = "TRUE"
2025-01-12T21:07:01.019Z In(05) vmx DICT               usb.present = "TRUE"
2025-01-12T21:07:01.019Z In(05) vmx DICT              ehci.present = "TRUE"
2025-01-12T21:07:01.019Z In(05) vmx DICT  ethernet0.connectionType = "nat"
2025-01-12T21:07:01.019Z In(05) vmx DICT     ethernet0.addressType = "generated"
2025-01-12T21:07:01.019Z In(05) vmx DICT ethernet0.linkStatePropagation.enable = "TRUE"
2025-01-12T21:07:01.019Z In(05) vmx DICT         ethernet0.present = "TRUE"
2025-01-12T21:07:01.019Z In(05) vmx DICT        extendedConfigFile = "Kali 2024 x64 Customized by zSecurity v1.2.vmxf"
2025-01-12T21:07:01.019Z In(05) vmx DICT                  numvcpus = "2"
2025-01-12T21:07:01.019Z In(05) vmx DICT gui.perVMWindowAutofitMode = "resize"
2025-01-12T21:07:01.019Z In(05) vmx DICT gui.perVMFullscreenAutofitMode = "resize"
2025-01-12T21:07:01.019Z In(05) vmx DICT           scsi0:0.present = "TRUE"
2025-01-12T21:07:01.019Z In(05) vmx DICT      numa.autosize.cookie = "20012"
2025-01-12T21:07:01.019Z In(05) vmx DICT numa.autosize.vcpu.maxPerVirtualNode = "2"
2025-01-12T21:07:01.019Z In(05) vmx DICT                 uuid.bios = "56 4d 40 f8 4f 80 56 23-17 f1 7f d0 7c 71 5a 8c"
2025-01-12T21:07:01.019Z In(05) vmx DICT             uuid.location = "56 4d 40 f8 4f 80 56 23-17 f1 7f d0 7c 71 5a 8c"
2025-01-12T21:07:01.019Z In(05) vmx DICT              scsi0:0.redo = ""
2025-01-12T21:07:01.019Z In(05) vmx DICT  pciBridge0.pciSlotNumber = "17"
2025-01-12T21:07:01.019Z In(05) vmx DICT  pciBridge4.pciSlotNumber = "21"
2025-01-12T21:07:01.019Z In(05) vmx DICT  pciBridge5.pciSlotNumber = "22"
2025-01-12T21:07:01.019Z In(05) vmx DICT  pciBridge6.pciSlotNumber = "23"
2025-01-12T21:07:01.019Z In(05) vmx DICT  pciBridge7.pciSlotNumber = "24"
2025-01-12T21:07:01.019Z In(05) vmx DICT       scsi0.pciSlotNumber = "16"
2025-01-12T21:07:01.019Z In(05) vmx DICT         usb.pciSlotNumber = "32"
2025-01-12T21:07:01.019Z In(05) vmx DICT   ethernet0.pciSlotNumber = "33"
2025-01-12T21:07:01.019Z In(05) vmx DICT       sound.pciSlotNumber = "34"
2025-01-12T21:07:01.019Z In(05) vmx DICT        ehci.pciSlotNumber = "35"
2025-01-12T21:07:01.019Z In(05) vmx DICT       vmci0.pciSlotNumber = "36"
2025-01-12T21:07:01.021Z In(05) vmx DICT             svga.vramSize = "268435456"
2025-01-12T21:07:01.021Z In(05) vmx DICT  vmotion.checkpointFBSize = "4194304"
2025-01-12T21:07:01.021Z In(05) vmx DICT vmotion.checkpointSVGAPrimarySize = "268435456"
2025-01-12T21:07:01.021Z In(05) vmx DICT   vmotion.svga.mobMaxSize = "268435456"
2025-01-12T21:07:01.021Z In(05) vmx DICT vmotion.svga.graphicsMemoryKB = "262144"
2025-01-12T21:07:01.021Z In(05) vmx DICT ethernet0.generatedAddress = "00:0c:29:71:5a:8c"
2025-01-12T21:07:01.021Z In(05) vmx DICT ethernet0.generatedAddressOffset = "0"
2025-01-12T21:07:01.021Z In(05) vmx DICT                  vmci0.id = "-586922912"
2025-01-12T21:07:01.021Z In(05) vmx DICT    monitor.phys_bits_used = "45"
2025-01-12T21:07:01.021Z In(05) vmx DICT             cleanShutdown = "TRUE"
2025-01-12T21:07:01.021Z In(05) vmx DICT              softPowerOff = "FALSE"
2025-01-12T21:07:01.021Z In(05) vmx DICT               usb:1.speed = "2"
2025-01-12T21:07:01.021Z In(05) vmx DICT             usb:1.present = "TRUE"
2025-01-12T21:07:01.021Z In(05) vmx DICT          usb:1.deviceType = "hub"
2025-01-12T21:07:01.021Z In(05) vmx DICT                usb:1.port = "1"
2025-01-12T21:07:01.021Z In(05) vmx DICT              usb:1.parent = "-1"
2025-01-12T21:07:01.021Z In(05) vmx DICT svga.guestBackedPrimaryAware = "TRUE"
2025-01-12T21:07:01.021Z In(05) vmx DICT     guestOS.detailed.data = "architecture='X86' bitness='64' distroAddlVersion='2024.3' distroName='Kali GNU/Linux' distroVersion='2024.3' familyName='Linux' kernelVersion='6.8.11-amd64' prettyName='Kali GNU/Linux Rolling'"
2025-01-12T21:07:01.021Z In(05) vmx DICT toolsInstallManager.updateCounter = "1"
2025-01-12T21:07:01.021Z In(05) vmx DICT isolation.tools.hgfs.disable = "TRUE"
2025-01-12T21:07:01.021Z In(05) vmx DICT         hgfs.mapRootShare = "TRUE"
2025-01-12T21:07:01.021Z In(05) vmx DICT        hgfs.linkRootShare = "TRUE"
2025-01-12T21:07:01.021Z In(05) vmx DICT     sharedFolder0.present = "TRUE"
2025-01-12T21:07:01.021Z In(05) vmx DICT     sharedFolder0.enabled = "TRUE"
2025-01-12T21:07:01.021Z In(05) vmx DICT  sharedFolder0.readAccess = "TRUE"
2025-01-12T21:07:01.021Z In(05) vmx DICT sharedFolder0.writeAccess = "TRUE"
2025-01-12T21:07:01.021Z In(05) vmx DICT    sharedFolder0.hostPath = "/Volumes/Data/Shared"
2025-01-12T21:07:01.021Z In(05) vmx DICT   sharedFolder0.guestName = "Shared"
2025-01-12T21:07:01.021Z In(05) vmx DICT  sharedFolder0.expiration = "never"
2025-01-12T21:07:01.021Z In(05) vmx DICT       sharedFolder.maxNum = "1"
2025-01-12T21:07:01.021Z In(05) vmx DICT     ide1:0.startConnected = "FALSE"
2025-01-12T21:07:01.021Z In(05) vmx DICT         ide1:0.autodetect = "TRUE"
2025-01-12T21:07:01.021Z In(05) vmx DICT           floppy0.present = "FALSE"
2025-01-12T21:07:01.021Z In(05) vmx DICT         vmxstats.filename = "Kali 2024 x64 Customized by zSecurity v1.2.scoreboard"
2025-01-12T21:07:01.021Z In(05) vmx DICT tools.capability.verifiedSamlToken = "TRUE"
2025-01-12T21:07:01.021Z In(05) vmx DICT   guestInfo.detailed.data = <not printed>
2025-01-12T21:07:01.021Z In(05) vmx DICT cpuid.coresPerSocket.cookie = "2"
2025-01-12T21:07:01.021Z In(05) vmx DICT      usb.generic.allowHID = "TRUE"
2025-01-12T21:07:01.021Z In(05) vmx DICT          usb_xhci.present = "TRUE"
2025-01-12T21:07:01.021Z In(05) vmx DICT   gui.lastPoweredViewMode = "fullscreen"
2025-01-12T21:07:01.021Z In(05) vmx DICT    usb_xhci.pciSlotNumber = "160"
2025-01-12T21:07:01.021Z In(05) vmx DICT        checkpoint.vmState = "Kali 2024 x64 Customized by zSecurity v1.2-f65b28b0.vmss"
2025-01-12T21:07:01.021Z In(05) vmx DICT      gui.stretchGuestMode = "fullfill"
2025-01-12T21:07:01.021Z In(05) vmx DICT       tools.remindInstall = "FALSE"
2025-01-12T21:07:01.021Z In(05) vmx DICT        usb_xhci:4.present = "TRUE"
2025-01-12T21:07:01.021Z In(05) vmx DICT     usb_xhci:4.deviceType = "hid"
2025-01-12T21:07:01.021Z In(05) vmx DICT           usb_xhci:4.port = "4"
2025-01-12T21:07:01.021Z In(05) vmx DICT         usb_xhci:4.parent = "-1"
2025-01-12T21:07:01.021Z In(05) vmx DICT          usb_xhci:6.speed = "2"
2025-01-12T21:07:01.021Z In(05) vmx DICT        usb_xhci:6.present = "TRUE"
2025-01-12T21:07:01.021Z In(05) vmx DICT     usb_xhci:6.deviceType = "hub"
2025-01-12T21:07:01.021Z In(05) vmx DICT           usb_xhci:6.port = "6"
2025-01-12T21:07:01.021Z In(05) vmx DICT         usb_xhci:6.parent = "-1"
2025-01-12T21:07:01.021Z In(05) vmx DICT          usb_xhci:7.speed = "4"
2025-01-12T21:07:01.021Z In(05) vmx DICT        usb_xhci:7.present = "TRUE"
2025-01-12T21:07:01.021Z In(05) vmx DICT     usb_xhci:7.deviceType = "hub"
2025-01-12T21:07:01.021Z In(05) vmx DICT           usb_xhci:7.port = "7"
2025-01-12T21:07:01.021Z In(05) vmx DICT         usb_xhci:7.parent = "-1"
2025-01-12T21:07:01.021Z In(05) vmx DICT --- USER DEFAULTS C:\Users\<USER>\AppData\Roaming\VMware\config.ini 
2025-01-12T21:07:01.021Z In(05) vmx DICT --- HOST DEFAULTS C:\ProgramData\VMware\VMware Workstation\config.ini 
2025-01-12T21:07:01.021Z In(05) vmx DICT         authd.client.port = "902"
2025-01-12T21:07:01.021Z In(05) vmx DICT           authd.proxy.nfc = "vmware-hostd:ha-nfc"
2025-01-12T21:07:01.021Z In(05) vmx DICT installerDefaults.autoSoftwareUpdateEnabled = "yes"
2025-01-12T21:07:01.021Z In(05) vmx DICT installerDefaults.autoSoftwareUpdateEnabled.epoch = "5203"
2025-01-12T21:07:01.021Z In(05) vmx DICT installerDefaults.componentDownloadEnabled = "yes"
2025-01-12T21:07:01.021Z In(05) vmx DICT installerDefaults.dataCollectionEnabled = "yes"
2025-01-12T21:07:01.021Z In(05) vmx DICT installerDefaults.dataCollectionEnabled.epoch = "5203"
2025-01-12T21:07:01.021Z In(05) vmx DICT --- SITE DEFAULTS C:\ProgramData\VMware\VMware Workstation\config.ini 
2025-01-12T21:07:01.021Z In(05) vmx DICT         authd.client.port = "902"
2025-01-12T21:07:01.021Z In(05) vmx DICT           authd.proxy.nfc = "vmware-hostd:ha-nfc"
2025-01-12T21:07:01.021Z In(05) vmx DICT installerDefaults.autoSoftwareUpdateEnabled = "yes"
2025-01-12T21:07:01.021Z In(05) vmx DICT installerDefaults.autoSoftwareUpdateEnabled.epoch = "5203"
2025-01-12T21:07:01.021Z In(05) vmx DICT installerDefaults.componentDownloadEnabled = "yes"
2025-01-12T21:07:01.021Z In(05) vmx DICT installerDefaults.dataCollectionEnabled = "yes"
2025-01-12T21:07:01.021Z In(05) vmx DICT installerDefaults.dataCollectionEnabled.epoch = "5203"
2025-01-12T21:07:01.021Z In(05) vmx DICT --- GLOBAL SETTINGS C:\ProgramData\VMware\VMware Workstation\settings.ini 
2025-01-12T21:07:01.021Z In(05) vmx DICT          printers.enabled = "FALSE"
2025-01-12T21:07:01.021Z In(05) vmx VMXSTATS: Registering 1 stats: vmx.diskLibDataVmdkOpenTime
2025-01-12T21:07:01.021Z In(05) vmx VMXSTATS: Registering 2 stats: vmx.diskLibDataVmdkOpenTime
2025-01-12T21:07:01.021Z In(05) vmx VMXSTATS: Registering 3 stats: vmx.diskLibDataVmdkGrowTime
2025-01-12T21:07:01.021Z In(05) vmx VMXSTATS: Registering 4 stats: vmx.diskLibDataVmdkGrowTime
2025-01-12T21:07:01.021Z In(05) vmx VMXSTATS: Registering 5 stats: vmx.diskLibDigestVmdkOpenTime
2025-01-12T21:07:01.021Z In(05) vmx VMXSTATS: Registering 6 stats: vmx.diskLibDigestVmdkOpenTime
2025-01-12T21:07:01.021Z In(05) vmx VMXSTATS: Registering 7 stats: vmx.diskLibDigestVmdkGrowTime
2025-01-12T21:07:01.021Z In(05) vmx VMXSTATS: Registering 8 stats: vmx.diskLibDigestVmdkGrowTime
2025-01-12T21:07:01.021Z In(05) vmx VMXSTATS: Registering 9 stats: vmx.diskLibDigestFileDataGrowTime
2025-01-12T21:07:01.021Z In(05) vmx VMXSTATS: Registering 10 stats: vmx.diskLibDigestFileDataGrowTime
2025-01-12T21:07:01.021Z In(05) vmx VMXSTATS: Registering 11 stats: vmx.digestLibOpenIntTime
2025-01-12T21:07:01.021Z In(05) vmx VMXSTATS: Registering 12 stats: vmx.digestLibOpenIntTime
2025-01-12T21:07:01.021Z In(05) vmx VMXSTATS: Registering 13 stats: vmx.diskLibDataVmdkCloseTime
2025-01-12T21:07:01.021Z In(05) vmx VMXSTATS: Registering 14 stats: vmx.diskLibDataVmdkCloseTime
2025-01-12T21:07:01.021Z In(05) vmx VMXSTATS: Registering 15 stats: vmx.diskLibDigestVmdkCloseTime
2025-01-12T21:07:01.021Z In(05) vmx VMXSTATS: Registering 16 stats: vmx.diskLibDigestVmdkCloseTime
2025-01-12T21:07:01.021Z In(05) vmx VMXSTATS: Registering 17 stats: vmx.diskLibVmdkCreateTime
2025-01-12T21:07:01.021Z In(05) vmx VMXSTATS: Registering 18 stats: vmx.diskLibVmdkCreateTime
2025-01-12T21:07:01.021Z In(05) vmx VMXSTATS: Registering 19 stats: vmx.diskLibChildVmdkCreateTime
2025-01-12T21:07:01.021Z In(05) vmx VMXSTATS: Registering 20 stats: vmx.diskLibChildVmdkCreateTime
2025-01-12T21:07:01.021Z In(05) vmx VMXSTATS: Registering 21 stats: vmx.snapshotCreateTime
2025-01-12T21:07:01.021Z In(05) vmx VMXSTATS: Registering 22 stats: vmx.snapshotCreateTime
2025-01-12T21:07:01.021Z In(05) vmx VMXSTATS: Registering 23 stats: vmx.snapshotCreateQuiescedTime
2025-01-12T21:07:01.021Z In(05) vmx VMXSTATS: Registering 24 stats: vmx.snapshotCreateQuiescedTime
2025-01-12T21:07:01.021Z In(05) vmx VMXSTATS: Registering 25 stats: vmx.snapshotCreateMemoryTime
2025-01-12T21:07:01.021Z In(05) vmx VMXSTATS: Registering 26 stats: vmx.snapshotCreateMemoryTime
2025-01-12T21:07:01.021Z In(05) vmx VMXSTATS: Registering 27 stats: vmx.snapshotDeleteTime
2025-01-12T21:07:01.021Z In(05) vmx VMXSTATS: Registering 28 stats: vmx.snapshotDeleteTime
2025-01-12T21:07:01.021Z In(05) vmx VMXSTATS: Registering 29 stats: vmx.snapshotConsolidateTime
2025-01-12T21:07:01.021Z In(05) vmx VMXSTATS: Registering 30 stats: vmx.snapshotConsolidateTime
2025-01-12T21:07:01.021Z In(05) vmx VMXSTATS: Registering 31 stats: vmx.checkpointStunTime
2025-01-12T21:07:01.021Z In(05) vmx VMXSTATS: Registering 32 stats: vmx.checkpointStunTime
2025-01-12T21:07:01.021Z In(05) vmx VMXSTATS: Registering 33 stats: vmx.setPolicyTime
2025-01-12T21:07:01.021Z In(05) vmx VMXSTATS: Registering 34 stats: vmx.setPolicyTime
2025-01-12T21:07:01.021Z In(05) vmx VMXSTATS: Registering 35 stats: vmx.filtlibApplyDiskConfigTime
2025-01-12T21:07:01.021Z In(05) vmx VMXSTATS: Registering 36 stats: vmx.filtlibApplyDiskConfigTime
2025-01-12T21:07:01.021Z In(05) vmx VMXSTATS: Registering 37 stats: vmx.diskLibGetInfoTime
2025-01-12T21:07:01.021Z In(05) vmx VMXSTATS: Registering 38 stats: vmx.diskLibGetInfoTime
2025-01-12T21:07:01.021Z In(05) vmx VMXSTATS: Registering 39 stats: vmx.diskLibDigestGetInfoTime
2025-01-12T21:07:01.021Z In(05) vmx VMXSTATS: Registering 40 stats: vmx.diskLibDigestGetInfoTime
2025-01-12T21:07:01.021Z In(05) vmx Powering on guestOS 'debian12-64' using the configuration for 'debian12-64'.
2025-01-12T21:07:01.026Z In(05) vmx ToolsISO: open of C:\Program Files (x86)\VMware\VMware Workstation\isoimages_manifest.txt.sig failed: Could not find the file
2025-01-12T21:07:01.026Z In(05) vmx ToolsISO: Unable to read signature file 'C:\Program Files (x86)\VMware\VMware Workstation\isoimages_manifest.txt.sig', ignoring.
2025-01-12T21:07:01.026Z In(05) vmx ToolsISO: Updated cached value for imageName to 'linux.iso'.
2025-01-12T21:07:01.026Z In(05) vmx ToolsISO: Selected Tools ISO 'linux.iso' for 'debian12-64' guest.
2025-01-12T21:07:01.046Z In(05) vmx DUMPER: Restoring checkpoint version 8.
2025-01-12T21:07:01.046Z In(05) vmx Checkpointed in VMware Workstation, 17.5.2, build-23775571, Windows Host
2025-01-12T21:07:01.046Z In(05) vmx Resuming virtual machine from C:\Users\<USER>\Desktop\Kali 2024 x64 Customized by zSecurity v1.2.vmwarevm\Kali 2024 x64 Customized by zSecurity v1.2-f65b28b0.vmss with 8192 MB of memory.
2025-01-12T21:07:01.046Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=1, newAppState=1873, success=1 additionalError=0
2025-01-12T21:07:01.046Z In(05) vmx DEVSWAP: GuestOS does not require LSI adapter swap.
2025-01-12T21:07:01.048Z In(05) vmx Monitor Mode: CPL0
2025-01-12T21:07:01.058Z In(05) vmx OvhdMem_PowerOn: initial admission: paged   674445 nonpaged     7729 anonymous    11823
2025-01-12T21:07:01.058Z In(05) vmx VMMEM: Initial Reservation: 2710MB (MainMem=8192MB)
2025-01-12T21:07:01.058Z In(05) vmx numa: Resuming from checkpoint using VPD = 2
2025-01-12T21:07:01.058Z In(05) vmx llc: maximum vcpus per LLC: 1
2025-01-12T21:07:01.058Z In(05) vmx llc: vLLC size: 2
2025-01-12T21:07:01.058Z In(05) vmx MemSched_PowerOn: balloon minGuestSize 209715 (80% of min required size 262144)
2025-01-12T21:07:01.058Z In(05) vmx MemSched: reserved mem (in MB) min 128 max 6200 recommended 6200
2025-01-12T21:07:01.058Z In(05) vmx MemSched: pg 674445 np 7729 anon 11823 mem 2097152
2025-01-12T21:07:01.312Z In(05) vmx MemSched: numvm 1 locked pages: num 0 max 1579008
2025-01-12T21:07:01.312Z In(05) vmx MemSched: locked Page Limit: host 1681740 config 1587200
2025-01-12T21:07:01.312Z In(05) vmx MemSched: minmempct 50 minalloc 0 admitted 1
2025-01-12T21:07:01.312Z No(00) vmx PowerOnTiming: Module MemSched took 253387 us
2025-01-12T21:07:01.324Z In(05) PowerNotifyThread VTHREAD 9336 "PowerNotifyThread"
2025-01-12T21:07:01.324Z In(05) PowerNotifyThread PowerNotify thread is alive.
2025-01-12T21:07:01.324Z In(05) vmx VMXSTATS: Registering 41 stats: vmx.logBytesDropped
2025-01-12T21:07:01.324Z In(05) vmx VMXSTATS: Registering 42 stats: vmx.logMsgsDropped
2025-01-12T21:07:01.324Z In(05) vmx VMXSTATS: Registering 43 stats: vmx.logBytesLogged
2025-01-12T21:07:01.324Z In(05) vmx VMXSTATS: Registering 44 stats: vmx.logWriteMinMaxTime
2025-01-12T21:07:01.324Z In(05) vmx VMXSTATS: Registering 45 stats: vmx.logWriteAvgTime
2025-01-12T21:07:01.324Z In(05) vmx LICENSE: Running unlicensed VMX (VMware Workstation)
2025-01-12T21:07:01.326Z In(05) vthread-11200 VTHREAD 11200 "vthread-11200"
2025-01-12T21:07:01.328Z In(05) vmx Win32U_GetFileAttributes: GetFileAttributesExW("C:\Users\<USER>\Desktop\Kali 2024 x64 Customized by zSecurity v1.2.vmwarevm\Kali 2024 x64 Customized by zSecurity v1.2.vmpl", ...) failed, error: 2
2025-01-12T21:07:01.328Z In(05) vmx PolicyVMXFindPolicyKey: policy file does not exist.
2025-01-12T21:07:01.328Z In(05) vmx Win32U_GetFileAttributes: GetFileAttributesExW("C:\Users\<USER>\Desktop\Kali 2024 x64 Customized by zSecurity v1.2.vmwarevm\Kali 2024 x64 Customized by zSecurity v1.2.vmpl", ...) failed, error: 2
2025-01-12T21:07:01.328Z In(05) vmx PolicyVMXFindPolicyKey: policy file does not exist.
2025-01-12T21:07:01.330Z In(05) vmx Host PA size: 39 bits. Guest PA size: 45 bits.
2025-01-12T21:07:01.332Z In(05) vmx ToolsISO: Refreshing imageName for 'debian12-64' (refreshCount=1, lastCount=1).
2025-01-12T21:07:01.334Z In(05) vmx ToolsISO: open of C:\Program Files (x86)\VMware\VMware Workstation\isoimages_manifest.txt.sig failed: Could not find the file
2025-01-12T21:07:01.334Z In(05) vmx ToolsISO: Unable to read signature file 'C:\Program Files (x86)\VMware\VMware Workstation\isoimages_manifest.txt.sig', ignoring.
2025-01-12T21:07:01.334Z In(05) vmx ToolsISO: Updated cached value for imageName to 'linux.iso'.
2025-01-12T21:07:01.334Z In(05) vmx ToolsISO: Selected Tools ISO 'linux.iso' for 'debian12-64' guest.
2025-01-12T21:07:01.336Z In(05) deviceThread VTHREAD 2816 "deviceThread"
2025-01-12T21:07:01.336Z In(05) deviceThread Device thread is alive
2025-01-12T21:07:01.336Z In(05) vmx Host VT-x Capabilities:
2025-01-12T21:07:01.336Z In(05) vmx Basic VMX Information (0x00da050000000013)
2025-01-12T21:07:01.336Z In(05) vmx   VMCS revision ID                          19
2025-01-12T21:07:01.336Z In(05) vmx   VMCS region length                      1280
2025-01-12T21:07:01.336Z In(05) vmx   VMX physical-address width           natural
2025-01-12T21:07:01.336Z In(05) vmx   SMM dual-monitor mode                    yes
2025-01-12T21:07:01.336Z In(05) vmx   VMCS memory type                          WB
2025-01-12T21:07:01.336Z In(05) vmx   Advanced INS/OUTS info                   yes
2025-01-12T21:07:01.336Z In(05) vmx   True VMX MSRs                            yes
2025-01-12T21:07:01.336Z In(05) vmx   Exception Injection ignores error code    no
2025-01-12T21:07:01.336Z In(05) vmx True Pin-Based VM-Execution Controls (0x000000ff00000016)
2025-01-12T21:07:01.336Z In(05) vmx   External-interrupt exiting               {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   NMI exiting                              {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   Virtual NMIs                             {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   Activate VMX-preemption timer            {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   Process posted interrupts                {0,1}
2025-01-12T21:07:01.336Z In(05) vmx True Primary Processor-Based VM-Execution Controls (0xfff9fffe04006172)
2025-01-12T21:07:01.336Z In(05) vmx   Interrupt-window exiting                 {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   Use TSC offsetting                       {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   HLT exiting                              {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   INVLPG exiting                           {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   MWAIT exiting                            {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   RDPMC exiting                            {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   RDTSC exiting                            {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   CR3-load exiting                         {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   CR3-store exiting                        {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   Activate tertiary controls               { 0 }
2025-01-12T21:07:01.336Z In(05) vmx   CR8-load exiting                         {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   CR8-store exiting                        {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   Use TPR shadow                           {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   NMI-window exiting                       {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   MOV-DR exiting                           {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   Unconditional I/O exiting                {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   Use I/O bitmaps                          {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   Monitor trap flag                        {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   Use MSR bitmaps                          {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   MONITOR exiting                          {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   PAUSE exiting                            {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   Activate secondary controls              {0,1}
2025-01-12T21:07:01.336Z In(05) vmx Secondary Processor-Based VM-Execution Controls (0x335fbfff00000000)
2025-01-12T21:07:01.336Z In(05) vmx   Virtualize APIC accesses                 {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   Enable EPT                               {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   Descriptor-table exiting                 {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   Enable RDTSCP                            {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   Virtualize x2APIC mode                   {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   Enable VPID                              {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   WBINVD exiting                           {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   Unrestricted guest                       {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   APIC-register virtualization             {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   Virtual-interrupt delivery               {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   PAUSE-loop exiting                       {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   RDRAND exiting                           {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   Enable INVPCID                           {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   Enable VM Functions                      {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   Use VMCS shadowing                       { 0 }
2025-01-12T21:07:01.336Z In(05) vmx   ENCLS exiting                            {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   RDSEED exiting                           {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   Enable PML                               {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   EPT-violation #VE                        {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   Conceal VMX from PT                      {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   Enable XSAVES/XRSTORS                    {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   PASID translation                        { 0 }
2025-01-12T21:07:01.336Z In(05) vmx   Mode-based execute control for EPT       {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   Sub-page write permissions for EPT       { 0 }
2025-01-12T21:07:01.336Z In(05) vmx   PT uses guest physical addresses         {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   Use TSC scaling                          {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   Enable UMWAIT and TPAUSE                 { 0 }
2025-01-12T21:07:01.336Z In(05) vmx   Enable ENCLV in VMX non-root mode        {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   Enable EPC Virtualization Extensions     {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   Bus lock exiting                         { 0 }
2025-01-12T21:07:01.336Z In(05) vmx   Notification VM exits                    { 0 }
2025-01-12T21:07:01.336Z In(05) vmx Tertiary Processor-Based VM-Execution Controls (0x0000000000000000)
2025-01-12T21:07:01.336Z In(05) vmx   LOADIWKEY exiting                          no
2025-01-12T21:07:01.336Z In(05) vmx   Enable HLAT                                no
2025-01-12T21:07:01.336Z In(05) vmx   Enable Paging-Write                        no
2025-01-12T21:07:01.336Z In(05) vmx   Enable Guest Paging Verification           no
2025-01-12T21:07:01.336Z In(05) vmx   Enable IPI Virtualization                  no
2025-01-12T21:07:01.336Z In(05) vmx   Enable Virtual MSR_SPEC_CTRL               no
2025-01-12T21:07:01.336Z In(05) vmx True VM-Exit Controls (0x037fffff00036dfb)
2025-01-12T21:07:01.336Z In(05) vmx   Save debug controls                      {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   Host address-space size                  {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   Load IA32_PERF_GLOBAL_CTRL               {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   Acknowledge interrupt on exit            {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   Save IA32_PAT                            {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   Load IA32_PAT                            {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   Save IA32_EFER                           {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   Load IA32_EFER                           {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   Save VMX-preemption timer                {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   Clear IA32_BNDCFGS                       { 0 }
2025-01-12T21:07:01.336Z In(05) vmx   Conceal VMX from processor trace         {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   Clear IA32_RTIT MSR                      {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   Clear IA32_LBR_CTL MSR                   { 0 }
2025-01-12T21:07:01.336Z In(05) vmx   Clear user-interrupt notification vector { 0 }
2025-01-12T21:07:01.336Z In(05) vmx   Load CET state                           { 0 }
2025-01-12T21:07:01.336Z In(05) vmx   Load PKRS                                { 0 }
2025-01-12T21:07:01.336Z In(05) vmx True VM-Entry Controls (0x0006ffff000011fb)
2025-01-12T21:07:01.336Z In(05) vmx   Load debug controls                      {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   IA-32e mode guest                        {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   Entry to SMM                             {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   Deactivate dual-monitor mode             {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   Load IA32_PERF_GLOBAL_CTRL               {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   Load IA32_PAT                            {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   Load IA32_EFER                           {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   Load IA32_BNDCFGS                        { 0 }
2025-01-12T21:07:01.336Z In(05) vmx   Conceal VMX from processor trace         {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   Load IA32_RTIT MSR                       {0,1}
2025-01-12T21:07:01.336Z In(05) vmx   Load user-interrupt notification vector  { 0 }
2025-01-12T21:07:01.336Z In(05) vmx   Load CET state                           { 0 }
2025-01-12T21:07:01.336Z In(05) vmx   Load IA32_LBR_CTL MSR                    { 0 }
2025-01-12T21:07:01.336Z In(05) vmx   Load PKRS                                { 0 }
2025-01-12T21:07:01.336Z In(05) vmx VPID and EPT Capabilities (0x00000f0106734141)
2025-01-12T21:07:01.336Z In(05) vmx   R=0/W=0/X=1                               yes
2025-01-12T21:07:01.336Z In(05) vmx   Page-walk length 3                        yes
2025-01-12T21:07:01.336Z In(05) vmx   EPT memory type WB                        yes
2025-01-12T21:07:01.336Z In(05) vmx   2MB super-page                            yes
2025-01-12T21:07:01.336Z In(05) vmx   1GB super-page                            yes
2025-01-12T21:07:01.336Z In(05) vmx   INVEPT support                            yes
2025-01-12T21:07:01.336Z In(05) vmx   Access & Dirty Bits                       yes
2025-01-12T21:07:01.336Z In(05) vmx   Advanced VM exit information for EPT violations   yes
2025-01-12T21:07:01.336Z In(05) vmx   Supervisor shadow-stack control            no
2025-01-12T21:07:01.336Z In(05) vmx   Type 1 INVEPT                             yes
2025-01-12T21:07:01.336Z In(05) vmx   Type 2 INVEPT                             yes
2025-01-12T21:07:01.336Z In(05) vmx   INVVPID support                           yes
2025-01-12T21:07:01.336Z In(05) vmx   Type 0 INVVPID                            yes
2025-01-12T21:07:01.336Z In(05) vmx   Type 1 INVVPID                            yes
2025-01-12T21:07:01.336Z In(05) vmx   Type 2 INVVPID                            yes
2025-01-12T21:07:01.336Z In(05) vmx   Type 3 INVVPID                            yes
2025-01-12T21:07:01.336Z In(05) vmx Miscellaneous VMX Data (0x000000007004c1e7)
2025-01-12T21:07:01.336Z In(05) vmx   TSC to preemption timer ratio      7
2025-01-12T21:07:01.336Z In(05) vmx   VM-Exit saves EFER.LMA           yes
2025-01-12T21:07:01.336Z In(05) vmx   Activity State HLT               yes
2025-01-12T21:07:01.336Z In(05) vmx   Activity State shutdown          yes
2025-01-12T21:07:01.336Z In(05) vmx   Activity State wait-for-SIPI     yes
2025-01-12T21:07:01.336Z In(05) vmx   Processor trace in VMX           yes
2025-01-12T21:07:01.336Z In(05) vmx   RDMSR SMBASE MSR in SMM          yes
2025-01-12T21:07:01.336Z In(05) vmx   CR3 targets supported              4
2025-01-12T21:07:01.336Z In(05) vmx   Maximum MSR list size            512
2025-01-12T21:07:01.336Z In(05) vmx   VMXOFF holdoff of SMIs           yes
2025-01-12T21:07:01.336Z In(05) vmx   Allow all VMWRITEs               yes
2025-01-12T21:07:01.336Z In(05) vmx   Allow zero instruction length    yes
2025-01-12T21:07:01.336Z In(05) vmx   MSEG revision ID                   0
2025-01-12T21:07:01.336Z In(05) vmx VMX-Fixed Bits in CR0 (0x0000000080000021/0x00000000ffffffff)
2025-01-12T21:07:01.336Z In(05) vmx   Fixed to 0        0xffffffff00000000
2025-01-12T21:07:01.336Z In(05) vmx   Fixed to 1        0x0000000080000021
2025-01-12T21:07:01.336Z In(05) vmx   Variable          0x000000007fffffde
2025-01-12T21:07:01.336Z In(05) vmx VMX-Fixed Bits in CR4 (0x0000000000002000/0x0000000000772fff)
2025-01-12T21:07:01.336Z In(05) vmx   Fixed to 0        0xffffffffff88d000
2025-01-12T21:07:01.336Z In(05) vmx   Fixed to 1        0x0000000000002000
2025-01-12T21:07:01.336Z In(05) vmx   Variable          0x0000000000770fff
2025-01-12T21:07:01.336Z In(05) vmx VMCS Enumeration (0x000000000000002e)
2025-01-12T21:07:01.336Z In(05) vmx   Highest index                   0x17
2025-01-12T21:07:01.336Z In(05) vmx VM Functions (0x0000000000000001)
2025-01-12T21:07:01.336Z In(05) vmx   Function  0 (EPTP-switching) supported.
2025-01-12T21:07:01.336Z In(05) vmx Monitor_PowerOn: hostedVSMPMaxSkew is 1500 us (1785586 cycles)
2025-01-12T21:07:01.336Z In(05) vmx vmm-modules: [vmm.vmm, vmce-vmce.vmm, viommu-none.vmm, vprobe-none.vmm, hv-vt.vmm, gphys-ept.vmm, callstack-none.vmm, !tdxSharedVMData=0x0, !vmSamples=0x0, !theIOSpace=0x40, !ttGPPerVcpu=0x6e40, {UseUnwind}=0x0, numVCPUsAsAddr=0x2, {SharedAreaReservations}=0x6e80, {rodataSize}=0x20a60, {textAddr}=0xfffffffffc000000, {textSize}=0x8eab9, <MonSrcFile>]
2025-01-12T21:07:01.336Z In(05) vmx vmm-vcpus:   2
2025-01-12T21:07:01.428Z In(05) vmx KHZEstimate 1190391
2025-01-12T21:07:01.428Z In(05) vmx MHZEstimate 1190
2025-01-12T21:07:01.428Z In(05) vmx NumVCPUs 2
2025-01-12T21:07:01.432Z In(05) vmx AIOGNRC: numThreads=18 ide=0, scsi=1, passthru=1
2025-01-12T21:07:01.432Z In(05) vmx WORKER: Creating new group with maxThreads=18 (18)
2025-01-12T21:07:01.458Z In(05) vmx WORKER: Creating new group with maxThreads=1 (19)
2025-01-12T21:07:01.458Z In(05) vmx MainMem: CPT Host WZ=0 PF=8192 D=0
2025-01-12T21:07:01.458Z In(05) vmx MainMem: CPT PLS=1 PLR=1 BS=1 BlkP=32 Mult=4 W=50
2025-01-12T21:07:01.458Z In(05) vmx MStat: Creating Stat vm.uptime
2025-01-12T21:07:01.458Z In(05) vmx MStat: Creating Stat vm.suspendTime
2025-01-12T21:07:01.458Z In(05) vmx MStat: Creating Stat vm.powerOnTimeStamp
2025-01-12T21:07:01.458Z In(05) aioCompletion VTHREAD 10456 "aioCompletion"
2025-01-12T21:07:01.458Z In(05) vmx VMXAIOMGR: Using: simple=Compl
2025-01-12T21:07:01.471Z In(05) vmx WORKER: Creating new group with maxThreads=1 (20)
2025-01-12T21:07:01.484Z In(05) vmx WORKER: Creating new group with maxThreads=1 (21)
2025-01-12T21:07:01.484Z In(05) vmx WORKER: Creating new group with maxThreads=14 (35)
2025-01-12T21:07:01.487Z In(05) vmx FeatureCompat: No VM masks.
2025-01-12T21:07:01.487Z In(05) vmx TimeTracker host to guest rate conversion 707002229 @ 1190391000Hz -> 0 @ 1190391000Hz
2025-01-12T21:07:01.487Z In(05) vmx TimeTracker host to guest rate conversion ((x * 2147483648) >> 31) + -707002229
2025-01-12T21:07:01.487Z In(05) vmx TSC scaling enabled.
2025-01-12T21:07:01.487Z In(05) vmx TSC offsetting enabled.
2025-01-12T21:07:01.487Z In(05) vmx timeTracker.globalProgressMaxAllowanceMS: 2000
2025-01-12T21:07:01.487Z In(05) vmx timeTracker.globalProgressToAllowanceNS: 1000
2025-01-12T21:07:01.487Z In(05) vmx MKS PowerOn
2025-01-12T21:07:01.491Z In(05) mks VTHREAD 10560 "mks"
2025-01-12T21:07:01.491Z In(05) mks MKS thread is alive
2025-01-12T21:07:01.491Z In(05) svga VTHREAD 1940 "svga"
2025-01-12T21:07:01.491Z In(05) svga SVGA thread is alive
2025-01-12T21:07:01.493Z In(05) mks MKS: SSE2=1, SSSE3=1, SSE4_1=1
2025-01-12T21:07:01.582Z In(05) mouse VTHREAD 3392 "mouse"
2025-01-12T21:07:01.582Z In(05) mks MKS-HookKeyboard: RegQueryValueEx(LowLevelHooksTimeout) failed: The system cannot find the file specified (2)
2025-01-12T21:07:01.582Z In(05) kbh VTHREAD 14388 "kbh"
2025-01-12T21:07:01.582Z In(05) mks MKS Win32: Registering top level window (0x507e4) to receive session change notification.
2025-01-12T21:07:01.582Z In(05) mks Current Display Settings:
2025-01-12T21:07:01.582Z In(05) mks    Display: 0 size: 1920x1080  position: (0, 0) name: \\.\DISPLAY1  
2025-01-12T21:07:01.582Z In(05) mks MKS Win32: MIL: 0x4000
2025-01-12T21:07:01.582Z In(05) mks MKS-RenderMain: PowerOn allowed MKSBasicOps 
2025-01-12T21:07:01.582Z In(05) mks MKS-RenderMain: ISB enabled by config
2025-01-12T21:07:01.582Z In(05) mks MKS-RenderMain: Collecting RenderOps caps from MKSBasicOps
2025-01-12T21:07:01.582Z In(05) mks MKS-RenderMain: Starting MKSBasicOps
2025-01-12T21:07:01.582Z In(05) mks MKS-RenderMain: Started MKSBasicOps
2025-01-12T21:07:01.582Z In(05) mks MKS-RenderMain: Found Full Renderer: MKSBasicOps
2025-01-12T21:07:01.582Z In(05) mks MKS-RenderMain: maxTextureSize=32768
2025-01-12T21:07:01.595Z In(05) mks KHBKL: Unable to parse keystring at: ''
2025-01-12T21:07:01.595Z In(05) mks MKSRemoteMgr: Set default display name: Kali 2024 x64 Customized by zSecurity v1.2
2025-01-12T21:07:01.595Z In(05) mks MKSRemoteMgr: Loading VNC Configuration from VM config file
2025-01-12T21:07:01.595Z In(05) mks MKSRemoteMgr: Using default VNC keymap table "us"
2025-01-12T21:07:01.595Z In(05) vmx VLANCE: send cluster threshold is 80, size = 2 recalcInterval is 20000 us
2025-01-12T21:07:01.597Z In(05) vmx VMXNET: send cluster threshold is 80, size = 2 recalcInterval is 20000 ticks, dontClusterSize is 128
2025-01-12T21:07:01.597Z In(05) vmx Chipset version: 0x13
2025-01-12T21:07:01.597Z In(05) vmx SOUNDLIB: Creating the Wave sound backend.
2025-01-12T21:07:01.613Z No(00) vmx ConfigDB: Setting pciBridge4.pciSlotNumber = "-1"
2025-01-12T21:07:01.613Z No(00) vmx ConfigDB: Setting pciBridge5.pciSlotNumber = "-1"
2025-01-12T21:07:01.613Z No(00) vmx ConfigDB: Setting pciBridge6.pciSlotNumber = "-1"
2025-01-12T21:07:01.613Z No(00) vmx ConfigDB: Setting pciBridge7.pciSlotNumber = "-1"
2025-01-12T21:07:01.613Z No(00) vmx ConfigDB: Setting pciBridge4.pciSlotNumber = "21"
2025-01-12T21:07:01.613Z No(00) vmx ConfigDB: Setting pciBridge5.pciSlotNumber = "22"
2025-01-12T21:07:01.613Z No(00) vmx ConfigDB: Setting pciBridge6.pciSlotNumber = "23"
2025-01-12T21:07:01.613Z No(00) vmx ConfigDB: Setting pciBridge7.pciSlotNumber = "24"
2025-01-12T21:07:01.645Z In(05) vmx VMXSTATS: Registering 46 stats: vmx.configWriteMinMaxTime
2025-01-12T21:07:01.645Z In(05) vmx VMXSTATS: Registering 47 stats: vmx.configWriteAvgTime
2025-01-12T21:07:01.661Z In(05) vmx MigrateBusMemPrealloc: BusMem preallocation begins.
2025-01-12T21:07:01.661Z In(05) vmx MigrateBusMemPrealloc: BusMem preallocation completes.
2025-01-12T21:07:01.661Z No(00) vmx ConfigDB: Setting scsi0:0.redo = ""
2025-01-12T21:07:01.661Z In(05) vmx DISK: OPEN scsi0:0 'C:\Users\<USER>\Desktop\Kali 2024 x64 Customized by zSecurity v1.2.vmwarevm\disk-000001.vmdk' persistent R[]
2025-01-12T21:07:01.703Z In(05) vmx DiskGetGeometry: Reading of disk partition table
2025-01-12T21:07:01.705Z In(05) vmx DISK: Disk 'C:\Users\<USER>\Desktop\Kali 2024 x64 Customized by zSecurity v1.2.vmwarevm\disk-000001.vmdk' has UUID '60 00 c2 90 a6 e9 fb 42-7d ce 01 79 c5 a1 3d cd'
2025-01-12T21:07:01.705Z In(05) vmx DISK: OPEN 'C:\Users\<USER>\Desktop\Kali 2024 x64 Customized by zSecurity v1.2.vmwarevm\disk-000001.vmdk' Geo (10443/255/63) BIOS Geo (328965/255/2)
2025-01-12T21:07:01.709Z In(05) vmx DISKUTILWIN32: DiskUtilW32IsATASSDDevice: Failed to send ATA IDENTIFY command to the target device.
2025-01-12T21:07:01.709Z In(05) vmx DISKUTILWIN32: DiskUtilW32IsScsiSSDDevice: Reported rotation rate = 1
2025-01-12T21:07:01.709Z In(05) vmx DISK: DiskConfigureVirtualSSD:  Disk 'scsi0:0' identified as Virtual SSD device.
2025-01-12T21:07:01.709Z In(05) vmx DISK: Opening disks took 39 ms.
2025-01-12T21:07:01.709Z In(05) vmx USBArbLib: USBArbLib initialized successfully, retryIntervalStart(5), retryIntervalMax(120), arbSocketName(\\.\pipe\vmware-usbarbpipe), useLocking(yes), tryUpgrading(no).
2025-01-12T21:07:01.709Z In(05) vmx UsbEnum: Initializing UsbEnum library, disableLocking(no), allowBootableHid(yes).
2025-01-12T21:07:01.709Z In(05) vmx USB: Initializing 'Virtual Hub' backend
2025-01-12T21:07:01.709Z In(05) vmx USB: Initializing 'Generic' backend
2025-01-12T21:07:01.709Z Wa(03) vmx USBArbLib: OUT SET_AUTO_CONNECT: Not connected to arbitrator, autoconnect(0) for client 'Kali 2024 x64 Customized by zSecurity v1.2', connectState(1).
2025-01-12T21:07:01.709Z In(05) vmx USB: Initializing 'Virtual HID' backend
2025-01-12T21:07:01.709Z In(05) sensorThread VTHREAD 6196 "sensorThread"
2025-01-12T21:07:01.709Z In(05) vmx USB: Initializing 'Remote Device' backend
2025-01-12T21:07:01.709Z In(05) vmx RemoteUSBVMX: Retrieved hostId [e4 a0 5e f8 ba 74 21 20-09 27 21 58 15 00 00 00].
2025-01-12T21:07:01.709Z In(05) vmx RemoteUSBVMX: Protocol version min:15 current:19
2025-01-12T21:07:01.709Z In(05) vmx RemoteUSBVMX: no delay setting is TRUE.
2025-01-12T21:07:01.709Z In(05) vmx USB: Initializing 'Virtual Mass Storage' backend
2025-01-12T21:07:01.709Z In(05) vmx USB: Initializing 'Virtual RNG' backend
2025-01-12T21:07:01.709Z In(05) vmx USB: Initializing 'Virtual CCID' backend
2025-01-12T21:07:01.716Z In(05) vmx USB-CCID: Could not establish context: SCARD_E_NO_SERVICE(0x8010001d).
2025-01-12T21:07:01.718Z In(05) vmx USB-CCID: Could not establish context: SCARD_E_NO_SERVICE(0x8010001d).
2025-01-12T21:07:01.718Z In(05) usbCCIDEnumCards VTHREAD 15008 "usbCCIDEnumCards"
2025-01-12T21:07:01.718Z In(05) usbCCIDEnumCards USB-CCID: Card enum thread created.
2025-01-12T21:07:01.718Z In(05) vmx USB: Initializing 'Virtual Bluetooth' backend
2025-01-12T21:07:01.718Z In(05) vmx USB: Initializing 'Virtual Audio' backend
2025-01-12T21:07:01.718Z In(05) vmx USB: Initializing 'Virtual Video' backend
2025-01-12T21:07:01.732Z In(05) vmx Win32U_GetFileAttributes: GetFileAttributesExW("C:\Users\<USER>\Desktop\Kali 2024 x64 Customized by zSecurity v1.2.vmwarevm\Kali 2024 x64 Customized by zSecurity v1.2-Snapshot3.vmem", ...) failed, error: 2
2025-01-12T21:07:01.732Z Wa(03) vmx USBGW: IOCTL_STORAGE_QUERY_PROPERTY failed. Error(0x0): The operation completed successfully.
2025-01-12T21:07:01.732Z In(05) vmx USBGW: Skipping disk backing for path(C:\Users\<USER>\Desktop\Kali 2024 x64 Customized by zSecurity v1.2.vmwarevm\Kali 2024 x64 Customized by zSecurity v1.2.vmx).
2025-01-12T21:07:01.732Z Wa(03) vmx USBGW: IOCTL_STORAGE_QUERY_PROPERTY failed. Error(0x0): The operation completed successfully.
2025-01-12T21:07:01.732Z In(05) vmx USBGW: Skipping disk backing for path(C:\Users\<USER>\Desktop\Kali 2024 x64 Customized by zSecurity v1.2.vmwarevm\disk-000001.vmdk).
2025-01-12T21:07:01.732Z Wa(03) vmx USBGW: IOCTL_STORAGE_QUERY_PROPERTY failed. Error(0x0): The operation completed successfully.
2025-01-12T21:07:01.732Z In(05) vmx USBGW: Skipping disk backing for path(C:\Users\<USER>\Desktop\Kali 2024 x64 Customized by zSecurity v1.2.vmwarevm\disk.vmdk).
2025-01-12T21:07:01.732Z Wa(03) vmx USBGW: IOCTL_STORAGE_QUERY_PROPERTY failed. Error(0x0): The operation completed successfully.
2025-01-12T21:07:01.732Z In(05) vmx USBGW: Skipping disk backing for path(C:\Users\<USER>\Desktop\Kali 2024 x64 Customized by zSecurity v1.2.vmwarevm\disk.vmdk).
2025-01-12T21:07:01.732Z Wa(03) vmx USBGW: IOCTL_STORAGE_QUERY_PROPERTY failed. Error(0x0): The operation completed successfully.
2025-01-12T21:07:01.732Z In(05) vmx USBGW: Skipping disk backing for path(C:\Users\<USER>\Desktop\Kali 2024 x64 Customized by zSecurity v1.2.vmwarevm\Kali 2024 x64 Customized by zSecurity v1.2-Snapshot3.vmsn).
2025-01-12T21:07:01.732Z In(05) usbCCIDEnumCards USB-CCID: Could not establish context: SCARD_E_NO_SERVICE(0x8010001d).
2025-01-12T21:07:01.741Z In(05) vmx SCSI DEVICE (ide1:0): Computed value of ide1:0.useBounceBuffers: default
2025-01-12T21:07:01.741Z In(05) vmx DISKUTIL: ide1:0 : capacity=0 logical sector size=2048
2025-01-12T21:07:01.741Z In(05) vmx DISKUTIL: ide1:0 : geometry=0/0/0
2025-01-12T21:07:01.741Z In(05) vmx SCSI: scsi0: intr coalescing: on period=50msec cifTh=4 iopsTh=2000 hlt=0
2025-01-12T21:07:01.741Z In(05) vmx SCSI0: UNTAGGED commands will be converted to ORDER tags.
2025-01-12T21:07:01.741Z In(05) vmx SCSI DEVICE (scsi0:0): Computed value of scsi0:0.useBounceBuffers: default
2025-01-12T21:07:01.741Z In(05) vmx DISKUTIL: scsi0:0 : capacity=167772160 logical sector size=512
2025-01-12T21:07:01.741Z In(05) vmx DISKUTIL: scsi0:0 : geometry=328965/255/2
2025-01-12T21:07:01.741Z In(05) vmx SVGA-GFB: Config settings: autodetect=1, numDisplays=1, maxWidth=2560, maxHeight=1600
2025-01-12T21:07:01.741Z In(05) vmx SVGA-GFB: Desired maximum display topology: wh(6688, 5016)
2025-01-12T21:07:01.741Z In(05) vmx SVGA-GFB: Autodetected target gfbSize = 268435456
2025-01-12T21:07:01.741Z In(05) vmx SVGA-GFB: Using Initial       gfbSize = 4194304
2025-01-12T21:07:01.741Z In(05) vmx SVGA-GFB: MaxPrimaryMem      register = 268435456
2025-01-12T21:07:01.741Z In(05) vmx SVGA-GFB: Truncated maximum resolution for register modes to VRAM size: VRAM=4194304 bytes, max wh(1176, 885)
2025-01-12T21:07:01.741Z In(05) vmx SVGA-GFB: Max wh(1176, 885), number of displays: 10
2025-01-12T21:07:01.741Z In(05) vmx SVGA: SVGA DeviceLabel: svga2
2025-01-12T21:07:01.741Z No(00) vmx ConfigDB: Setting vmotion.svga.mobMaxSize = "268435456"
2025-01-12T21:07:01.741Z No(00) vmx ConfigDB: Setting vmotion.svga.graphicsMemoryKB = "262144"
2025-01-12T21:07:01.741Z In(05) vmx SVGA: mobMaxSize=268435456
2025-01-12T21:07:01.741Z In(05) vmx SVGA: graphicsMemoryKB=262144
2025-01-12T21:07:01.741Z In(05) vmx SVGA: FIFO capabilities 0x0000077f
2025-01-12T21:07:01.741Z In(05) vmx SVGAFeature renderer (before clamping) svga.supports3D bool 0
2025-01-12T21:07:01.741Z In(05) vmx SVGAFeature renderer (before clamping) svga.baseCapsLevel num 11
2025-01-12T21:07:01.741Z In(05) vmx SVGAFeature renderer (before clamping) svga.maxPointSize num 0
2025-01-12T21:07:01.741Z In(05) vmx SVGAFeature renderer (before clamping) svga.maxTextureSize num 32768
2025-01-12T21:07:01.741Z In(05) vmx SVGAFeature renderer (before clamping) svga.maxVolumeExtent num 0
2025-01-12T21:07:01.741Z In(05) vmx SVGAFeature renderer (before clamping) svga.maxTextureAnisotropy num 0
2025-01-12T21:07:01.741Z In(05) vmx SVGAFeature renderer (before clamping) svga.lineStipple bool 0
2025-01-12T21:07:01.741Z In(05) vmx SVGAFeature renderer (before clamping) svga.dxMaxConstantBuffers num 0
2025-01-12T21:07:01.741Z In(05) vmx SVGAFeature renderer (before clamping) svga.dxProvokingVertex bool 0
2025-01-12T21:07:01.741Z In(05) vmx SVGAFeature renderer (before clamping) svga.sm41 bool 0
2025-01-12T21:07:01.741Z In(05) vmx SVGAFeature renderer (before clamping) svga.multisample2x bool 0
2025-01-12T21:07:01.741Z In(05) vmx SVGAFeature renderer (before clamping) svga.multisample4x bool 0
2025-01-12T21:07:01.741Z In(05) vmx SVGAFeature renderer (before clamping) svga.msFullQuality bool 0
2025-01-12T21:07:01.741Z In(05) vmx SVGAFeature renderer (before clamping) svga.logicOps bool 0
2025-01-12T21:07:01.741Z In(05) vmx SVGAFeature renderer (before clamping) svga.bc67 num 0
2025-01-12T21:07:01.741Z In(05) vmx SVGAFeature renderer (before clamping) svga.sm5 bool 0
2025-01-12T21:07:01.741Z In(05) vmx SVGAFeature renderer (before clamping) svga.multisample8x bool 0
2025-01-12T21:07:01.741Z In(05) vmx SVGAFeature renderer (before clamping) svga.logicBlendOps bool 0
2025-01-12T21:07:01.741Z In(05) vmx SVGAFeature renderer (before clamping) svga.maxForcedSampleCount num 0
2025-01-12T21:07:01.741Z In(05) vmx SVGAFeature renderer (before clamping) svga.gl43 bool 0
2025-01-12T21:07:01.741Z In(05) vmx SVGAFeature renderer (after  clamping) svga.supports3D bool 0
2025-01-12T21:07:01.741Z In(05) vmx SVGAFeature renderer (after  clamping) svga.baseCapsLevel num 0
2025-01-12T21:07:01.741Z In(05) vmx SVGAFeature renderer (after  clamping) svga.maxPointSize num 0
2025-01-12T21:07:01.741Z In(05) vmx SVGAFeature renderer (after  clamping) svga.maxTextureSize num 0
2025-01-12T21:07:01.741Z In(05) vmx SVGAFeature renderer (after  clamping) svga.maxVolumeExtent num 0
2025-01-12T21:07:01.741Z In(05) vmx SVGAFeature renderer (after  clamping) svga.maxTextureAnisotropy num 0
2025-01-12T21:07:01.741Z In(05) vmx SVGAFeature renderer (after  clamping) svga.lineStipple bool 0
2025-01-12T21:07:01.741Z In(05) vmx SVGAFeature renderer (after  clamping) svga.dxMaxConstantBuffers num 0
2025-01-12T21:07:01.741Z In(05) vmx SVGAFeature renderer (after  clamping) svga.dxProvokingVertex bool 0
2025-01-12T21:07:01.741Z In(05) vmx SVGAFeature renderer (after  clamping) svga.sm41 bool 0
2025-01-12T21:07:01.741Z In(05) vmx SVGAFeature renderer (after  clamping) svga.multisample2x bool 0
2025-01-12T21:07:01.741Z In(05) vmx SVGAFeature renderer (after  clamping) svga.multisample4x bool 0
2025-01-12T21:07:01.741Z In(05) vmx SVGAFeature renderer (after  clamping) svga.msFullQuality bool 0
2025-01-12T21:07:01.741Z In(05) vmx SVGAFeature renderer (after  clamping) svga.logicOps bool 0
2025-01-12T21:07:01.741Z In(05) vmx SVGAFeature renderer (after  clamping) svga.bc67 num 0
2025-01-12T21:07:01.741Z In(05) vmx SVGAFeature renderer (after  clamping) svga.sm5 bool 0
2025-01-12T21:07:01.741Z In(05) vmx SVGAFeature renderer (after  clamping) svga.multisample8x bool 0
2025-01-12T21:07:01.741Z In(05) vmx SVGAFeature renderer (after  clamping) svga.logicBlendOps bool 0
2025-01-12T21:07:01.741Z In(05) vmx SVGAFeature renderer (after  clamping) svga.maxForcedSampleCount num 0
2025-01-12T21:07:01.741Z In(05) vmx SVGAFeature renderer (after  clamping) svga.gl43 bool 0
2025-01-12T21:07:01.741Z In(05) vmx SVGA3dClamp: Renderer Provides     BC67Level:     0 (    0,     0)
2025-01-12T21:07:01.741Z In(05) vmx SVGA3dClamp: Renderer Provides BaseCapsLevel:     0 (    0,     0)
2025-01-12T21:07:01.741Z In(05) vmx SVGA3dClamp: Renderer Provides    ClampLevel:     0 (    0,     0)
2025-01-12T21:07:01.741Z In(05) vmx SVGA3dCaps: host, at power on (3d disabled)
2025-01-12T21:07:01.741Z In(05) vmx   cap[ 19]: 0x00002000 (MAX_TEXTURE_WIDTH)
2025-01-12T21:07:01.741Z In(05) vmx   cap[ 20]: 0x00002000 (MAX_TEXTURE_HEIGHT)
2025-01-12T21:07:01.741Z In(05) vmx   cap[ 93]: 0x00000001 (TS_COLOR_KEY)
2025-01-12T21:07:01.741Z In(05) vmx SVGA3dClamp:     Host Provides     BC67Level:     0 (    0,     0)
2025-01-12T21:07:01.741Z In(05) vmx SVGA3dClamp:     Host Provides BaseCapsLevel:     0 (    0,     4)
2025-01-12T21:07:01.741Z In(05) vmx SVGA3dClamp:     Host Provides    ClampLevel:     0 (    0,     0)
2025-01-12T21:07:01.741Z In(05) vmx USB: Initializing 'UHCI' host controller.
2025-01-12T21:07:01.741Z In(05) vmx USB: PowerOnCreateDevice 'usb:1' #1, found port 1B10E803868.
2025-01-12T21:07:01.741Z No(00) vmx ConfigDB: Setting usb:1.speed = "2"
2025-01-12T21:07:01.741Z In(05) vmx Ethernet0 MAC Address: 00:0c:29:71:5a:8c
2025-01-12T21:07:01.757Z In(05) vmx USB: Initializing 'EHCI' host controller.
2025-01-12T21:07:01.757Z In(05) vmx USB: Initializing 'xHCI' host controller.
2025-01-12T21:07:01.758Z In(05) vmx PCIXHCI: SuperSpeed20Gbps(USB3.2) is supported by xHCI.
2025-01-12T21:07:01.758Z In(05) vmx USB: PowerOnCreateDevice 'usb_xhci:4' #4, found port 1B10E8073C8.
2025-01-12T21:07:01.758Z No(00) vmx ConfigDB: Setting usb_xhci:4.present = "TRUE"
2025-01-12T21:07:01.758Z No(00) vmx ConfigDB: Setting usb_xhci:4.deviceType = "hid"
2025-01-12T21:07:01.758Z No(00) vmx ConfigDB: Setting usb_xhci:4.port = "4"
2025-01-12T21:07:01.758Z No(00) vmx ConfigDB: Setting usb_xhci:4.parent = "-1"
2025-01-12T21:07:01.758Z In(05) vmx USB: PowerOnCreateDevice 'usb_xhci:6' #6, found port 1B10E807438.
2025-01-12T21:07:01.758Z No(00) vmx ConfigDB: Setting usb_xhci:6.speed = "2"
2025-01-12T21:07:01.758Z In(05) vmx USB: PowerOnCreateDevice 'usb_xhci:7' #7, found port 1B10E807470.
2025-01-12T21:07:01.758Z No(00) vmx ConfigDB: Setting usb_xhci:7.speed = "4"
2025-01-12T21:07:01.758Z No(00) vmx ConfigDB: Setting vmci0.id = "-586922912"
2025-01-12T21:07:01.772Z In(05) vmx WORKER: Creating new group with maxThreads=1 (36)
2025-01-12T21:07:01.772Z In(05) vmx DISKUTIL: scsi0:0 : max toolsVersion = 12421, type = 4
2025-01-12T21:07:01.772Z In(05) vmx TOOLS setting legacy tools version to '12421' type 4, manifest status is 9
2025-01-12T21:07:01.772Z In(05) worker-11200 ToolsVersionGetStatusWorkerThread: Tools status 8 derived from environment
2025-01-12T21:07:01.772Z In(05) vmx Tools: sending 'OS_Resume' (state = 4) state change request
2025-01-12T21:07:01.772Z In(05) vmx Tools: Delaying state change request to state 4.
2025-01-12T21:07:01.772Z In(05) vmx TOOLS INSTALL initializing state to IDLE on power on.
2025-01-12T21:07:01.772Z In(05) vmx TOOLS INSTALL updating Rpc handlers registration.
2025-01-12T21:07:01.772Z In(05) vmx TOOLS INSTALL register RPC: upgrader.setGuestFileRoot
2025-01-12T21:07:01.772Z In(05) vmx TOOLS INSTALL register RPC: toolinstall.is_image_inserted
2025-01-12T21:07:01.772Z In(05) vmx TOOLS INSTALL register RPC: toolinstall.installerActive
2025-01-12T21:07:01.772Z In(05) vmx TOOLS INSTALL register RPC: guest.upgrader_send_cmd_line_args
2025-01-12T21:07:01.772Z In(05) vmx P9FS_PowerOn: 9PFS server is not enabled.
2025-01-12T21:07:01.772Z In(05) vmx HgfsServerManagerVigorInit: Initialize: dev api
2025-01-12T21:07:01.772Z In(05) vmx MKSVMX: Copy/paste enabled = 1
2025-01-12T21:07:01.772Z In(05) vmx DEPLOYPKG: No pending deploy package name set
2025-01-12T21:07:01.772Z In(05) vmx DEPLOYPKG: ToolsDeployPkgPublishState: state=0, code=0, message=(null)
2025-01-12T21:07:01.810Z In(05) vmx MonPmc: ctrBase 0x4c1 selBase 0x186/1 PGC 1/1 SMM 1 drain 0 AMD 0
2025-01-12T21:07:01.810Z In(05)+ vmx MonPmc:   gen counters num: 8 width 48 write width 48
2025-01-12T21:07:01.810Z In(05)+ vmx MonPmc:   fix counters num: 4 width 48; version 5
2025-01-12T21:07:01.810Z In(05)+ vmx MonPmc:   unavailable counters: 0xf000000ff
2025-01-12T21:07:01.810Z In(05) vmx CPT: Restoring checkpoint C:\Users\<USER>\Desktop\Kali 2024 x64 Customized by zSecurity v1.2.vmwarevm\Kali 2024 x64 Customized by zSecurity v1.2-f65b28b0.vmss
2025-01-12T21:07:01.810Z In(05) vmx DUMPER: Restoring checkpoint version 8.
2025-01-12T21:07:01.810Z In(05) vmx PStrIntern expansion: nBkts=256
2025-01-12T21:07:01.810Z In(05) vmx Progress -1% (msg.checkpoint.restoreStatus)
2025-01-12T21:07:01.810Z In(05) vmx   restoring GuestVars
2025-01-12T21:07:01.859Z In(05) vmx   restoring A20
2025-01-12T21:07:01.859Z In(05) vmx   restoring BusMemSample
2025-01-12T21:07:01.859Z In(05) vmx   restoring UUIDVMX
2025-01-12T21:07:01.859Z In(05) vmx   restoring memory
2025-01-12T21:07:01.885Z In(05) vmx MainMem: Opened paging file, 'C:\Users\<USER>\Desktop\Kali 2024 x64 Customized by zSecurity v1.2.vmwarevm\Kali 2024 x64 Customized by zSecurity v1.2-f65b28b0.vmem'.
2025-01-12T21:07:01.885Z In(05) vmx MemSched: caller 0 numvm 1 locked pages: num 4614 max 1579008
2025-01-12T21:07:01.887Z In(05) vmx MemSched: locked Page Limit: host 1679277 config 1587200
2025-01-12T21:07:01.887Z In(05) vmx MemSched: minmempct 50  timestamp 11349
2025-01-12T21:07:01.887Z In(05) vmx MemSched: VM 0 min 1068128 max 2116704 shares 2097152 paged 674445 nonpaged 7729 anonymous 11823 locked 4614 touchedPct 0 dirtiedPct 0 timestamp 11349 vmResponsive is 1
2025-01-12T21:07:01.887Z In(05) vmx MemSched: locked 4614 target 1468477 balloon 0 0 0 swapped 0 0 allocd 0 512 state 0 100
2025-01-12T21:07:01.887Z In(05) vmx MemSched: states: 0 1 : 1 0 : 2 0 : 3 0
2025-01-12T21:07:01.887Z In(05) vmx MemSched: Balloon enabled 1 guestType 0 maxSize 0
2025-01-12T21:07:01.888Z In(05) vmx Progress 0% (none)
2025-01-12T21:07:01.989Z In(05) vmx Progress 1% (none)
2025-01-12T21:07:02.049Z In(05) vmx Progress 2% (none)
2025-01-12T21:07:02.102Z In(05) vmx Progress 3% (none)
2025-01-12T21:07:02.150Z In(05) vmx Progress 4% (none)
2025-01-12T21:07:02.231Z In(05) vmx Progress 5% (none)
2025-01-12T21:07:02.307Z In(05) vmx Progress 6% (none)
2025-01-12T21:07:02.371Z In(05) vmx Progress 7% (none)
2025-01-12T21:07:02.498Z In(05) vmx Progress 8% (none)
2025-01-12T21:07:02.577Z In(05) vmx Progress 9% (none)
2025-01-12T21:07:02.640Z In(05) vmx Progress 10% (none)
2025-01-12T21:07:02.719Z In(05) vmx Progress 11% (none)
2025-01-12T21:07:02.815Z In(05) vmx Progress 12% (none)
2025-01-12T21:07:02.878Z In(05) vmx Progress 13% (none)
2025-01-12T21:07:02.973Z In(05) vmx Progress 14% (none)
2025-01-12T21:07:03.065Z In(05) vmx Progress 15% (none)
2025-01-12T21:07:03.131Z In(05) vmx Progress 16% (none)
2025-01-12T21:07:03.227Z In(05) vmx Progress 17% (none)
2025-01-12T21:07:03.315Z In(05) vmx Progress 18% (none)
2025-01-12T21:07:03.400Z In(05) vmx Progress 19% (none)
2025-01-12T21:07:03.514Z In(05) vmx Progress 20% (none)
2025-01-12T21:07:03.622Z In(05) vmx Progress 21% (none)
2025-01-12T21:07:03.813Z In(05) vmx Progress 22% (none)
2025-01-12T21:07:04.021Z In(05) vmx Progress 23% (none)
2025-01-12T21:07:04.179Z In(05) vmx Progress 24% (none)
2025-01-12T21:07:04.327Z In(05) vmx Progress 25% (none)
2025-01-12T21:07:04.469Z In(05) vmx Progress 26% (none)
2025-01-12T21:07:04.605Z In(05) vmx Progress 27% (none)
2025-01-12T21:07:04.734Z In(05) vmx Progress 28% (none)
2025-01-12T21:07:04.854Z In(05) vmx Progress 29% (none)
2025-01-12T21:07:04.999Z In(05) vmx Progress 30% (none)
2025-01-12T21:07:05.200Z In(05) vmx Progress 31% (none)
2025-01-12T21:07:05.345Z In(05) vmx Progress 32% (none)
2025-01-12T21:07:05.491Z In(05) vmx Progress 33% (none)
2025-01-12T21:07:05.640Z In(05) vmx Progress 34% (none)
2025-01-12T21:07:05.789Z In(05) vmx Progress 35% (none)
2025-01-12T21:07:05.930Z In(05) vmx Progress 36% (none)
2025-01-12T21:07:06.058Z In(05) vmx Progress 37% (none)
2025-01-12T21:07:06.197Z In(05) vmx Progress 38% (none)
2025-01-12T21:07:06.333Z In(05) vmx Progress 39% (none)
2025-01-12T21:07:06.451Z In(05) vmx Progress 40% (none)
2025-01-12T21:07:06.579Z In(05) vmx Progress 41% (none)
2025-01-12T21:07:06.702Z In(05) vmx Progress 42% (none)
2025-01-12T21:07:06.835Z In(05) vmx Progress 43% (none)
2025-01-12T21:07:06.979Z In(05) vmx Progress 44% (none)
2025-01-12T21:07:07.105Z In(05) vmx Progress 45% (none)
2025-01-12T21:07:07.282Z In(05) vmx Progress 46% (none)
2025-01-12T21:07:07.374Z In(05) vmx Progress 47% (none)
2025-01-12T21:07:07.503Z In(05) vmx Progress 48% (none)
2025-01-12T21:07:07.663Z In(05) vmx Progress 49% (none)
2025-01-12T21:07:07.803Z In(05) vmx Progress 50% (none)
2025-01-12T21:07:07.960Z In(05) vmx Progress 51% (none)
2025-01-12T21:07:08.082Z In(05) vmx Progress 52% (none)
2025-01-12T21:07:08.213Z In(05) vmx Progress 53% (none)
2025-01-12T21:07:08.347Z In(05) vmx Progress 54% (none)
2025-01-12T21:07:08.435Z In(05) vmx Progress 55% (none)
2025-01-12T21:07:08.563Z In(05) vmx Progress 56% (none)
2025-01-12T21:07:08.704Z In(05) vmx Progress 57% (none)
2025-01-12T21:07:08.840Z In(05) vmx Progress 58% (none)
2025-01-12T21:07:08.958Z In(05) vmx Progress 59% (none)
2025-01-12T21:07:09.096Z In(05) vmx Progress 60% (none)
2025-01-12T21:07:09.231Z In(05) vmx Progress 61% (none)
2025-01-12T21:07:09.342Z In(05) vmx Progress 62% (none)
2025-01-12T21:07:09.422Z In(05) vmx Progress 63% (none)
2025-01-12T21:07:09.507Z In(05) vmx Progress 64% (none)
2025-01-12T21:07:09.626Z In(05) vmx Progress 65% (none)
2025-01-12T21:07:09.800Z In(05) vmx Progress 66% (none)
2025-01-12T21:07:09.959Z In(05) vmx Progress 67% (none)
2025-01-12T21:07:10.087Z In(05) vmx Progress 68% (none)
2025-01-12T21:07:10.222Z In(05) vmx Progress 69% (none)
2025-01-12T21:07:10.342Z In(05) vmx Progress 70% (none)
2025-01-12T21:07:10.423Z In(05) vmx Progress 71% (none)
2025-01-12T21:07:10.524Z In(05) vmx Progress 72% (none)
2025-01-12T21:07:10.624Z In(05) vmx Progress 73% (none)
2025-01-12T21:07:10.739Z In(05) vmx Progress 74% (none)
2025-01-12T21:07:10.874Z In(05) vmx Progress 75% (none)
2025-01-12T21:07:11.007Z In(05) vmx Progress 76% (none)
2025-01-12T21:07:11.167Z In(05) vmx Progress 77% (none)
2025-01-12T21:07:11.278Z In(05) vmx Progress 78% (none)
2025-01-12T21:07:11.466Z In(05) vmx Progress 79% (none)
2025-01-12T21:07:11.687Z In(05) vmx Progress 80% (none)
2025-01-12T21:07:11.870Z In(05) vmx Progress 81% (none)
2025-01-12T21:07:12.020Z In(05) vmx Progress 82% (none)
2025-01-12T21:07:12.189Z In(05) vmx Progress 83% (none)
2025-01-12T21:07:12.367Z In(05) vmx Progress 84% (none)
2025-01-12T21:07:12.510Z In(05) vmx Progress 85% (none)
2025-01-12T21:07:12.667Z In(05) vmx Progress 86% (none)
2025-01-12T21:07:12.813Z In(05) vmx Progress 87% (none)
2025-01-12T21:07:13.032Z In(05) vmx Progress 88% (none)
2025-01-12T21:07:13.238Z In(05) vmx Progress 89% (none)
2025-01-12T21:07:13.396Z In(05) vmx Progress 90% (none)
2025-01-12T21:07:13.544Z In(05) vmx Progress 91% (none)
2025-01-12T21:07:13.681Z In(05) vmx Progress 92% (none)
2025-01-12T21:07:13.876Z In(05) vmx Progress 93% (none)
2025-01-12T21:07:14.078Z In(05) vmx Progress 94% (none)
2025-01-12T21:07:14.331Z In(05) vmx Progress 95% (none)
2025-01-12T21:07:14.441Z In(05) vmx Progress 96% (none)
2025-01-12T21:07:14.569Z In(05) vmx Progress 97% (none)
2025-01-12T21:07:14.729Z In(05) vmx Progress 98% (none)
2025-01-12T21:07:14.860Z In(05) vmx Progress 99% (none)
2025-01-12T21:07:15.003Z In(05) vmx MainMem: Prefetch 2849 MB from checkpoint (729536 hot, 330741 locked, lazy = 0).
2025-01-12T21:07:15.003Z In(05) vmx   restoring MStats
2025-01-12T21:07:15.003Z In(05) vmx   restoring Snapshot
2025-01-12T21:07:15.003Z In(05) vmx   restoring pic
2025-01-12T21:07:15.003Z In(05) vmx   restoring ide1:0
2025-01-12T21:07:15.003Z In(05) vmx   restoring scsi0:0
2025-01-12T21:07:15.003Z In(05) vmx   restoring FeatureCompat
2025-01-12T21:07:15.003Z In(05) vmx   restoring TimeTracker
2025-01-12T21:07:15.003Z In(05) vmx TimeTracker host to guest rate conversion 707002229 @ 1190391000Hz -> 31129184813010 @ 1190390000Hz
2025-01-12T21:07:15.003Z In(05) vmx TimeTracker host to guest rate conversion ((x * 4294963688) >> 32) + 31128477811375
2025-01-12T21:07:15.003Z In(05) vmx TSC scaling enabled.
2025-01-12T21:07:15.003Z In(05) vmx TSC offsetting enabled.
2025-01-12T21:07:15.003Z In(05) vmx timeTracker.globalProgressMaxAllowanceMS: 2000
2025-01-12T21:07:15.003Z In(05) vmx timeTracker.globalProgressToAllowanceNS: 1000
2025-01-12T21:07:15.003Z In(05) vmx   restoring Backdoor
2025-01-12T21:07:15.003Z In(05) vmx   restoring PCI
2025-01-12T21:07:15.003Z In(05) vmx   restoring ExtCfgDevice
2025-01-12T21:07:15.003Z In(05) vmx   restoring Cs440bx
2025-01-12T21:07:15.003Z In(05) vmx DUMPER: Item 'gpe.status' [2, -1] not found.
2025-01-12T21:07:15.003Z In(05) vmx DUMPER: Item 'gpe.enable' [2, -1] not found.
2025-01-12T21:07:15.003Z In(05) vmx   restoring AcpiNotify
2025-01-12T21:07:15.003Z In(05) vmx   restoring vcpuHotPlug
2025-01-12T21:07:15.011Z In(05) vmx   restoring MemoryHotplug
2025-01-12T21:07:15.011Z In(05) vmx   restoring devHP
2025-01-12T21:07:15.011Z In(05) vmx   restoring ACPIWake
2025-01-12T21:07:15.011Z In(05) vmx   restoring OEMDevice
2025-01-12T21:07:15.011Z In(05) vmx   restoring HotButton
2025-01-12T21:07:15.011Z In(05) vmx   restoring Timer
2025-01-12T21:07:15.011Z In(05) vmx   restoring ACPI
2025-01-12T21:07:15.011Z In(05) vmx   restoring XPMode
2025-01-12T21:07:15.011Z In(05) vmx   restoring DMA
2025-01-12T21:07:15.011Z In(05) vmx   restoring BackdoorAPM
2025-01-12T21:07:15.011Z In(05) vmx   restoring smram
2025-01-12T21:07:15.011Z In(05) vmx   restoring backdoorAbsMouse
2025-01-12T21:07:15.011Z In(05) vmx   restoring Keyboard
2025-01-12T21:07:15.011Z In(05) vmx   restoring SIO
2025-01-12T21:07:15.011Z In(05) vmx   restoring monitorLate
2025-01-12T21:07:15.011Z In(05) vmx   restoring vcpuNUMA
2025-01-12T21:07:15.011Z In(05) vmx   restoring devices
2025-01-12T21:07:15.011Z In(05) vmx   restoring configdbFT
2025-01-12T21:07:15.011Z In(05) vmx   restoring DevicesPowerOn
2025-01-12T21:07:15.011Z In(05) vmx   restoring PCIBridge0
2025-01-12T21:07:15.011Z In(05) vmx   restoring PCIBridge4
2025-01-12T21:07:15.011Z In(05) vmx   restoring pciBridge4:1
2025-01-12T21:07:15.011Z In(05) vmx   restoring pciBridge4:2
2025-01-12T21:07:15.011Z In(05) vmx   restoring pciBridge4:3
2025-01-12T21:07:15.011Z In(05) vmx   restoring pciBridge4:4
2025-01-12T21:07:15.011Z In(05) vmx   restoring pciBridge4:5
2025-01-12T21:07:15.011Z In(05) vmx   restoring pciBridge4:6
2025-01-12T21:07:15.011Z In(05) vmx   restoring pciBridge4:7
2025-01-12T21:07:15.011Z In(05) vmx   restoring PCIBridge5
2025-01-12T21:07:15.011Z In(05) vmx   restoring pciBridge5:1
2025-01-12T21:07:15.011Z In(05) vmx   restoring pciBridge5:2
2025-01-12T21:07:15.011Z In(05) vmx   restoring pciBridge5:3
2025-01-12T21:07:15.011Z In(05) vmx   restoring pciBridge5:4
2025-01-12T21:07:15.011Z In(05) vmx   restoring pciBridge5:5
2025-01-12T21:07:15.011Z In(05) vmx   restoring pciBridge5:6
2025-01-12T21:07:15.011Z In(05) vmx   restoring pciBridge5:7
2025-01-12T21:07:15.011Z In(05) vmx   restoring PCIBridge6
2025-01-12T21:07:15.011Z In(05) vmx   restoring pciBridge6:1
2025-01-12T21:07:15.011Z In(05) vmx   restoring pciBridge6:2
2025-01-12T21:07:15.011Z In(05) vmx   restoring pciBridge6:3
2025-01-12T21:07:15.011Z In(05) vmx   restoring pciBridge6:4
2025-01-12T21:07:15.011Z In(05) vmx   restoring pciBridge6:5
2025-01-12T21:07:15.011Z In(05) vmx   restoring pciBridge6:6
2025-01-12T21:07:15.011Z In(05) vmx   restoring pciBridge6:7
2025-01-12T21:07:15.011Z In(05) vmx   restoring PCIBridge7
2025-01-12T21:07:15.011Z In(05) vmx   restoring pciBridge7:1
2025-01-12T21:07:15.011Z In(05) vmx   restoring pciBridge7:2
2025-01-12T21:07:15.011Z In(05) vmx   restoring pciBridge7:3
2025-01-12T21:07:15.011Z In(05) vmx   restoring pciBridge7:4
2025-01-12T21:07:15.011Z In(05) vmx   restoring pciBridge7:5
2025-01-12T21:07:15.013Z In(05) vmx   restoring pciBridge7:6
2025-01-12T21:07:15.013Z In(05) vmx   restoring pciBridge7:7
2025-01-12T21:07:15.013Z In(05) vmx   restoring Migrate
2025-01-12T21:07:15.013Z In(05) vmx   restoring vide
2025-01-12T21:07:15.014Z In(05) vmx DUMPER: Block item 'monbuf' [0, -1] not found.
2025-01-12T21:07:15.014Z In(05) vmx DUMPER: Block item 'monbuf' [1, -1] not found.
2025-01-12T21:07:15.014Z In(05) vmx   restoring SCSI0
2025-01-12T21:07:15.014Z In(05) vmx   restoring VGA
2025-01-12T21:07:15.015Z In(05) vmx   restoring SVGA
2025-01-12T21:07:15.016Z In(05) vmx SVGA: Guest reported SVGA driver: (2, 101187595, 34865152, 0)
2025-01-12T21:07:15.016Z In(05) vmx SVGA-GFB: Allocated gfbSize=4194304
2025-01-12T21:07:15.016Z No(00) vmx ConfigDB: Setting vmotion.checkpointFBSize = "4194304"
2025-01-12T21:07:15.016Z No(00) vmx ConfigDB: Setting vmotion.checkpointSVGAPrimarySize = "268435456"
2025-01-12T21:07:15.027Z In(05) vmx SVGA3dCaps: guest, saved in checkpoint
2025-01-12T21:07:15.027Z In(05) vmx   cap[  0]: 0x00000000 (3D)
2025-01-12T21:07:15.027Z In(05) vmx   cap[  1]: 0x00000000 (MAX_LIGHTS)
2025-01-12T21:07:15.027Z In(05) vmx   cap[  2]: 0x00000000 (MAX_TEXTURES)
2025-01-12T21:07:15.027Z In(05) vmx   cap[  3]: 0x00000000 (MAX_CLIP_PLANES)
2025-01-12T21:07:15.027Z In(05) vmx   cap[  4]: 0x00000000 (VERTEX_SHADER_VERSION)
2025-01-12T21:07:15.027Z In(05) vmx   cap[  5]: 0x00000000 (VERTEX_SHADER)
2025-01-12T21:07:15.027Z In(05) vmx   cap[  6]: 0x00000000 (FRAGMENT_SHADER_VERSION)
2025-01-12T21:07:15.027Z In(05) vmx   cap[  7]: 0x00000000 (FRAGMENT_SHADER)
2025-01-12T21:07:15.027Z In(05) vmx   cap[  8]: 0x00000000 (MAX_RENDER_TARGETS)
2025-01-12T21:07:15.027Z In(05) vmx   cap[  9]: 0x00000000 (S23E8_TEXTURES)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 10]: 0x00000000 (S10E5_TEXTURES)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 11]: 0x00000000 (MAX_FIXED_VERTEXBLEND)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 12]: 0x00000000 (D16_BUFFER_FORMAT)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 13]: 0x00000000 (D24S8_BUFFER_FORMAT)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 14]: 0x00000000 (D24X8_BUFFER_FORMAT)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 15]: 0x00000000 (QUERY_TYPES)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 16]: 0x00000000 (TEXTURE_GRADIENT_SAMPLING)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 17]:   0.000000 (MAX_POINT_SIZE)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 18]: 0x00000000 (MAX_SHADER_TEXTURES)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 19]: 0x00002000 (MAX_TEXTURE_WIDTH)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 20]: 0x00002000 (MAX_TEXTURE_HEIGHT)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 21]: 0x00000000 (MAX_VOLUME_EXTENT)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 22]: 0x00000000 (MAX_TEXTURE_REPEAT)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 23]: 0x00000000 (MAX_TEXTURE_ASPECT_RATIO)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 24]: 0x00000000 (MAX_TEXTURE_ANISOTROPY)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 25]: 0x00000000 (MAX_PRIMITIVE_COUNT)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 26]: 0x00000000 (MAX_VERTEX_INDEX)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 27]: 0x00000000 (MAX_VERTEX_SHADER_INSTRUCTIONS)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 28]: 0x00000000 (MAX_FRAGMENT_SHADER_INSTRUCTIONS)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 29]: 0x00000000 (MAX_VERTEX_SHADER_TEMPS)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 30]: 0x00000000 (MAX_FRAGMENT_SHADER_TEMPS)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 31]: 0x00000000 (TEXTURE_OPS)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 32]: 0x00000000 (SURFACEFMT_X8R8G8B8)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 33]: 0x00000000 (SURFACEFMT_A8R8G8B8)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 34]: 0x00000000 (SURFACEFMT_A2R10G10B10)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 35]: 0x00000000 (SURFACEFMT_X1R5G5B5)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 36]: 0x00000000 (SURFACEFMT_A1R5G5B5)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 37]: 0x00000000 (SURFACEFMT_A4R4G4B4)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 38]: 0x00000000 (SURFACEFMT_R5G6B5)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 39]: 0x00000000 (SURFACEFMT_LUMINANCE16)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 40]: 0x00000000 (SURFACEFMT_LUMINANCE8_ALPHA8)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 41]: 0x00000000 (SURFACEFMT_ALPHA8)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 42]: 0x00000000 (SURFACEFMT_LUMINANCE8)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 43]: 0x00000000 (SURFACEFMT_Z_D16)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 44]: 0x00000000 (SURFACEFMT_Z_D24S8)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 45]: 0x00000000 (SURFACEFMT_Z_D24X8)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 46]: 0x00000000 (SURFACEFMT_DXT1)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 47]: 0x00000000 (SURFACEFMT_DXT2)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 48]: 0x00000000 (SURFACEFMT_DXT3)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 49]: 0x00000000 (SURFACEFMT_DXT4)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 50]: 0x00000000 (SURFACEFMT_DXT5)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 51]: 0x00000000 (SURFACEFMT_BUMPX8L8V8U8)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 52]: 0x00000000 (SURFACEFMT_A2W10V10U10)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 53]: 0x00000000 (SURFACEFMT_BUMPU8V8)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 54]: 0x00000000 (SURFACEFMT_Q8W8V8U8)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 55]: 0x00000000 (SURFACEFMT_CxV8U8)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 56]: 0x00000000 (SURFACEFMT_R_S10E5)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 57]: 0x00000000 (SURFACEFMT_R_S23E8)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 58]: 0x00000000 (SURFACEFMT_RG_S10E5)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 59]: 0x00000000 (SURFACEFMT_RG_S23E8)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 60]: 0x00000000 (SURFACEFMT_ARGB_S10E5)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 61]: 0x00000000 (SURFACEFMT_ARGB_S23E8)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 62]: 0x00000000 (MISSING62)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 63]: 0x00000000 (MAX_VERTEX_SHADER_TEXTURES)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 64]: 0x00000000 (MAX_SIMULTANEOUS_RENDER_TARGETS)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 65]: 0x00000000 (SURFACEFMT_V16U16)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 66]: 0x00000000 (SURFACEFMT_G16R16)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 67]: 0x00000000 (SURFACEFMT_A16B16G16R16)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 68]: 0x00000000 (SURFACEFMT_UYVY)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 69]: 0x00000000 (SURFACEFMT_YUY2)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 70]: 0x00000000 (DEAD4)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 71]: 0x00000000 (DEAD5)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 72]: 0x00000000 (DEAD7)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 73]: 0x00000000 (DEAD6)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 74]: 0x00000000 (AUTOGENMIPMAPS)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 75]: 0x00000000 (SURFACEFMT_NV12)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 76]: 0x00000000 (DEAD10)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 77]: 0x00000000 (MAX_CONTEXT_IDS)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 78]: 0x00000000 (MAX_SURFACE_IDS)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 79]: 0x00000000 (SURFACEFMT_Z_DF16)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 80]: 0x00000000 (SURFACEFMT_Z_DF24)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 81]: 0x00000000 (SURFACEFMT_Z_D24S8_INT)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 82]: 0x00000000 (SURFACEFMT_ATI1)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 83]: 0x00000000 (SURFACEFMT_ATI2)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 84]: 0x00000000 (DEAD1)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 85]: 0x00000000 (DEAD8)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 86]: 0x00000000 (DEAD9)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 87]: 0x00000000 (LINE_AA)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 88]: 0x00000000 (LINE_STIPPLE)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 89]:   0.000000 (MAX_LINE_WIDTH)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 90]:   0.000000 (MAX_AA_LINE_WIDTH)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 91]: 0x00000000 (SURFACEFMT_YV12)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 92]: 0x00000000 (DEAD3)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 93]: 0x00000000 (TS_COLOR_KEY)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 94]: 0x00000000 (DEAD2)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 95]: 0x00000000 (DXCONTEXT)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 96]: 0x00000000 (DEAD11)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 97]: 0x00000000 (DX_MAX_VERTEXBUFFERS)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 98]: 0x00000000 (DX_MAX_CONSTANT_BUFFERS)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 99]: 0x00000000 (DX_PROVOKING_VERTEX)
2025-01-12T21:07:15.027Z In(05) vmx   cap[100]: 0x00000000 (DXFMT_X8R8G8B8)
2025-01-12T21:07:15.027Z In(05) vmx   cap[101]: 0x00000000 (DXFMT_A8R8G8B8)
2025-01-12T21:07:15.027Z In(05) vmx   cap[102]: 0x00000000 (DXFMT_R5G6B5)
2025-01-12T21:07:15.027Z In(05) vmx   cap[103]: 0x00000000 (DXFMT_X1R5G5B5)
2025-01-12T21:07:15.027Z In(05) vmx   cap[104]: 0x00000000 (DXFMT_A1R5G5B5)
2025-01-12T21:07:15.027Z In(05) vmx   cap[105]: 0x00000000 (DXFMT_A4R4G4B4)
2025-01-12T21:07:15.027Z In(05) vmx   cap[106]: 0x00000000 (DXFMT_Z_D32)
2025-01-12T21:07:15.027Z In(05) vmx   cap[107]: 0x00000000 (DXFMT_Z_D16)
2025-01-12T21:07:15.027Z In(05) vmx   cap[108]: 0x00000000 (DXFMT_Z_D24S8)
2025-01-12T21:07:15.027Z In(05) vmx   cap[109]: 0x00000000 (DXFMT_Z_D15S1)
2025-01-12T21:07:15.027Z In(05) vmx   cap[110]: 0x00000000 (DXFMT_LUMINANCE8)
2025-01-12T21:07:15.027Z In(05) vmx   cap[111]: 0x00000000 (DXFMT_LUMINANCE4_ALPHA4)
2025-01-12T21:07:15.027Z In(05) vmx   cap[112]: 0x00000000 (DXFMT_LUMINANCE16)
2025-01-12T21:07:15.027Z In(05) vmx   cap[113]: 0x00000000 (DXFMT_LUMINANCE8_ALPHA8)
2025-01-12T21:07:15.027Z In(05) vmx   cap[114]: 0x00000000 (DXFMT_DXT1)
2025-01-12T21:07:15.027Z In(05) vmx   cap[115]: 0x00000000 (DXFMT_DXT2)
2025-01-12T21:07:15.027Z In(05) vmx   cap[116]: 0x00000000 (DXFMT_DXT3)
2025-01-12T21:07:15.027Z In(05) vmx   cap[117]: 0x00000000 (DXFMT_DXT4)
2025-01-12T21:07:15.027Z In(05) vmx   cap[118]: 0x00000000 (DXFMT_DXT5)
2025-01-12T21:07:15.027Z In(05) vmx   cap[119]: 0x00000000 (DXFMT_BUMPU8V8)
2025-01-12T21:07:15.027Z In(05) vmx   cap[120]: 0x00000000 (DXFMT_BUMPL6V5U5)
2025-01-12T21:07:15.027Z In(05) vmx   cap[121]: 0x00000000 (DXFMT_BUMPX8L8V8U8)
2025-01-12T21:07:15.027Z In(05) vmx   cap[122]: 0x00000000 (DXFMT_FORMAT_DEAD1)
2025-01-12T21:07:15.027Z In(05) vmx   cap[123]: 0x00000000 (DXFMT_ARGB_S10E5)
2025-01-12T21:07:15.027Z In(05) vmx   cap[124]: 0x00000000 (DXFMT_ARGB_S23E8)
2025-01-12T21:07:15.027Z In(05) vmx   cap[125]: 0x00000000 (DXFMT_A2R10G10B10)
2025-01-12T21:07:15.027Z In(05) vmx   cap[126]: 0x00000000 (DXFMT_V8U8)
2025-01-12T21:07:15.027Z In(05) vmx   cap[127]: 0x00000000 (DXFMT_Q8W8V8U8)
2025-01-12T21:07:15.027Z In(05) vmx   cap[128]: 0x00000000 (DXFMT_CxV8U8)
2025-01-12T21:07:15.027Z In(05) vmx   cap[129]: 0x00000000 (DXFMT_X8L8V8U8)
2025-01-12T21:07:15.027Z In(05) vmx   cap[130]: 0x00000000 (DXFMT_A2W10V10U10)
2025-01-12T21:07:15.027Z In(05) vmx   cap[131]: 0x00000000 (DXFMT_ALPHA8)
2025-01-12T21:07:15.027Z In(05) vmx   cap[132]: 0x00000000 (DXFMT_R_S10E5)
2025-01-12T21:07:15.027Z In(05) vmx   cap[133]: 0x00000000 (DXFMT_R_S23E8)
2025-01-12T21:07:15.027Z In(05) vmx   cap[134]: 0x00000000 (DXFMT_RG_S10E5)
2025-01-12T21:07:15.027Z In(05) vmx   cap[135]: 0x00000000 (DXFMT_RG_S23E8)
2025-01-12T21:07:15.027Z In(05) vmx   cap[136]: 0x00000000 (DXFMT_BUFFER)
2025-01-12T21:07:15.027Z In(05) vmx   cap[137]: 0x00000000 (DXFMT_Z_D24X8)
2025-01-12T21:07:15.027Z In(05) vmx   cap[138]: 0x00000000 (DXFMT_V16U16)
2025-01-12T21:07:15.027Z In(05) vmx   cap[139]: 0x00000000 (DXFMT_G16R16)
2025-01-12T21:07:15.027Z In(05) vmx   cap[140]: 0x00000000 (DXFMT_A16B16G16R16)
2025-01-12T21:07:15.027Z In(05) vmx   cap[141]: 0x00000000 (DXFMT_UYVY)
2025-01-12T21:07:15.027Z In(05) vmx   cap[142]: 0x00000000 (DXFMT_YUY2)
2025-01-12T21:07:15.027Z In(05) vmx   cap[143]: 0x00000000 (DXFMT_NV12)
2025-01-12T21:07:15.027Z In(05) vmx   cap[144]: 0x00000000 (DXFMT_FORMAT_DEAD2)
2025-01-12T21:07:15.027Z In(05) vmx   cap[145]: 0x00000000 (DXFMT_R32G32B32A32_TYPELESS)
2025-01-12T21:07:15.027Z In(05) vmx   cap[146]: 0x00000000 (DXFMT_R32G32B32A32_UINT)
2025-01-12T21:07:15.027Z In(05) vmx   cap[147]: 0x00000000 (DXFMT_R32G32B32A32_SINT)
2025-01-12T21:07:15.027Z In(05) vmx   cap[148]: 0x00000000 (DXFMT_R32G32B32_TYPELESS)
2025-01-12T21:07:15.027Z In(05) vmx   cap[149]: 0x00000000 (DXFMT_R32G32B32_FLOAT)
2025-01-12T21:07:15.027Z In(05) vmx   cap[150]: 0x00000000 (DXFMT_R32G32B32_UINT)
2025-01-12T21:07:15.027Z In(05) vmx   cap[151]: 0x00000000 (DXFMT_R32G32B32_SINT)
2025-01-12T21:07:15.027Z In(05) vmx   cap[152]: 0x00000000 (DXFMT_R16G16B16A16_TYPELESS)
2025-01-12T21:07:15.027Z In(05) vmx   cap[153]: 0x00000000 (DXFMT_R16G16B16A16_UINT)
2025-01-12T21:07:15.027Z In(05) vmx   cap[154]: 0x00000000 (DXFMT_R16G16B16A16_SNORM)
2025-01-12T21:07:15.027Z In(05) vmx   cap[155]: 0x00000000 (DXFMT_R16G16B16A16_SINT)
2025-01-12T21:07:15.027Z In(05) vmx   cap[156]: 0x00000000 (DXFMT_R32G32_TYPELESS)
2025-01-12T21:07:15.027Z In(05) vmx   cap[157]: 0x00000000 (DXFMT_R32G32_UINT)
2025-01-12T21:07:15.027Z In(05) vmx   cap[158]: 0x00000000 (DXFMT_R32G32_SINT)
2025-01-12T21:07:15.027Z In(05) vmx   cap[159]: 0x00000000 (DXFMT_R32G8X24_TYPELESS)
2025-01-12T21:07:15.027Z In(05) vmx   cap[160]: 0x00000000 (DXFMT_D32_FLOAT_S8X24_UINT)
2025-01-12T21:07:15.027Z In(05) vmx   cap[161]: 0x00000000 (DXFMT_R32_FLOAT_X8X24)
2025-01-12T21:07:15.027Z In(05) vmx   cap[162]: 0x00000000 (DXFMT_X32_G8X24_UINT)
2025-01-12T21:07:15.027Z In(05) vmx   cap[163]: 0x00000000 (DXFMT_R10G10B10A2_TYPELESS)
2025-01-12T21:07:15.027Z In(05) vmx   cap[164]: 0x00000000 (DXFMT_R10G10B10A2_UINT)
2025-01-12T21:07:15.027Z In(05) vmx   cap[165]: 0x00000000 (DXFMT_R11G11B10_FLOAT)
2025-01-12T21:07:15.027Z In(05) vmx   cap[166]: 0x00000000 (DXFMT_R8G8B8A8_TYPELESS)
2025-01-12T21:07:15.027Z In(05) vmx   cap[167]: 0x00000000 (DXFMT_R8G8B8A8_UNORM)
2025-01-12T21:07:15.027Z In(05) vmx   cap[168]: 0x00000000 (DXFMT_R8G8B8A8_UNORM_SRGB)
2025-01-12T21:07:15.027Z In(05) vmx   cap[169]: 0x00000000 (DXFMT_R8G8B8A8_UINT)
2025-01-12T21:07:15.027Z In(05) vmx   cap[170]: 0x00000000 (DXFMT_R8G8B8A8_SINT)
2025-01-12T21:07:15.027Z In(05) vmx   cap[171]: 0x00000000 (DXFMT_R16G16_TYPELESS)
2025-01-12T21:07:15.027Z In(05) vmx   cap[172]: 0x00000000 (DXFMT_R16G16_UINT)
2025-01-12T21:07:15.027Z In(05) vmx   cap[173]: 0x00000000 (DXFMT_R16G16_SINT)
2025-01-12T21:07:15.027Z In(05) vmx   cap[174]: 0x00000000 (DXFMT_R32_TYPELESS)
2025-01-12T21:07:15.027Z In(05) vmx   cap[175]: 0x00000000 (DXFMT_D32_FLOAT)
2025-01-12T21:07:15.027Z In(05) vmx   cap[176]: 0x00000000 (DXFMT_R32_UINT)
2025-01-12T21:07:15.027Z In(05) vmx   cap[177]: 0x00000000 (DXFMT_R32_SINT)
2025-01-12T21:07:15.027Z In(05) vmx   cap[178]: 0x00000000 (DXFMT_R24G8_TYPELESS)
2025-01-12T21:07:15.027Z In(05) vmx   cap[179]: 0x00000000 (DXFMT_D24_UNORM_S8_UINT)
2025-01-12T21:07:15.027Z In(05) vmx   cap[180]: 0x00000000 (DXFMT_R24_UNORM_X8)
2025-01-12T21:07:15.027Z In(05) vmx   cap[181]: 0x00000000 (DXFMT_X24_G8_UINT)
2025-01-12T21:07:15.027Z In(05) vmx   cap[182]: 0x00000000 (DXFMT_R8G8_TYPELESS)
2025-01-12T21:07:15.027Z In(05) vmx   cap[183]: 0x00000000 (DXFMT_R8G8_UNORM)
2025-01-12T21:07:15.027Z In(05) vmx   cap[184]: 0x00000000 (DXFMT_R8G8_UINT)
2025-01-12T21:07:15.027Z In(05) vmx   cap[185]: 0x00000000 (DXFMT_R8G8_SINT)
2025-01-12T21:07:15.027Z In(05) vmx   cap[186]: 0x00000000 (DXFMT_R16_TYPELESS)
2025-01-12T21:07:15.027Z In(05) vmx   cap[187]: 0x00000000 (DXFMT_R16_UNORM)
2025-01-12T21:07:15.027Z In(05) vmx   cap[188]: 0x00000000 (DXFMT_R16_UINT)
2025-01-12T21:07:15.027Z In(05) vmx   cap[189]: 0x00000000 (DXFMT_R16_SNORM)
2025-01-12T21:07:15.027Z In(05) vmx   cap[190]: 0x00000000 (DXFMT_R16_SINT)
2025-01-12T21:07:15.027Z In(05) vmx   cap[191]: 0x00000000 (DXFMT_R8_TYPELESS)
2025-01-12T21:07:15.027Z In(05) vmx   cap[192]: 0x00000000 (DXFMT_R8_UNORM)
2025-01-12T21:07:15.027Z In(05) vmx   cap[193]: 0x00000000 (DXFMT_R8_UINT)
2025-01-12T21:07:15.027Z In(05) vmx   cap[194]: 0x00000000 (DXFMT_R8_SNORM)
2025-01-12T21:07:15.027Z In(05) vmx   cap[195]: 0x00000000 (DXFMT_R8_SINT)
2025-01-12T21:07:15.027Z In(05) vmx   cap[196]: 0x00000000 (DXFMT_P8)
2025-01-12T21:07:15.027Z In(05) vmx   cap[197]: 0x00000000 (DXFMT_R9G9B9E5_SHAREDEXP)
2025-01-12T21:07:15.027Z In(05) vmx   cap[198]: 0x00000000 (DXFMT_R8G8_B8G8_UNORM)
2025-01-12T21:07:15.027Z In(05) vmx   cap[199]: 0x00000000 (DXFMT_G8R8_G8B8_UNORM)
2025-01-12T21:07:15.027Z In(05) vmx   cap[200]: 0x00000000 (DXFMT_BC1_TYPELESS)
2025-01-12T21:07:15.027Z In(05) vmx   cap[201]: 0x00000000 (DXFMT_BC1_UNORM_SRGB)
2025-01-12T21:07:15.027Z In(05) vmx   cap[202]: 0x00000000 (DXFMT_BC2_TYPELESS)
2025-01-12T21:07:15.027Z In(05) vmx   cap[203]: 0x00000000 (DXFMT_BC2_UNORM_SRGB)
2025-01-12T21:07:15.027Z In(05) vmx   cap[204]: 0x00000000 (DXFMT_BC3_TYPELESS)
2025-01-12T21:07:15.027Z In(05) vmx   cap[205]: 0x00000000 (DXFMT_BC3_UNORM_SRGB)
2025-01-12T21:07:15.027Z In(05) vmx   cap[206]: 0x00000000 (DXFMT_BC4_TYPELESS)
2025-01-12T21:07:15.027Z In(05) vmx   cap[207]: 0x00000000 (DXFMT_ATI1)
2025-01-12T21:07:15.027Z In(05) vmx   cap[208]: 0x00000000 (DXFMT_BC4_SNORM)
2025-01-12T21:07:15.027Z In(05) vmx   cap[209]: 0x00000000 (DXFMT_BC5_TYPELESS)
2025-01-12T21:07:15.027Z In(05) vmx   cap[210]: 0x00000000 (DXFMT_ATI2)
2025-01-12T21:07:15.027Z In(05) vmx   cap[211]: 0x00000000 (DXFMT_BC5_SNORM)
2025-01-12T21:07:15.027Z In(05) vmx   cap[212]: 0x00000000 (DXFMT_R10G10B10_XR_BIAS_A2_UNORM)
2025-01-12T21:07:15.027Z In(05) vmx   cap[213]: 0x00000000 (DXFMT_B8G8R8A8_TYPELESS)
2025-01-12T21:07:15.027Z In(05) vmx   cap[214]: 0x00000000 (DXFMT_B8G8R8A8_UNORM_SRGB)
2025-01-12T21:07:15.027Z In(05) vmx   cap[215]: 0x00000000 (DXFMT_B8G8R8X8_TYPELESS)
2025-01-12T21:07:15.027Z In(05) vmx   cap[216]: 0x00000000 (DXFMT_B8G8R8X8_UNORM_SRGB)
2025-01-12T21:07:15.027Z In(05) vmx   cap[217]: 0x00000000 (DXFMT_Z_DF16)
2025-01-12T21:07:15.027Z In(05) vmx   cap[218]: 0x00000000 (DXFMT_Z_DF24)
2025-01-12T21:07:15.027Z In(05) vmx   cap[219]: 0x00000000 (DXFMT_Z_D24S8_INT)
2025-01-12T21:07:15.027Z In(05) vmx   cap[220]: 0x00000000 (DXFMT_YV12)
2025-01-12T21:07:15.027Z In(05) vmx   cap[221]: 0x00000000 (DXFMT_R32G32B32A32_FLOAT)
2025-01-12T21:07:15.027Z In(05) vmx   cap[222]: 0x00000000 (DXFMT_R16G16B16A16_FLOAT)
2025-01-12T21:07:15.027Z In(05) vmx   cap[223]: 0x00000000 (DXFMT_R16G16B16A16_UNORM)
2025-01-12T21:07:15.027Z In(05) vmx   cap[224]: 0x00000000 (DXFMT_R32G32_FLOAT)
2025-01-12T21:07:15.027Z In(05) vmx   cap[225]: 0x00000000 (DXFMT_R10G10B10A2_UNORM)
2025-01-12T21:07:15.027Z In(05) vmx   cap[226]: 0x00000000 (DXFMT_R8G8B8A8_SNORM)
2025-01-12T21:07:15.027Z In(05) vmx   cap[227]: 0x00000000 (DXFMT_R16G16_FLOAT)
2025-01-12T21:07:15.027Z In(05) vmx   cap[228]: 0x00000000 (DXFMT_R16G16_UNORM)
2025-01-12T21:07:15.027Z In(05) vmx   cap[229]: 0x00000000 (DXFMT_R16G16_SNORM)
2025-01-12T21:07:15.027Z In(05) vmx   cap[230]: 0x00000000 (DXFMT_R32_FLOAT)
2025-01-12T21:07:15.027Z In(05) vmx   cap[231]: 0x00000000 (DXFMT_R8G8_SNORM)
2025-01-12T21:07:15.027Z In(05) vmx   cap[232]: 0x00000000 (DXFMT_R16_FLOAT)
2025-01-12T21:07:15.027Z In(05) vmx   cap[233]: 0x00000000 (DXFMT_D16_UNORM)
2025-01-12T21:07:15.027Z In(05) vmx   cap[234]: 0x00000000 (DXFMT_A8_UNORM)
2025-01-12T21:07:15.027Z In(05) vmx   cap[235]: 0x00000000 (DXFMT_BC1_UNORM)
2025-01-12T21:07:15.027Z In(05) vmx   cap[236]: 0x00000000 (DXFMT_BC2_UNORM)
2025-01-12T21:07:15.027Z In(05) vmx   cap[237]: 0x00000000 (DXFMT_BC3_UNORM)
2025-01-12T21:07:15.027Z In(05) vmx   cap[238]: 0x00000000 (DXFMT_B5G6R5_UNORM)
2025-01-12T21:07:15.027Z In(05) vmx   cap[239]: 0x00000000 (DXFMT_B5G5R5A1_UNORM)
2025-01-12T21:07:15.027Z In(05) vmx   cap[240]: 0x00000000 (DXFMT_B8G8R8A8_UNORM)
2025-01-12T21:07:15.027Z In(05) vmx   cap[241]: 0x00000000 (DXFMT_B8G8R8X8_UNORM)
2025-01-12T21:07:15.027Z In(05) vmx   cap[242]: 0x00000000 (DXFMT_BC4_UNORM)
2025-01-12T21:07:15.027Z In(05) vmx   cap[243]: 0x00000000 (DXFMT_BC5_UNORM)
2025-01-12T21:07:15.027Z In(05) vmx   cap[244]: 0x00000000 (SM41)
2025-01-12T21:07:15.027Z In(05) vmx   cap[245]: 0x00000000 (MULTISAMPLE_2X)
2025-01-12T21:07:15.027Z In(05) vmx   cap[246]: 0x00000000 (MULTISAMPLE_4X)
2025-01-12T21:07:15.027Z In(05) vmx   cap[247]: 0x00000000 (MS_FULL_QUALITY)
2025-01-12T21:07:15.027Z In(05) vmx   cap[248]: 0x00000000 (LOGICOPS)
2025-01-12T21:07:15.027Z In(05) vmx   cap[249]: 0x00000000 (LOGIC_BLENDOPS)
2025-01-12T21:07:15.027Z In(05) vmx   cap[250]: 0x00000000 (DEAD12)
2025-01-12T21:07:15.027Z In(05) vmx   cap[251]: 0x00000000 (DXFMT_BC6H_TYPELESS)
2025-01-12T21:07:15.027Z In(05) vmx   cap[252]: 0x00000000 (DXFMT_BC6H_UF16)
2025-01-12T21:07:15.027Z In(05) vmx   cap[253]: 0x00000000 (DXFMT_BC6H_SF16)
2025-01-12T21:07:15.027Z In(05) vmx   cap[254]: 0x00000000 (DXFMT_BC7_TYPELESS)
2025-01-12T21:07:15.027Z In(05) vmx   cap[255]: 0x00000000 (DXFMT_BC7_UNORM)
2025-01-12T21:07:15.027Z In(05) vmx   cap[256]: 0x00000000 (DXFMT_BC7_UNORM_SRGB)
2025-01-12T21:07:15.027Z In(05) vmx   cap[257]: 0x00000000 (DEAD13)
2025-01-12T21:07:15.027Z In(05) vmx   cap[258]: 0x00000000 (SM5)
2025-01-12T21:07:15.027Z In(05) vmx   cap[259]: 0x00000000 (MULTISAMPLE_8X)
2025-01-12T21:07:15.027Z In(05) vmx   cap[260]: 0x00000000 (MAX_FORCED_SAMPLE_COUNT)
2025-01-12T21:07:15.027Z In(05) vmx   cap[261]: 0x00000000 (GL43)
2025-01-12T21:07:15.027Z In(05) vmx SVGA3dCaps: guest, at resume
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 19]: 0x00002000 (MAX_TEXTURE_WIDTH)
2025-01-12T21:07:15.027Z In(05) vmx   cap[ 20]: 0x00002000 (MAX_TEXTURE_HEIGHT)
2025-01-12T21:07:15.033Z In(05) vmx SVGA: Disabling 3d support
2025-01-12T21:07:15.033Z In(05) vmx   restoring usb
2025-01-12T21:07:15.033Z In(05) vmx DUMPER: Item 'lastFrnumChangeTime' [-1, -1] not found.
2025-01-12T21:07:15.033Z In(05) vmx   restoring usb:1
2025-01-12T21:07:15.033Z In(05) vmx   restoring Ethernet0
2025-01-12T21:07:15.033Z In(05) vmx   restoring sound
2025-01-12T21:07:15.033Z In(05) vmx   restoring hpet0
2025-01-12T21:07:15.033Z In(05) vmx   restoring ich7m.hpet
2025-01-12T21:07:15.033Z In(05) vmx   restoring ehci
2025-01-12T21:07:15.033Z In(05) vmx   restoring usb_xhci
2025-01-12T21:07:15.033Z In(05) vmx PCIXHCI: Interrupt type changed from INTX to MSIX
2025-01-12T21:07:15.033Z In(05) vmx   restoring usb_xhci:4
2025-01-12T21:07:15.036Z In(05) vmx   restoring usb_xhci:6
2025-01-12T21:07:15.036Z In(05) vmx   restoring usb_xhci:7
2025-01-12T21:07:15.036Z In(05) vmx   restoring vmci0
2025-01-12T21:07:15.037Z In(05) vmx   restoring vsock
2025-01-12T21:07:15.038Z In(05) vmx DUMPER: Item 'remote.port' [0, -1] not found.
2025-01-12T21:07:15.038Z In(05) vmx DUMPER: Item 'qpair.context' [0, -1] not found.
2025-01-12T21:07:15.038Z In(05) vmx DUMPER: Item 'timeoutRemaining' [0, -1] not found.
2025-01-12T21:07:15.039Z In(05) vmx DUMPER: Item 'timeoutRemaining' [1, -1] not found.
2025-01-12T21:07:15.039Z In(05) vmx DUMPER: Item 'timeoutRemaining' [2, -1] not found.
2025-01-12T21:07:15.039Z In(05) vmx   restoring GuestMsg
2025-01-12T21:07:15.039Z In(05) vmx   restoring GuestRpc
2025-01-12T21:07:15.039Z In(05) vmx DUMPER: Item 'AsyncVmciSocket.numSendBuf' [-1, -1] not found.
2025-01-12T21:07:15.039Z In(05) vmx   restoring Tools
2025-01-12T21:07:15.040Z In(05) vmx   restoring Tools Install
2025-01-12T21:07:15.040Z In(05) vmx TOOLS INSTALL setting state to 0 on restore.
2025-01-12T21:07:15.040Z In(05) vmx   restoring GuestAppMonitor
2025-01-12T21:07:15.040Z In(05) vmx DUMPER: Requested 10 bytes, found 5 bytes.
2025-01-12T21:07:15.040Z In(05) vmx DUMPER: Requested 20 bytes, found 5 bytes.
2025-01-12T21:07:15.040Z In(05) vmx   restoring Hgfs
2025-01-12T21:07:15.040Z In(05) vmx   restoring MKSVMX
2025-01-12T21:07:15.040Z In(05) vmx   restoring ToolsDeployPkg
2025-01-12T21:07:15.040Z In(05) vmx DEPLOYPKG: ToolsDeployPkgCptRestore: state=0 err=0 (null msg)
2025-01-12T21:07:15.040Z In(05) vmx DEPLOYPKG: ToolsDeployPkgRestoreSuccessTasks: state=0 err=0, msg=null
2025-01-12T21:07:15.040Z In(05) vmx   restoring CMOS
2025-01-12T21:07:15.040Z In(05) vmx   restoring FlashRam
2025-01-12T21:07:15.040Z In(05) vmx Progress 101% (none)
2025-01-12T21:07:15.043Z In(05) vmx DUMPER: Updating header magic on restore.
2025-01-12T21:07:15.061Z No(00) vmx ConfigDB: Setting checkpoint.vmState = ""
2025-01-12T21:07:15.128Z No(00) vmx PowerOnTiming: Module CheckpointLate took 13316557 us
2025-01-12T21:07:15.132Z No(00) vmx ConfigDB: Setting monitor.phys_bits_used = "45"
2025-01-12T21:07:15.132Z In(05) vmx Full guest CPUID with differences from hostCPUID highlighted.
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID guest vendor: GenuineIntel
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID guest family: 0x6 model: 0x7e stepping: 0x5
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID guest codename: Ice Lake-U/Y
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID guest name: Intel(R) Core(TM) i3-1005G1 CPU @ 1.20GHz
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID       level eaxIn, ecxIn:        eax        ebx        ecx        edx
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID guest level 00000000,  0: 0x0000001b 0x756e6547 0x6c65746e 0x49656e69
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID guest level 00000001,  0: 0x000706e5 0x00020800 0xf7fa3203 0x1f8bfbff
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID *host level 00000001,  0: 0x000706e5 0x00100800 0x7ffafbbf 0xbfebfbff
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID guest level 00000002,  0: 0x00feff01 0x000000f0 0x00000000 0x00000000
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID guest level 00000004,  0: 0x04000121 0x02c0003f 0x0000003f 0x00000000
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID *host level 00000004,  0: 0x1c004121 0x02c0003f 0x0000003f 0x00000000
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID guest level 00000004,  1: 0x04000122 0x01c0003f 0x0000003f 0x00000000
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID *host level 00000004,  1: 0x1c004122 0x01c0003f 0x0000003f 0x00000000
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID guest level 00000004,  2: 0x04000143 0x01c0003f 0x000003ff 0x00000000
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID *host level 00000004,  2: 0x1c004143 0x01c0003f 0x000003ff 0x00000000
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID guest level 00000004,  3: 0x04004163 0x03c0003f 0x00000fff 0x00000006
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID *host level 00000004,  3: 0x1c03c163 0x03c0003f 0x00000fff 0x00000006
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID guest level 00000004,  4: 0x04000000 0x00000000 0x00000000 0x00000000
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID *host level 00000004,  4: 0x00000000 0x00000000 0x00000000 0x00000000
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID guest level 00000006,  0: 0x00000004 0x00000000 0x00000000 0x00000000
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID *host level 00000006,  0: 0x0017aff7 0x00000002 0x00000009 0x00000000
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID guest level 00000007,  0: 0x00000000 0xf0bf27eb 0x00405f4e 0xbc000410
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID *host level 00000007,  0: 0x00000000 0xf2bf27ef 0x40405f4e 0xbc000410
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID guest level 0000000a,  0: 0x08300801 0x000000ff 0x0000000f 0x00008000
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID *host level 0000000a,  0: 0x08300805 0x00000000 0x0000000f 0x00008604
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID guest level 0000000b,  0: 0x00000000 0x00000001 0x00000100 0x00000000
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID *host level 0000000b,  0: 0x00000001 0x00000002 0x00000100 0x00000000
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID guest level 0000000b,  1: 0x00000001 0x00000002 0x00000201 0x00000000
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID *host level 0000000b,  1: 0x00000004 0x00000004 0x00000201 0x00000000
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID guest level 0000000d,  0: 0x000002e7 0x00000a88 0x00000a88 0x00000000
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID *host level 0000000d,  0: 0x000002e7 0x00000a80 0x00000a88 0x00000000
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID guest level 0000000d,  1: 0x0000000f 0x00000a00 0x00000000 0x00000000
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID *host level 0000000d,  1: 0x0000000f 0x00000a00 0x00002100 0x00000000
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID guest level 0000000d,  2: 0x00000100 0x00000240 0x00000000 0x00000000
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID guest level 0000000d,  5: 0x00000040 0x00000440 0x00000000 0x00000000
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID guest level 0000000d,  6: 0x00000200 0x00000480 0x00000000 0x00000000
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID guest level 0000000d,  7: 0x00000400 0x00000680 0x00000000 0x00000000
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID guest level 0000000d,  9: 0x00000008 0x00000a80 0x00000000 0x00000000
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID guest level 00000015,  0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID *host level 00000015,  0: 0x00000002 0x0000003e 0x0249f000 0x00000000
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID guest level 00000016,  0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID *host level 00000016,  0: 0x000004b0 0x00000d48 0x00000064 0x00000000
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID guest level 00000018,  0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID *host level 00000018,  0: 0x00000007 0x00000000 0x00000000 0x00000000
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID guest level 00000018,  1: 0x00000000 0x00000000 0x00000000 0x00000000
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID *host level 00000018,  1: 0x00000000 0x00080007 0x00000001 0x00004122
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID guest level 00000018,  2: 0x00000000 0x00000000 0x00000000 0x00000000
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID *host level 00000018,  2: 0x00000000 0x0010000f 0x00000001 0x00004125
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID guest level 00000018,  3: 0x00000000 0x00000000 0x00000000 0x00000000
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID *host level 00000018,  3: 0x00000000 0x00040001 0x00000010 0x00004024
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID guest level 00000018,  4: 0x00000000 0x00000000 0x00000000 0x00000000
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID *host level 00000018,  4: 0x00000000 0x00040006 0x00000008 0x00004024
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID guest level 00000018,  5: 0x00000000 0x00000000 0x00000000 0x00000000
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID *host level 00000018,  5: 0x00000000 0x00080008 0x00000001 0x00004124
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID guest level 00000018,  6: 0x00000000 0x00000000 0x00000000 0x00000000
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID *host level 00000018,  6: 0x00000000 0x00080007 0x00000080 0x00004043
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID guest level 00000018,  7: 0x00000000 0x00000000 0x00000000 0x00000000
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID *host level 00000018,  7: 0x00000000 0x00080009 0x00000080 0x00004043
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID guest level 40000000,  0: 0x40000010 0x61774d56 0x4d566572 0x65726177
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID guest level 40000010,  0: 0x001229f6 0x000101d0 0x00000002 0x00000000
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID guest level 80000000,  0: 0x80000008 0x00000000 0x00000000 0x00000000
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID guest level 80000001,  0: 0x00000000 0x00000000 0x00000121 0x2c100800
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID guest level 80000002,  0: 0x65746e49 0x2952286c 0x726f4320 0x4d542865
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID guest level 80000003,  0: 0x33692029 0x3030312d 0x20314735 0x20555043
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID guest level 80000004,  0: 0x2e312040 0x48473032 0x0000007a 0x00000000
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID guest level 80000006,  0: 0x00000000 0x00000000 0x01006040 0x00000000
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID guest level 80000007,  0: 0x00000000 0x00000000 0x00000000 0x00000100
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID guest level 80000008,  0: 0x0000302d 0x00000000 0x00000000 0x00000000
2025-01-12T21:07:15.132Z In(05) vmx guest vs. host CPUID *host level 80000008,  0: 0x00003027 0x00000000 0x00000000 0x00000000
2025-01-12T21:07:15.132Z In(05) vmx Minimum ucode level: 0x000000a6
2025-01-12T21:07:15.132Z In(05) vmx VPMC: events will use hybrid freeze.
2025-01-12T21:07:15.132Z In(05) vmx VPMC: gen counters: num 8 mask 0xffffffffffff
2025-01-12T21:07:15.132Z In(05) vmx VPMC: fix counters: num 0 mask 0; version 1
2025-01-12T21:07:15.132Z In(05) vmx VPMC: hardware counters: 0
2025-01-12T21:07:15.132Z In(05) vmx VPMC: perf capabilities: 0x2000
2025-01-12T21:07:15.132Z In(05) vmx Guest MSR IA32_ARCH_CAPABILITIES 0x10a = 0x8002b
2025-01-12T21:07:15.132Z In(05) vmx SVGA: Registering IOSpace at 0x1070
2025-01-12T21:07:15.132Z In(05) vmx SVGA-PCI: BAR gfbSize=134217728, fifoSize=8388608
2025-01-12T21:07:15.132Z In(05) vmx SVGA: SVGA_REG_MEMORY_SIZE=4194304
2025-01-12T21:07:15.132Z In(05) vmx SVGA: SVGA_REG_VRAM_SIZE=4194304
2025-01-12T21:07:15.132Z In(05) vmx SVGA: Final Device caps : 0xfdff83e2
2025-01-12T21:07:15.132Z In(05) vmx SVGA: Final Device caps2: 0x0005efff
2025-01-12T21:07:15.132Z In(05) vmx BusMemSampleSetUpStats: touched: initPct 8 pages 0 : dirtied: initPct 2 pages 0
2025-01-12T21:07:15.132Z In(05) vmx FeatureCompat: Capabilities:
2025-01-12T21:07:15.132Z In(05) vmx Capability Found: cpuid.sse3 = 1
2025-01-12T21:07:15.132Z In(05) vmx Capability Found: cpuid.pclmulqdq = 1
2025-01-12T21:07:15.132Z In(05) vmx Capability Found: cpuid.ssse3 = 1
2025-01-12T21:07:15.132Z In(05) vmx Capability Found: cpuid.fma = 1
2025-01-12T21:07:15.132Z In(05) vmx Capability Found: cpuid.cmpxchg16b = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.pcid = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.sse41 = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.sse42 = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.movbe = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.popcnt = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.aes = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.xsave = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.avx = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.f16c = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.rdrand = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.ss = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.fsgsbase = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.bmi1 = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.avx2 = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.smep = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.bmi2 = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.enfstrg = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.invpcid = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.avx512f = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.avx512dq = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.rdseed = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.adx = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.smap = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.avx512ifma = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.clflushopt = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.avx512cd = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.sha = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.avx512bw = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.avx512vl = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.avx512vbmi = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.umip = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.pku = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.avx512vbmi2 = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.gfni = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.vaes = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.vpclmulqdq = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.avx512vnni = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.avx512bitalg = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.avx512vpopcntdq = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.rdpid = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.fast_short_repmov = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.mdclear = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.stibp = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.fcmd = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.ssbd = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.xcr0_master_sse = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.xcr0_master_ymm_h = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.xcr0_master_opmask = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.xcr0_master_zmm_h = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.xcr0_master_hi16_zmm = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.xcr0_master_pkru = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.xsaveopt = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.xsavec = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.xgetbv_ecx1 = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.xsaves = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.lahf64 = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.abm = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.3dnprefetch = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.nx = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.pdpe1gb = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.rdtscp = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.lm = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.intel = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.ibrs = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.ibpb = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.mwait = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.vmx = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.ds = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.xcr0_master_bndregs = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: cpuid.xcr0_master_bndcsr = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: misc.rdcl_no = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: misc.ibrs_all = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: misc.rsba_no = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: misc.mds_no = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: hv.capable = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: vt.realmode = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: vt.mbx = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: misc.cpuidfaulting = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: vt.advexitinfo = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: vt.eptad = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: vt.ple = 1
2025-01-12T21:07:15.136Z In(05) vmx Capability Found: vt.zeroinstlen = 1
2025-01-12T21:07:15.136Z In(05) vmx FeatureCompat: Requirements:
2025-01-12T21:07:15.136Z In(05) vmx VM Features Required: cpuid.sse3 - Bool:Min:1
2025-01-12T21:07:15.136Z In(05) vmx VM Features Required: cpuid.pclmulqdq - Bool:Min:1
2025-01-12T21:07:15.136Z In(05) vmx VM Features Required: cpuid.ssse3 - Bool:Min:1
2025-01-12T21:07:15.136Z In(05) vmx VM Features Required: cpuid.fma - Bool:Min:1
2025-01-12T21:07:15.136Z In(05) vmx VM Features Required: cpuid.cmpxchg16b - Bool:Min:1
2025-01-12T21:07:15.136Z In(05) vmx VM Features Required: cpuid.pcid - Bool:Min:1
2025-01-12T21:07:15.136Z In(05) vmx VM Features Required: cpuid.sse41 - Bool:Min:1
2025-01-12T21:07:15.136Z In(05) vmx VM Features Required: cpuid.sse42 - Bool:Min:1
2025-01-12T21:07:15.136Z In(05) vmx VM Features Required: cpuid.movbe - Bool:Min:1
2025-01-12T21:07:15.136Z In(05) vmx VM Features Required: cpuid.popcnt - Bool:Min:1
2025-01-12T21:07:15.136Z In(05) vmx VM Features Required: cpuid.aes - Bool:Min:1
2025-01-12T21:07:15.136Z In(05) vmx VM Features Required: cpuid.xsave - Bool:Min:1
2025-01-12T21:07:15.136Z In(05) vmx VM Features Required: cpuid.avx - Bool:Min:1
2025-01-12T21:07:15.136Z In(05) vmx VM Features Required: cpuid.f16c - Bool:Min:1
2025-01-12T21:07:15.136Z In(05) vmx VM Features Required: cpuid.rdrand - Bool:Min:1
2025-01-12T21:07:15.138Z In(05) vmx VM Features Required: cpuid.ss - Bool:Min:1
2025-01-12T21:07:15.138Z In(05) vmx VM Features Required: cpuid.fsgsbase - Bool:Min:1
2025-01-12T21:07:15.138Z In(05) vmx VM Features Required: cpuid.bmi1 - Bool:Min:1
2025-01-12T21:07:15.138Z In(05) vmx VM Features Required: cpuid.avx2 - Bool:Min:1
2025-01-12T21:07:15.138Z In(05) vmx VM Features Required: cpuid.smep - Bool:Min:1
2025-01-12T21:07:15.138Z In(05) vmx VM Features Required: cpuid.bmi2 - Bool:Min:1
2025-01-12T21:07:15.138Z In(05) vmx VM Features Required: cpuid.enfstrg - Bool:Min:1
2025-01-12T21:07:15.138Z In(05) vmx VM Features Required: cpuid.invpcid - Bool:Min:1
2025-01-12T21:07:15.138Z In(05) vmx VM Features Required: cpuid.avx512f - Bool:Min:1
2025-01-12T21:07:15.138Z In(05) vmx VM Features Required: cpuid.avx512dq - Bool:Min:1
2025-01-12T21:07:15.138Z In(05) vmx VM Features Required: cpuid.rdseed - Bool:Min:1
2025-01-12T21:07:15.138Z In(05) vmx VM Features Required: cpuid.adx - Bool:Min:1
2025-01-12T21:07:15.138Z In(05) vmx VM Features Required: cpuid.smap - Bool:Min:1
2025-01-12T21:07:15.138Z In(05) vmx VM Features Required: cpuid.avx512ifma - Bool:Min:1
2025-01-12T21:07:15.138Z In(05) vmx VM Features Required: cpuid.clflushopt - Bool:Min:1
2025-01-12T21:07:15.138Z In(05) vmx VM Features Required: cpuid.avx512cd - Bool:Min:1
2025-01-12T21:07:15.138Z In(05) vmx VM Features Required: cpuid.sha - Bool:Min:1
2025-01-12T21:07:15.138Z In(05) vmx VM Features Required: cpuid.avx512bw - Bool:Min:1
2025-01-12T21:07:15.138Z In(05) vmx VM Features Required: cpuid.avx512vl - Bool:Min:1
2025-01-12T21:07:15.138Z In(05) vmx VM Features Required: cpuid.avx512vbmi - Bool:Min:1
2025-01-12T21:07:15.138Z In(05) vmx VM Features Required: cpuid.umip - Bool:Min:1
2025-01-12T21:07:15.138Z In(05) vmx VM Features Required: cpuid.pku - Bool:Min:1
2025-01-12T21:07:15.138Z In(05) vmx VM Features Required: cpuid.avx512vbmi2 - Bool:Min:1
2025-01-12T21:07:15.138Z In(05) vmx VM Features Required: cpuid.gfni - Bool:Min:1
2025-01-12T21:07:15.138Z In(05) vmx VM Features Required: cpuid.vaes - Bool:Min:1
2025-01-12T21:07:15.138Z In(05) vmx VM Features Required: cpuid.vpclmulqdq - Bool:Min:1
2025-01-12T21:07:15.138Z In(05) vmx VM Features Required: cpuid.avx512vnni - Bool:Min:1
2025-01-12T21:07:15.138Z In(05) vmx VM Features Required: cpuid.avx512bitalg - Bool:Min:1
2025-01-12T21:07:15.138Z In(05) vmx VM Features Required: cpuid.avx512vpopcntdq - Bool:Min:1
2025-01-12T21:07:15.138Z In(05) vmx VM Features Required: cpuid.rdpid - Bool:Min:1
2025-01-12T21:07:15.138Z In(05) vmx VM Features Required: cpuid.fast_short_repmov - Bool:Min:1
2025-01-12T21:07:15.138Z In(05) vmx VM Features Required: cpuid.mdclear - Bool:Min:1
2025-01-12T21:07:15.138Z In(05) vmx VM Features Required: cpuid.stibp - Bool:Min:1
2025-01-12T21:07:15.138Z In(05) vmx VM Features Required: cpuid.fcmd - Bool:Min:1
2025-01-12T21:07:15.138Z In(05) vmx VM Features Required: cpuid.ssbd - Bool:Min:1
2025-01-12T21:07:15.138Z In(05) vmx VM Features Required: cpuid.xcr0_master_sse - Bool:Min:1
2025-01-12T21:07:15.138Z In(05) vmx VM Features Required: cpuid.xcr0_master_ymm_h - Bool:Min:1
2025-01-12T21:07:15.138Z In(05) vmx VM Features Required: cpuid.xcr0_master_opmask - Bool:Min:1
2025-01-12T21:07:15.138Z In(05) vmx VM Features Required: cpuid.xcr0_master_zmm_h - Bool:Min:1
2025-01-12T21:07:15.138Z In(05) vmx VM Features Required: cpuid.xcr0_master_hi16_zmm - Bool:Min:1
2025-01-12T21:07:15.138Z In(05) vmx VM Features Required: cpuid.xcr0_master_pkru - Bool:Min:1
2025-01-12T21:07:15.138Z In(05) vmx VM Features Required: cpuid.xsaveopt - Bool:Min:1
2025-01-12T21:07:15.138Z In(05) vmx VM Features Required: cpuid.xsavec - Bool:Min:1
2025-01-12T21:07:15.138Z In(05) vmx VM Features Required: cpuid.xgetbv_ecx1 - Bool:Min:1
2025-01-12T21:07:15.138Z In(05) vmx VM Features Required: cpuid.xsaves - Bool:Min:1
2025-01-12T21:07:15.138Z In(05) vmx VM Features Required: cpuid.lahf64 - Bool:Min:1
2025-01-12T21:07:15.138Z In(05) vmx VM Features Required: cpuid.abm - Bool:Min:1
2025-01-12T21:07:15.138Z In(05) vmx VM Features Required: cpuid.3dnprefetch - Bool:Min:1
2025-01-12T21:07:15.138Z In(05) vmx VM Features Required: cpuid.nx - Bool:Min:1
2025-01-12T21:07:15.138Z In(05) vmx VM Features Required: cpuid.pdpe1gb - Bool:Min:1
2025-01-12T21:07:15.138Z In(05) vmx VM Features Required: cpuid.rdtscp - Bool:Min:1
2025-01-12T21:07:15.138Z In(05) vmx VM Features Required: cpuid.lm - Bool:Min:1
2025-01-12T21:07:15.138Z In(05) vmx VM Features Required: cpuid.intel - Bool:Min:1
2025-01-12T21:07:15.138Z In(05) vmx VM Features Required: cpuid.ibrs - Bool:Min:1
2025-01-12T21:07:15.138Z In(05) vmx VM Features Required: cpuid.ibpb - Bool:Min:1
2025-01-12T21:07:15.138Z In(05) vmx VM Features Required: misc.rdcl_no - Bool:Min:1
2025-01-12T21:07:15.138Z In(05) vmx VM Features Required: misc.ibrs_all - Bool:Min:1
2025-01-12T21:07:15.138Z In(05) vmx VM Features Required: misc.rsba_no - Bool:Min:1
2025-01-12T21:07:15.138Z In(05) vmx VM Features Required: misc.mds_no - Bool:Min:1
2025-01-12T21:07:15.149Z In(05) vmx TOOLS received request in VMX to set option 'enableDnD' -> '1'
2025-01-12T21:07:15.149Z In(05) vmx 
2025-01-12T21:07:15.149Z In(05)+ vmx OvhdMem: Static (Power On) Overheads
2025-01-12T21:07:15.149Z In(05) vmx                                                       reserved      |          used
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem excluded                                  cur    max    avg |    cur    max    avg
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdUser_MainMem                    :  2097152 2097152      - | 330741 330741      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdUser_VmxText                    :    7680   7680      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdUser_VmxTextLibs                :   17408  17408      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem Total excluded                      :  2122240 2122240      - |      -      -      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem Actual maximum                      :         2122240        |             -
2025-01-12T21:07:15.149Z In(05)+ vmx 
2025-01-12T21:07:15.149Z In(05) vmx                                                       reserved      |          used
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem paged                                     cur    max    avg |    cur    max    avg
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdUser_STATS_vmm                  :       4      4      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdUser_STATS_device               :       2      2      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdUser_SvgaMobFallback            :  196608 196608      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdUser_DiskLibMemUsed             :    3075   3075      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdUser_SvgaSurfaceTable           :       6      6      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdUser_SvgaBESurfaceTable         :       4      4      - |      4      4      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdUser_SvgaSDirtyCache            :      96     96      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdUser_SvgaCursor                 :      10     10      - |     10     10      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdUser_SvgaPPNList                :     258    258      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdUser_VmxGlobals                 :   10240  10240      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdUser_VmxGlobalsLibs             :    3584   3584      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdUser_VmxHeap                    :    8704   8704      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdUser_VmxMks                     :      33     33      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdUser_VmxMksRenderOps            :     678    678      - |    492    492      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdUser_VmxMks3d                   :  131072 131072      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdUser_VmxMksScreenTemp           :   69890  69890      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdUser_VmxMksVnc                  :   74936  74936      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdUser_VmxMksScreen               :  131075 131075      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdUser_VmxMksSVGAVO               :    4096   4096      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdUser_VmxMksSwbCursor            :    4096   4096      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdUser_VmxPhysMemErrPages         :      10     10      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdUser_VmxSLEntryBuf              :     128    128      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdUser_VmxThreads                 :   35840  35840      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem Total paged                         :  674445 674445      - |    506    506      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem Actual maximum                      :         674445        |        674445
2025-01-12T21:07:15.149Z In(05)+ vmx 
2025-01-12T21:07:15.149Z In(05) vmx                                                       reserved      |          used
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem nonpaged                                  cur    max    avg |    cur    max    avg
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdUser_SharedArea                 :     148    148      - |    133    133      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdUser_BusMemTraceBitmap          :      69     69      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdUser_PFrame                     :    4279   5314      - |   4279   4279      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdUser_VIDE_KSEG                  :      16     16      - |     16     16      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdUser_VGA                        :      64     64      - |     64     64      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdUser_BalloonMPN                 :       1      1      - |      1      1      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdUser_P2MUpdateBuffer            :       3      3      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdUser_ServicesMPN                :       3      3      - |      2      2      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdUser_LocalApic                  :       2      2      - |      2      2      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdUser_VBIOS                      :       8      8      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdUser_VnicGuest                  :      16     16      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdUser_LSIBIOS                    :       4      4      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdUser_LSIRings                   :       4      4      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdUser_SAS1068BIOS                :       4      4      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdUser_SBIOS                      :      16     16      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdUser_FlashRam                   :     128    128      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdUser_SVGAFB                     :    1024   1024      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdUser_SVGAMEM                    :      64    512      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdUser_HDAudioReg                 :       3      3      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdUser_EHCIRegister               :       1      1      - |      1      1      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdUser_XhciRegister               :       1      1      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdUser_HyperV                     :       2      2      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdUser_ExtCfg                     :       4      4      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdUser_vhvCachedVMCS              :       2      2      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdUser_vhvNestedAPIC              :       2      2      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdUser_LBR                        :       2      2      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdUser_MonWired                   :      53     53      - |     53     53      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdUser_MonNuma                    :     252    252      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdUser_NVDC                       :       1      1      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdUser_PCIeMMIOArea               :      70     70      - |     34     34      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem Total nonpaged                      :    6246   7729      - |   4585   4585      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem Actual maximum                      :           6246        |          7729
2025-01-12T21:07:15.149Z In(05)+ vmx 
2025-01-12T21:07:15.149Z In(05) vmx                                                       reserved      |          used
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem anonymous                                 cur    max    avg |    cur    max    avg
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdMon_Alloc                       :     196    196      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdMon_BusMemFrame                 :    2114   2171      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdMon_BusMem2MInfo                :      32     32      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdMon_BusMem1GInfo                :       1      1      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdMon_BusMemZapListMPN            :       1      1      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdMon_BusMemPreval                :       8      8      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdMon_MonAS                       :       2      2      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdMon_GuestMem                    :      80     80      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdMon_TC                          :    1026   1026      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdMon_BusMemMonAS                 :       8      8      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdMon_PlatformMonAS               :      13     13      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdMon_HVNuma                      :       4      4      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdMon_HV                          :       2      2      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdMon_HVMSRBitmap                 :       1      1      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdMon_VHVGuestMSRBitmap           :       2      2      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdMon_VHV                         :       6      6      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdMon_Numa                        :      30     30      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdMon_NumaTextRodata              :     198    198      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdMon_NumaDataBss                 :      54     54      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdMon_BaseWired                   :      58     58      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdMon_Bootstrap                   :    2303   2303      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdMon_GPhysTraced                 :     875    875      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdMon_GPhysHWMMU                  :    4374   4374      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdMon_GPhysNoTrace                :     266    266      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdMon_PhysMemGart                 :     104    104      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdMon_PhysMemErr                  :       7      7      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem OvhdMon_VProbe                      :       1      1      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem Total anonymous                     :   11766  11823      - |      0      0      -
2025-01-12T21:07:15.149Z In(05) vmx OvhdMem Actual maximum                      :          11766        |         11823
2025-01-12T21:07:15.149Z In(05)+ vmx 
2025-01-12T21:07:15.149Z In(05) vmx VMMEM: Precise Reservation: 2704MB (MainMem=8192MB)
2025-01-12T21:07:15.149Z In(05) vmx VMXSTATS: Registering 48 stats: vmx.overheadMemSize
2025-01-12T21:07:15.149Z In(05) vmx Vix: [mainDispatch.c:1058]: VMAutomation_PowerOn. Powering on.
2025-01-12T21:07:15.153Z No(00) vmx PowerOnTiming: ALL took 14147817 us
2025-01-12T21:07:15.153Z In(05) vmx VMX_PowerOn: ModuleTable_PowerOn = 1
2025-01-12T21:07:15.153Z No(00) vmx ConfigDB: Setting cleanShutdown = "FALSE"
2025-01-12T21:07:15.153Z No(00) vmx ConfigDB: Setting softPowerOff = "FALSE"
2025-01-12T21:07:15.181Z In(05) vcpu-0 VTHREAD 13704 "vcpu-0"
2025-01-12T21:07:15.185Z In(05) vcpu-0 MonTimer APIC:0/0 vec: 0
2025-01-12T21:07:15.185Z In(05) vcpu-0 APIC: version = 0x15, max LVT = 6, LDR = 0x2000000, DFR = 0xffffffff
2025-01-12T21:07:15.185Z In(05) vcpu-0 Active HV capabilities
2025-01-12T21:07:15.185Z In(05) vcpu-0    Virtual interrupt delivery
2025-01-12T21:07:15.185Z In(05) vcpu-0    XAPIC MMIO virtualization
2025-01-12T21:07:15.185Z In(05) vcpu-0    Full decode
2025-01-12T21:07:15.185Z In(05) vcpu-0    Nested paging A/D bits
2025-01-12T21:07:15.185Z In(05) vcpu-0    Real-address mode
2025-01-12T21:07:15.185Z In(05) vcpu-0    Skip debug state
2025-01-12T21:07:15.185Z In(05) vcpu-0    X2APIC virtualization
2025-01-12T21:07:15.185Z In(05) vcpu-0    TPR MMIO virtualization
2025-01-12T21:07:15.185Z In(05) vcpu-0    Page-modification logging
2025-01-12T21:07:15.185Z In(05) vcpu-0    ENCLS exiting
2025-01-12T21:07:15.185Z In(05) vcpu-0    PAUSE-loop exiting
2025-01-12T21:07:15.185Z In(05) vcpu-0    TSC scaling
2025-01-12T21:07:15.185Z In(05) vcpu-0    Advanced exit information for EPT violations
2025-01-12T21:07:15.185Z In(05) vcpu-0    Mode-based execute control for nested paging
2025-01-12T21:07:15.185Z In(05) vcpu-0    EPT-violation virtualization exception
2025-01-12T21:07:15.185Z In(05) vcpu-0    Event injection with instruction length zero
2025-01-12T21:07:15.217Z In(05) vcpu-0 TSC scaling ratio: 0000_fffff1e80000 (mult=4294963688, shift=32)
2025-01-12T21:07:15.217Z In(05) vcpu-1 VTHREAD 11088 "vcpu-1"
2025-01-12T21:07:15.217Z In(05) vcpu-1 MonTimer APIC:0/0 vec: 0
2025-01-12T21:07:15.217Z In(05) vcpu-1 APIC: version = 0x15, max LVT = 6, LDR = 0x2000000, DFR = 0xffffffff
2025-01-12T21:07:15.217Z In(05) vcpu-1 TSC scaling ratio: 0000_fffff1e80000 (mult=4294963688, shift=32)
2025-01-12T21:07:15.217Z In(05) vcpu-0 GuestRpc: Using vsocket for TCLO messaging is disabled.
2025-01-12T21:07:15.217Z In(05) vcpu-0 memoryHotplug: Node 0: Present: 8191 MB (100 %) Size:8191 MB (100 %)
2025-01-12T21:07:15.217Z In(05) vcpu-0 PIIX4: PM Resuming from suspend type 0x5, chipset.onlineStandby 0
2025-01-12T21:07:15.522Z In(05) vcpu-0 SOUND 871.755145 ES1371Win32GetMixerCtl mixerGetLineInfo error 1024
2025-01-12T21:07:15.524Z In(05) vcpu-0 VNET: 'ethernet0' enable link state propagation, lsp.state = 5
2025-01-12T21:07:15.526Z In(05) vcpu-0 VNET: MACVNetConnectToNetwork 'ethernet0' lsp.state = 4
2025-01-12T21:07:15.526Z In(05) vcpu-0 VNET: MACVNetConnectToNetwork 'Ethernet0' notify available.
2025-01-12T21:07:15.526Z In(05) vcpu-0 HGFSPublish: publishing 1 shares
2025-01-12T21:07:15.532Z In(05) vcpu-0 Win32U_GetFileAttributes: GetFileAttributesExW("C:\Users\<USER>\Desktop\Kali 2024 x64 Customized by zSecurity v1.2.vmwarevm\Kali 2024 x64 Customized by zSecurity v1.2.vmpl", ...) failed, error: 2
2025-01-12T21:07:15.532Z In(05) vcpu-0 PolicyVMXFindPolicyKey: policy file does not exist.
2025-01-12T21:07:15.546Z In(05) vcpu-0 DEVSWAP: GuestOS does not require LSI adapter swap.
2025-01-12T21:07:15.546Z In(05) vcpu-0 Intel VT: FlexPriority enabled, VPID enabled.
2025-01-12T21:07:15.548Z In(05) worker-11200 MainMem: Begin lazy IO (2097152 pages, 2097152 done, 1 threads, bio = 0).
2025-01-12T21:07:15.548Z In(05) worker-11200 MainMem: End lazy IO (2097152 done, sync = 0, error = 0).
2025-01-12T21:07:15.548Z Wa(03) vcpu-0 USB: Disconnecting missing device on usb port 0.
2025-01-12T21:07:15.548Z In(05) svga SVGA-ScreenMgr: Screen type changed to ScreenTarget
2025-01-12T21:07:15.548Z In(05) svga SWBScreen: Screen 1 Defined: xywh(0, 0, 1718, 878) flags=0x2
2025-01-12T21:07:15.556Z In(05) vcpu-0 SVGA: Registering MemSpace at 0xe8000000(0xe8000000) and 0xfe000000(0xfe000000)
2025-01-12T21:07:15.556Z In(05) vcpu-0 SVGA: FIFO is already mapped
2025-01-12T21:07:15.556Z In(05) vcpu-0 TSC scaling ratio: 0000_fffff1e80000 (mult=4294963688, shift=32)
2025-01-12T21:07:15.558Z In(05) vcpu-0 VMXSTATS: Registering 49 stats: vmx.vigor.opsTotal
2025-01-12T21:07:15.558Z In(05) vcpu-0 VMXSTATS: Registering 50 stats: vmx.vigor.opsPerS
2025-01-12T21:07:15.558Z In(05) vcpu-0 VMXSTATS: Registering 51 stats: vmx.vigor.queriesPerS
2025-01-12T21:07:15.558Z In(05) vcpu-0 VMXSTATS: Registering 52 stats: vmx.poll.itersPerS
2025-01-12T21:07:15.558Z In(05) vcpu-0 VMXSTATS: Registering 53 stats: vmx.userRpc.opsPerS
2025-01-12T21:07:15.558Z In(05) vcpu-0 VMXSTATS: Registering 54 stats: vmx.metrics.lastUpdate
2025-01-12T21:07:15.558Z No(00) vcpu-0 Metrics lastUpdate (s): 82871
2025-01-12T21:07:15.558Z In(05) vcpu-0 Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=0, newAppState=1872, success=1 additionalError=0
2025-01-12T21:07:15.558Z In(05) vcpu-0 Vix: [mainDispatch.c:4129]: VMAutomationReportPowerStateChange: Reporting power state change (opcode=0, err=0).
2025-01-12T21:07:15.558Z In(05) vcpu-0 Vix: [mainDispatch.c:4129]: VMAutomationReportPowerStateChange: Reporting power state change (opcode=2, err=0).
2025-01-12T21:07:15.558Z In(05) vcpu-0 Transitioned vmx/execState/val to poweredOn
2025-01-12T21:07:15.558Z In(05) vcpu-0 Tools: Adding Tools inactivity timer.
2025-01-12T21:07:15.560Z In(05) vmx USB: New set of 1 USB devices.
2025-01-12T21:07:15.560Z In(05) vmx USB: Found device [name:Virtual\ Bluetooth\ Adapter vid:0e0f pid:0008 speed:full family:wireless,bluetooth deviceType:virtual-bluetooth info:0000001 version:5]
2025-01-12T21:07:15.560Z In(05) vmx VNET: MACVNetLinkStateEventHandler: event, up:1, adapter:0
2025-01-12T21:07:15.560Z In(05) vmx VNET: MACVNetLinkStateEventHandler: 'ethernet0' state from 4 to 6.
2025-01-12T21:07:15.562Z In(05) vmx ToolsISO: Refreshing imageName for 'debian12-64' (refreshCount=1, lastCount=1).
2025-01-12T21:07:15.562Z In(05) vmx ToolsISO: open of C:\Program Files (x86)\VMware\VMware Workstation\isoimages_manifest.txt.sig failed: Could not find the file
2025-01-12T21:07:15.562Z In(05) vmx ToolsISO: Unable to read signature file 'C:\Program Files (x86)\VMware\VMware Workstation\isoimages_manifest.txt.sig', ignoring.
2025-01-12T21:07:15.562Z In(05) vmx ToolsISO: Updated cached value for imageName to 'linux.iso'.
2025-01-12T21:07:15.562Z In(05) vmx ToolsISO: Selected Tools ISO 'linux.iso' for 'debian12-64' guest.
2025-01-12T21:07:15.562Z In(05) vmx TOOLS updated cached value for isoImageExists to 1.
2025-01-12T21:07:15.562Z In(05) vmx VMXVmdb_SetToolsVersionStatus: status value set to 'unmanaged', 'unmanaged', install possible
2025-01-12T21:07:15.562Z In(05) vmx MainMem: Completed pending lazy checkpoint restore (1).
2025-01-12T21:07:15.562Z In(05) vmx CPT: Deleting checkpoint state, 'C:\Users\<USER>\Desktop\Kali 2024 x64 Customized by zSecurity v1.2.vmwarevm\Kali 2024 x64 Customized by zSecurity v1.2-f65b28b0.vmss'.
2025-01-12T21:07:15.566Z In(05) vmx USB: New set of 3 USB devices.
2025-01-12T21:07:15.566Z In(05) vmx USB: Found device [name:Bison\ Integrated\ Camera vid:5986 pid:1135 path:1/0/4 speed:high family:unknown instanceId:USB\\VID_5986&PID_1135\\200901010001 serialnum:200901010001 arbRuntimeKey:1 version:5]
2025-01-12T21:07:15.566Z In(05) vmx USB: Found device [name:Intel(R)\ Wireless\ Bluetooth(R) vid:8087 pid:0026 path:1/0/9 speed:full family:wireless,bluetooth instanceId:USB\\VID_8087&PID_0026\\5&2FFBEC60&0&10 arbRuntimeKey:2 version:5]
2025-01-12T21:07:15.566Z In(05) vmx USB: Found device [name:Virtual\ Bluetooth\ Adapter vid:0e0f pid:0008 speed:full family:wireless,bluetooth deviceType:virtual-bluetooth info:0000001 version:5]
2025-01-12T21:07:15.622Z In(05) mks MKSControlMgr: connected
2025-01-12T21:07:15.638Z In(05) mks MKS-VMDB: VMDB requested a screenshot
2025-01-12T21:07:15.638Z In(05) svga MKSScreenShotMgr: Taking a screenshot
2025-01-12T21:07:15.641Z In(05) vcpu-0 Preparing for SPEC_CTRL Guest MSR write (0x48) passthrough.
2025-01-12T21:07:15.672Z In(05) vcpu-0 CPT: vmstart
2025-01-12T21:07:15.672Z In(05) vcpu-1 CPT: vmstart
2025-01-12T21:07:15.692Z In(05) vmx DEVSWAP: GuestOS does not require LSI adapter swap.
2025-01-12T21:07:15.697Z Wa(03) vcpu-1 GuestRpc: application toolbox-dnd, changing channel 65535 -> 1
2025-01-12T21:07:15.697Z In(05) vcpu-1 GuestRpc: Channel 1, guest application toolbox-dnd.
2025-01-12T21:07:15.697Z In(05) vcpu-1 TOOLS: appName=toolbox-dnd, oldStatus=0, status=1, guestInitiated=0.
2025-01-12T21:07:15.704Z In(05) vmx DEVSWAP: GuestOS does not require LSI adapter swap.
2025-01-12T21:07:15.704Z Wa(03) vcpu-0 GuestRpc: application toolbox, changing channel 65535 -> 0
2025-01-12T21:07:15.704Z In(05) vcpu-0 GuestRpc: Channel 0, guest application toolbox.
2025-01-12T21:07:15.704Z In(05) vcpu-0 Tools: [AppStatus] Last heartbeat value 0 (never received)
2025-01-12T21:07:15.704Z In(05) vcpu-0 TOOLS: appName=toolbox, oldStatus=0, status=1, guestInitiated=0.
2025-01-12T21:07:15.713Z In(05) vmx DnDCP: set guest controllers to version 4
2025-01-12T21:07:15.728Z In(05) vmx DnDCP: set guest controllers to version 4
2025-01-12T21:07:15.764Z In(05) vmx Tools: Changing running status: 0 => 2.
2025-01-12T21:07:15.764Z In(05) vmx Tools: [RunningStatus] Last heartbeat value 0 (never received)
2025-01-12T21:07:15.764Z In(05) vmx Tools: Removing Tools inactivity timer.
2025-01-12T21:07:15.767Z In(05) vmx USB: DevID(700000010e0f0008): Connecting device desc:name:Virtual\ Bluetooth\ Adapter vid:0e0f pid:0008 speed:full family:wireless,bluetooth deviceType:virtual-bluetooth info:0000001 version:5.
2025-01-12T21:07:15.777Z In(05) vmx WORKER: Creating new group with maxThreads=16 (52)
2025-01-12T21:07:15.777Z In(05) vmx USB: New set of 3 USB devices.
2025-01-12T21:07:15.777Z In(05) vmx USB: Found device [name:Bison\ Integrated\ Camera vid:5986 pid:1135 path:1/0/4 speed:high family:unknown instanceId:USB\\VID_5986&PID_1135\\200901010001 serialnum:200901010001 arbRuntimeKey:1 version:5]
2025-01-12T21:07:15.777Z In(05) vmx USB: Found device [name:Intel(R)\ Wireless\ Bluetooth(R) vid:8087 pid:0026 path:1/0/9 speed:full family:wireless,bluetooth instanceId:USB\\VID_8087&PID_0026\\5&2FFBEC60&0&10 arbRuntimeKey:2 version:5]
2025-01-12T21:07:15.777Z In(05) vmx USB: Found device [name:Virtual\ Bluetooth\ Adapter vid:0e0f pid:0008 speed:full family:wireless,bluetooth virtPath:usb:0 deviceType:virtual-bluetooth info:0000001 version:5], connected to usb port 0.
2025-01-12T21:07:15.786Z In(05) vmx TOOLS Received tools.set.versiontype rpc call, version = 12421, type = 4
2025-01-12T21:07:15.786Z In(05) vmx TOOLS Setting toolsVersionStatus = TOOLS_STATUS_UNMANAGED
2025-01-12T21:07:15.786Z In(05) vmx Tools_SetVersionAndType did nothing; new tools version (12421) and type (4) match old Tools version and type
2025-01-12T21:07:15.788Z In(05) vmx Guest: Executing script for state change 'OS_Resume'.
2025-01-12T21:07:15.793Z In(05) vcpu-0 Tools: State change '4' progress: last event 0, event 1, success 1.
2025-01-12T21:07:15.865Z In(05) vmx VNET: MACVNetLinkStateTimerHandler: 'ethernet0' state from 6 to 1.
2025-01-12T21:07:15.995Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 1.
2025-01-12T21:07:15.995Z In(05) mks SWBWindow: Window 0 Defined: src screenId=-1, src xywh(0, 0, 1718, 878) dest xywh(0, 0, 1718, 878) pixelScale=1, flags=0xD
2025-01-12T21:07:15.995Z In(05) mks GDI-Backend: successfully started by HWinMux to do window composition.
2025-01-12T21:07:16.014Z In(05) mks MKS-HWinMux: Started GDI presentation backend.
2025-01-12T21:07:16.017Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 2.
2025-01-12T21:07:16.017Z In(05) mks SWBWindow: Window 1 Defined: src screenId=-1, src xywh(0, 0, 1718, 878) dest xywh(0, 0, 1718, 878) pixelScale=1, flags=0x10
2025-01-12T21:07:16.025Z In(05) vmx DnDCP: set guest controllers to version 4
2025-01-12T21:07:16.895Z In(05) vmx GuestInfo: HostinfoDetailedDataHeader version: 1
2025-01-12T21:07:16.895Z No(00) vmx ConfigDB: Setting guestInfo.detailed.data = <not printed>
2025-01-12T21:07:16.910Z No(00) vmx ConfigDB: Setting ide1:0.fileName = "auto detect"
2025-01-12T21:07:16.975Z In(05) vcpu-0 TOOLS call to unity.show.taskbar failed.
2025-01-12T21:07:17.373Z In(05) vcpu-0 VLANCE: Returning 0x0 for LANCE_EXTINT IN
2025-01-12T21:07:17.373Z In(05) vcpu-0 VLANCE: Ignoring LANCE_EXTINT OUT of 0x1
2025-01-12T21:07:17.515Z In(05) vmx CPClipboard_SetItem: Set CPClipboard struct with data of size:1876, format:5.
2025-01-12T21:07:17.588Z In(05) vcpu-0 VLANCE: IN on LANCE_MODE while not stopped: 0x73
2025-01-12T21:07:17.589Z In(05) vcpu-0 VLANCE: OUT on LANCE_MODE while not stopped: 0x73, word: 0x80
2025-01-12T21:07:17.589Z In(05) vcpu-0 VLANCE: OUT on LANCE_LADRF0 while not stopped: 0x73, word: 0x0
2025-01-12T21:07:17.589Z In(05) vcpu-0 VLANCE: OUT on LANCE_LADRF1 while not stopped: 0x73, word: 0x0
2025-01-12T21:07:17.589Z In(05) vcpu-0 VLANCE: OUT on LANCE_LADRF2 while not stopped: 0x73, word: 0x0
2025-01-12T21:07:17.589Z In(05) vcpu-0 VLANCE: OUT on LANCE_LADRF3 while not stopped: 0x73, word: 0x0
2025-01-12T21:07:18.227Z In(05) vmx Guest: Script exit code: 0, success = 1
2025-01-12T21:07:18.227Z In(05) vmx TOOLS state change 4 returned status 1
2025-01-12T21:07:18.227Z In(05) vmx Tools: State change '4' progress: last event 1, event 2, success 1.
2025-01-12T21:07:18.227Z In(05) vmx Tools: State change '4' progress: last event 1, event 4, success 1.
2025-01-12T21:07:18.227Z In(05) vmx Vix: [mainDispatch.c:4129]: VMAutomationReportPowerStateChange: Reporting power state change (opcode=2, err=0).
2025-01-12T21:07:18.228Z In(05) vmx Tools: Changing running status: 2 => 1.
2025-01-12T21:07:18.228Z In(05) vmx Tools: [RunningStatus] Last heartbeat value 3 (last received 0s ago)
2025-01-12T21:07:19.384Z In(05) vcpu-1 DDB: "longContentID" = "066a1fb9f3816b666db0b83db3efa746" (was "a550c6b486676fac5d8345472e12bca8")
2025-01-12T21:07:27.940Z In(05) vmx VNET: MACVNetLinkStateTimerHandler: 'ethernet0' state from 1 to 5.
2025-01-12T21:08:13.609Z In(05) vmx Tools: sending 'OS_Suspend' (state = 5) state change request
2025-01-12T21:08:13.609Z In(05) vmx Tools: Changing running status: 1 => 2.
2025-01-12T21:08:13.609Z In(05) vmx Tools: [RunningStatus] Last heartbeat value 58 (last received 0s ago)
2025-01-12T21:08:13.611Z In(05) vmx Vix: [vmxCommands.c:839]: VMAutomation_SuspendImpl: SoftSuspend succeeded.
2025-01-12T21:08:13.628Z In(05) vmx Guest: Executing script for state change 'OS_Suspend'.
2025-01-12T21:08:13.631Z In(05) vcpu-1 Tools: State change '5' progress: last event 0, event 1, success 1.
2025-01-12T21:08:14.964Z In(05) vcpu-0 VLANCE: Reserved value written to sstyle register 4
2025-01-12T21:08:14.999Z In(05) vmx Guest: Script exit code: 0, success = 1
2025-01-12T21:08:15.000Z In(05) vmx TOOLS state change 5 returned status 1
2025-01-12T21:08:15.000Z In(05) vmx Tools: State change '5' progress: last event 1, event 2, success 1.
2025-01-12T21:08:15.000Z In(05) vmx SUSPEND: Start suspend (flags=0)
2025-01-12T21:08:15.002Z In(05) svga MKSScreenShotMgr: Taking a screenshot
2025-01-12T21:08:15.064Z In(05) vcpu-0 Closing all the disks of the VM.
2025-01-12T21:08:15.064Z In(05) vcpu-0 Closing disk 'scsi0:0'
2025-01-12T21:08:15.080Z In(05) vcpu-0 Progress -1% (msg.checkpoint.saveStatus)
2025-01-12T21:08:15.080Z In(05) vcpu-0 Checkpointed in VMware Workstation, 17.5.2, build-23775571, Windows Host
2025-01-12T21:08:15.098Z In(05) vcpu-0 MainMem: Keep paging file 'C:\Users\<USER>\Desktop\Kali 2024 x64 Customized by zSecurity v1.2.vmwarevm\Kali 2024 x64 Customized by zSecurity v1.2-f65b28b0.vmem' as memory image.
2025-01-12T21:08:15.098Z In(05) vcpu-0 Progress 0% (none)
2025-01-12T21:08:15.098Z In(05) vcpu-0 Progress 1% (none)
2025-01-12T21:08:15.100Z In(05) vcpu-0 Progress 2% (none)
2025-01-12T21:08:15.101Z In(05) vcpu-0 Progress 3% (none)
2025-01-12T21:08:15.102Z In(05) vcpu-0 Progress 4% (none)
2025-01-12T21:08:15.102Z In(05) vcpu-0 Progress 5% (none)
2025-01-12T21:08:15.102Z In(05) vcpu-0 Progress 6% (none)
2025-01-12T21:08:15.103Z In(05) vcpu-0 Progress 7% (none)
2025-01-12T21:08:15.103Z In(05) vcpu-0 Progress 8% (none)
2025-01-12T21:08:15.104Z In(05) vcpu-0 Progress 9% (none)
2025-01-12T21:08:15.104Z In(05) vcpu-0 Progress 10% (none)
2025-01-12T21:08:15.105Z In(05) vcpu-0 Progress 11% (none)
2025-01-12T21:08:15.105Z In(05) vcpu-0 Progress 12% (none)
2025-01-12T21:08:15.106Z In(05) vcpu-0 Progress 13% (none)
2025-01-12T21:08:15.106Z In(05) vcpu-0 Progress 14% (none)
2025-01-12T21:08:15.106Z In(05) vcpu-0 Progress 15% (none)
2025-01-12T21:08:15.107Z In(05) vcpu-0 Progress 16% (none)
2025-01-12T21:08:15.107Z In(05) vcpu-0 Progress 17% (none)
2025-01-12T21:08:15.108Z In(05) vcpu-0 Progress 18% (none)
2025-01-12T21:08:15.109Z In(05) vcpu-0 Progress 19% (none)
2025-01-12T21:08:15.109Z In(05) vcpu-0 Progress 20% (none)
2025-01-12T21:08:15.110Z In(05) vcpu-0 Progress 21% (none)
2025-01-12T21:08:15.110Z In(05) vcpu-0 Progress 22% (none)
2025-01-12T21:08:15.110Z In(05) vcpu-0 Progress 23% (none)
2025-01-12T21:08:15.111Z In(05) vcpu-0 Progress 24% (none)
2025-01-12T21:08:15.111Z In(05) vcpu-0 Progress 25% (none)
2025-01-12T21:08:15.112Z In(05) vcpu-0 Progress 26% (none)
2025-01-12T21:08:15.112Z In(05) vcpu-0 Progress 27% (none)
2025-01-12T21:08:15.113Z In(05) vcpu-0 Progress 28% (none)
2025-01-12T21:08:15.113Z In(05) vcpu-0 Progress 29% (none)
2025-01-12T21:08:15.114Z In(05) vcpu-0 Progress 30% (none)
2025-01-12T21:08:15.114Z In(05) vcpu-0 Progress 31% (none)
2025-01-12T21:08:15.115Z In(05) vcpu-0 Progress 32% (none)
2025-01-12T21:08:15.115Z In(05) vcpu-0 Progress 33% (none)
2025-01-12T21:08:15.116Z In(05) vcpu-0 Progress 34% (none)
2025-01-12T21:08:15.116Z In(05) vcpu-0 Progress 35% (none)
2025-01-12T21:08:15.116Z In(05) vcpu-0 Progress 36% (none)
2025-01-12T21:08:15.117Z In(05) vcpu-0 Progress 37% (none)
2025-01-12T21:08:15.119Z In(05) vcpu-0 Progress 38% (none)
2025-01-12T21:08:15.133Z In(05) vcpu-0 Progress 39% (none)
2025-01-12T21:08:15.146Z In(05) vcpu-0 Progress 40% (none)
2025-01-12T21:08:15.158Z In(05) vcpu-0 Progress 41% (none)
2025-01-12T21:08:15.166Z In(05) vcpu-0 Progress 42% (none)
2025-01-12T21:08:15.168Z In(05) vcpu-0 Progress 43% (none)
2025-01-12T21:08:15.171Z In(05) vcpu-0 Progress 44% (none)
2025-01-12T21:08:15.173Z In(05) vcpu-0 Progress 45% (none)
2025-01-12T21:08:15.175Z In(05) vcpu-0 Progress 46% (none)
2025-01-12T21:08:15.177Z In(05) vcpu-0 Progress 47% (none)
2025-01-12T21:08:15.178Z In(05) vcpu-0 Progress 48% (none)
2025-01-12T21:08:15.180Z In(05) vcpu-0 Progress 49% (none)
2025-01-12T21:08:15.185Z In(05) vcpu-0 Progress 50% (none)
2025-01-12T21:08:15.188Z In(05) vcpu-0 Progress 51% (none)
2025-01-12T21:08:15.193Z In(05) vcpu-0 Progress 52% (none)
2025-01-12T21:08:15.196Z In(05) vcpu-0 Progress 53% (none)
2025-01-12T21:08:15.201Z In(05) vcpu-0 Progress 54% (none)
2025-01-12T21:08:15.207Z In(05) vcpu-0 Progress 55% (none)
2025-01-12T21:08:15.210Z In(05) vcpu-0 Progress 56% (none)
2025-01-12T21:08:15.214Z In(05) vcpu-0 Progress 57% (none)
2025-01-12T21:08:15.216Z In(05) vcpu-0 Progress 58% (none)
2025-01-12T21:08:15.220Z In(05) vcpu-0 Progress 59% (none)
2025-01-12T21:08:15.226Z In(05) vcpu-0 Progress 60% (none)
2025-01-12T21:08:15.234Z In(05) vcpu-0 Progress 61% (none)
2025-01-12T21:08:15.239Z In(05) vcpu-0 Progress 62% (none)
2025-01-12T21:08:15.246Z In(05) vcpu-0 Progress 63% (none)
2025-01-12T21:08:15.248Z In(05) vcpu-0 Progress 64% (none)
2025-01-12T21:08:15.258Z In(05) vcpu-0 Progress 65% (none)
2025-01-12T21:08:15.264Z In(05) vcpu-0 Progress 66% (none)
2025-01-12T21:08:15.272Z In(05) vcpu-0 Progress 67% (none)
2025-01-12T21:08:15.276Z In(05) vcpu-0 Progress 68% (none)
2025-01-12T21:08:15.279Z In(05) vcpu-0 Progress 69% (none)
2025-01-12T21:08:15.282Z In(05) vcpu-0 Progress 70% (none)
2025-01-12T21:08:15.291Z In(05) vcpu-0 Progress 71% (none)
2025-01-12T21:08:15.297Z In(05) vcpu-0 Progress 72% (none)
2025-01-12T21:08:15.307Z In(05) vcpu-0 Progress 73% (none)
2025-01-12T21:08:15.311Z In(05) vcpu-0 Progress 74% (none)
2025-01-12T21:08:15.314Z In(05) vcpu-0 Progress 75% (none)
2025-01-12T21:08:15.317Z In(05) vcpu-0 Progress 76% (none)
2025-01-12T21:08:15.323Z In(05) vcpu-0 Progress 77% (none)
2025-01-12T21:08:15.334Z In(05) vcpu-0 Progress 78% (none)
2025-01-12T21:08:15.341Z In(05) vcpu-0 Progress 79% (none)
2025-01-12T21:08:15.347Z In(05) vcpu-0 Progress 80% (none)
2025-01-12T21:08:15.355Z In(05) vcpu-0 Progress 81% (none)
2025-01-12T21:08:15.362Z In(05) vcpu-0 Progress 82% (none)
2025-01-12T21:08:15.366Z In(05) vcpu-0 Progress 83% (none)
2025-01-12T21:08:15.377Z In(05) vcpu-0 Progress 84% (none)
2025-01-12T21:08:15.383Z In(05) vcpu-0 Progress 85% (none)
2025-01-12T21:08:15.391Z In(05) vcpu-0 Progress 86% (none)
2025-01-12T21:08:15.403Z In(05) vcpu-0 Progress 87% (none)
2025-01-12T21:08:15.411Z In(05) vcpu-0 Progress 88% (none)
2025-01-12T21:08:15.414Z In(05) vcpu-0 Progress 89% (none)
2025-01-12T21:08:15.415Z In(05) vcpu-0 Progress 90% (none)
2025-01-12T21:08:15.416Z In(05) vcpu-0 Progress 91% (none)
2025-01-12T21:08:15.419Z In(05) vcpu-0 Progress 92% (none)
2025-01-12T21:08:15.422Z In(05) vcpu-0 Progress 93% (none)
2025-01-12T21:08:15.424Z In(05) vcpu-0 Progress 94% (none)
2025-01-12T21:08:15.427Z In(05) vcpu-0 Progress 95% (none)
2025-01-12T21:08:15.428Z In(05) vcpu-0 Progress 96% (none)
2025-01-12T21:08:15.430Z In(05) vcpu-0 Progress 97% (none)
2025-01-12T21:08:15.430Z In(05) vcpu-0 Progress 98% (none)
2025-01-12T21:08:15.435Z In(05) vcpu-0 Progress 99% (none)
2025-01-12T21:08:15.470Z In(05) vcpu-0 SVGA: Guest reported SVGA driver: (2, 101187595, 34865152, 0)
2025-01-12T21:08:15.474Z In(05) vcpu-0 Progress 100% (none)
2025-01-12T21:08:15.475Z In(05) vcpu-0 GuestRpc: Reinitializing Channel 0(toolbox)
2025-01-12T21:08:15.475Z In(05) vcpu-0 GuestMsg: Channel 0, Cannot unpost because the previous post is already completed
2025-01-12T21:08:15.475Z In(05) vcpu-0 Tools: [AppStatus] Last heartbeat value 60 (last received 0s ago)
2025-01-12T21:08:15.475Z In(05) vcpu-0 TOOLS: appName=toolbox, oldStatus=1, status=0, guestInitiated=0.
2025-01-12T21:08:15.488Z In(05) vcpu-0 GuestRpc: Reinitializing Channel 1(toolbox-dnd)
2025-01-12T21:08:15.488Z In(05) vcpu-0 GuestMsg: Channel 1, Cannot unpost because the previous post is already completed
2025-01-12T21:08:15.488Z In(05) vcpu-0 TOOLS: appName=toolbox-dnd, oldStatus=1, status=0, guestInitiated=0.
2025-01-12T21:08:15.490Z In(05) vcpu-0 DEPLOYPKG: ToolsDeployPkgCptSave: state=0 err=0 (null msg)
2025-01-12T21:08:15.491Z In(05) vcpu-0 Progress 101% (none)
2025-01-12T21:08:15.503Z No(00) vcpu-0 ConfigDB: Setting checkpoint.vmState = "Kali 2024 x64 Customized by zSecurity v1.2-f65b28b0.vmss"
2025-01-12T21:08:15.532Z In(05) vcpu-0 Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=1, newAppState=1875, success=1 additionalError=0
2025-01-12T21:08:15.532Z In(05) vcpu-0 SUSPEND: Completed suspend: 'Operation completed successfully' (0)
2025-01-12T21:08:15.532Z In(05) vcpu-0 Tools: State change '5' progress: last event 2, event 4, success 1.
2025-01-12T21:08:15.532Z In(05) vcpu-0 Tools: Changing running status: 2 => 1.
2025-01-12T21:08:15.532Z In(05) vcpu-0 Tools: [RunningStatus] Last heartbeat value 60 (last received 0s ago)
2025-01-12T21:08:15.533Z In(05) vmx Stopping VCPU threads...
2025-01-12T21:08:15.533Z In(05) vmx Vix: [mainDispatch.c:4651]: VMAutomationProcessMessage: Abort the command. VMX is shutting down
2025-01-12T21:08:15.534Z In(05) vmx MKSThread: Requesting MKS exit
2025-01-12T21:08:15.534Z In(05) vmx Stopping MKS/SVGA threads
2025-01-12T21:08:15.535Z In(05) svga SWBScreen: Screen 1 Destroyed: xywh(0, 0, 1718, 878) flags=0x2
2025-01-12T21:08:15.536Z In(05) svga SVGA thread is exiting the main loop
2025-01-12T21:08:15.536Z In(05) mks SWBWindow: Window 0 Destroyed: src screenId=-1, src xywh(0, 0, 1718, 878) dest xywh(0, 0, 1718, 878) pixelScale=1, flags=0xF
2025-01-12T21:08:15.541Z In(05) mks SWBWindow: Number of MKSWindows changed: 0 rendering MKSWindow(s) of total 1.
2025-01-12T21:08:15.541Z In(05) mks SWBWindow: Window 1 Destroyed: src screenId=-1, src xywh(0, 0, 1718, 878) dest xywh(0, 0, 1718, 878) pixelScale=1, flags=0x10
2025-01-12T21:08:15.541Z In(05) mks GDI-Backend: stopped by HWinMux to do window composition.
2025-01-12T21:08:15.541Z In(05) mks SWBWindow: Number of MKSWindows changed: 0 rendering MKSWindow(s) of total 0.
2025-01-12T21:08:15.548Z In(05) vmx MKS/SVGA threads are stopped
2025-01-12T21:08:15.548Z In(05) vmx USB: DevID(700000010e0f0008): Disconnecting device.
2025-01-12T21:08:15.549Z In(05) vmx 
2025-01-12T21:08:15.549Z In(05)+ vmx OvhdMem: Final (Power Off) Overheads
2025-01-12T21:08:15.549Z In(05) vmx                                                       reserved      |          used
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem excluded                                  cur    max    avg |    cur    max    avg
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem OvhdUser_MainMem                    :  2097152 2097152      - | 392991 392991      -
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem OvhdUser_VmxText                    :    7680   7680      - |      0      0      -
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem OvhdUser_VmxTextLibs                :   17408  17408      - |      0      0      -
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem Total excluded                      :  2122240 2122240      - |      -      -      -
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem Actual maximum                      :         2122240        |             -
2025-01-12T21:08:15.549Z In(05)+ vmx 
2025-01-12T21:08:15.549Z In(05) vmx                                                       reserved      |          used
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem paged                                     cur    max    avg |    cur    max    avg
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem OvhdUser_STATS_vmm                  :       4      4      - |      0      0      -
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem OvhdUser_STATS_device               :       2      2      - |      0      0      -
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem OvhdUser_SvgaMobFallback            :  196608 196608      - |      0      0      -
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem OvhdUser_DiskLibMemUsed             :    3075   3075      - |      0      0      -
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem OvhdUser_SvgaSurfaceTable           :       6      6      - |      1      1      -
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem OvhdUser_SvgaBESurfaceTable         :       4      4      - |      4      4      -
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem OvhdUser_SvgaSDirtyCache            :      96     96      - |      0      0      -
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem OvhdUser_SvgaCursor                 :      10     10      - |     10     10      -
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem OvhdUser_SvgaPPNList                :     258    258      - |      0      0      -
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem OvhdUser_VmxGlobals                 :   10240  10240      - |      0      0      -
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem OvhdUser_VmxGlobalsLibs             :    3584   3584      - |      0      0      -
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem OvhdUser_VmxHeap                    :    8704   8704      - |      0      0      -
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem OvhdUser_VmxMks                     :      33     33      - |      1      1      -
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem OvhdUser_VmxMksRenderOps            :     678    678      - |    492    492      -
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem OvhdUser_VmxMks3d                   :  131072 131072      - |      0      0      -
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem OvhdUser_VmxMksScreenTemp           :   69890  69890      - |      0    187      -
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem OvhdUser_VmxMksVnc                  :   74936  74936      - |      0      0      -
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem OvhdUser_VmxMksScreen               :  131075 131075      - |      0   1474      -
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem OvhdUser_VmxMksSVGAVO               :    4096   4096      - |      0      0      -
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem OvhdUser_VmxMksSwbCursor            :    4096   4096      - |      8      8      -
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem OvhdUser_VmxPhysMemErrPages         :      10     10      - |      0      0      -
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem OvhdUser_VmxSLEntryBuf              :     128    128      - |      0      0      -
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem OvhdUser_VmxThreads                 :   35840  35840      - |      0      0      -
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem Total paged                         :  674445 674445      - |    516   2177      -
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem Actual maximum                      :         674445        |        674445
2025-01-12T21:08:15.549Z In(05)+ vmx 
2025-01-12T21:08:15.549Z In(05) vmx                                                       reserved      |          used
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem nonpaged                                  cur    max    avg |    cur    max    avg
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem OvhdUser_SharedArea                 :     148    148      - |    133    133      -
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem OvhdUser_BusMemTraceBitmap          :      69     69      - |      0      0      -
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem OvhdUser_PFrame                     :    4279   5314      - |   4279   4279      -
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem OvhdUser_VIDE_KSEG                  :      16     16      - |     16     16      -
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem OvhdUser_VGA                        :      64     64      - |     64     64      -
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem OvhdUser_BalloonMPN                 :       1      1      - |      1      1      -
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem OvhdUser_P2MUpdateBuffer            :       3      3      - |      0      0      -
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem OvhdUser_ServicesMPN                :       3      3      - |      2      2      -
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem OvhdUser_LocalApic                  :       2      2      - |      2      2      -
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem OvhdUser_VBIOS                      :       8      8      - |      0      0      -
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem OvhdUser_VnicGuest                  :      16     16      - |      0      0      -
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem OvhdUser_LSIBIOS                    :       4      4      - |      0      0      -
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem OvhdUser_LSIRings                   :       4      4      - |      0      0      -
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem OvhdUser_SAS1068BIOS                :       4      4      - |      0      0      -
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem OvhdUser_SBIOS                      :      16     16      - |      0      0      -
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem OvhdUser_FlashRam                   :     128    128      - |      0      0      -
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem OvhdUser_SVGAFB                     :    1024   1024      - |      0      0      -
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem OvhdUser_SVGAMEM                    :      64    512      - |      1      1      -
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem OvhdUser_HDAudioReg                 :       3      3      - |      0      0      -
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem OvhdUser_EHCIRegister               :       1      1      - |      1      1      -
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem OvhdUser_XhciRegister               :       1      1      - |      0      0      -
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem OvhdUser_HyperV                     :       2      2      - |      0      0      -
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem OvhdUser_ExtCfg                     :       4      4      - |      0      0      -
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem OvhdUser_vhvCachedVMCS              :       2      2      - |      0      0      -
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem OvhdUser_vhvNestedAPIC              :       2      2      - |      0      0      -
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem OvhdUser_LBR                        :       2      2      - |      0      0      -
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem OvhdUser_MonWired                   :      53     53      - |     53     53      -
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem OvhdUser_MonNuma                    :     252    252      - |      0      0      -
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem OvhdUser_NVDC                       :       1      1      - |      0      0      -
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem OvhdUser_PCIeMMIOArea               :      70     70      - |     34     34      -
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem Total nonpaged                      :    6246   7729      - |   4586   4586      -
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem Actual maximum                      :           6246        |          7729
2025-01-12T21:08:15.549Z In(05)+ vmx 
2025-01-12T21:08:15.549Z In(05) vmx                                                       reserved      |          used
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem anonymous                                 cur    max    avg |    cur    max    avg
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem OvhdMon_Alloc                       :     196    196      - |     58    150      -
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem OvhdMon_BusMemFrame                 :    2114   2171      - |   2114   2114      -
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem OvhdMon_BusMem2MInfo                :      32     32      - |     32     32      -
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem OvhdMon_BusMem1GInfo                :       1      1      - |      1      1      -
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem OvhdMon_BusMemZapListMPN            :       1      1      - |      1      1      -
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem OvhdMon_BusMemPreval                :       8      8      - |      0      0      -
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem OvhdMon_MonAS                       :       2      2      - |      1      1      -
2025-01-12T21:08:15.549Z In(05) vmx OvhdMem OvhdMon_GuestMem                    :      80     80      - |     60     60      -
2025-01-12T21:08:15.550Z In(05) vmx OvhdMem OvhdMon_TC                          :    1026   1026      - |    954    954      -
2025-01-12T21:08:15.550Z In(05) vmx OvhdMem OvhdMon_BusMemMonAS                 :       8      8      - |      8      8      -
2025-01-12T21:08:15.550Z In(05) vmx OvhdMem OvhdMon_PlatformMonAS               :      13     13      - |     10     10      -
2025-01-12T21:08:15.550Z In(05) vmx OvhdMem OvhdMon_HVNuma                      :       4      4      - |      0      0      -
2025-01-12T21:08:15.550Z In(05) vmx OvhdMem OvhdMon_HV                          :       2      2      - |      2      2      -
2025-01-12T21:08:15.550Z In(05) vmx OvhdMem OvhdMon_HVMSRBitmap                 :       1      1      - |      0      0      -
2025-01-12T21:08:15.550Z In(05) vmx OvhdMem OvhdMon_VHVGuestMSRBitmap           :       2      2      - |      0      0      -
2025-01-12T21:08:15.550Z In(05) vmx OvhdMem OvhdMon_VHV                         :       6      6      - |      0      0      -
2025-01-12T21:08:15.550Z In(05) vmx OvhdMem OvhdMon_Numa                        :      30     30      - |     14     28      -
2025-01-12T21:08:15.550Z In(05) vmx OvhdMem OvhdMon_NumaTextRodata              :     198    374      - |    176    352      -
2025-01-12T21:08:15.550Z In(05) vmx OvhdMem OvhdMon_NumaDataBss                 :      54     54      - |     52     52      -
2025-01-12T21:08:15.550Z In(05) vmx OvhdMem OvhdMon_NumaLargeData               :       0    512      - |      0      0      -
2025-01-12T21:08:15.550Z In(05) vmx OvhdMem OvhdMon_BaseWired                   :      56     58      - |     48     48      -
2025-01-12T21:08:15.550Z In(05) vmx OvhdMem OvhdMon_Bootstrap                   :       0   2303      - |      0    330      -
2025-01-12T21:08:15.550Z In(05) vmx OvhdMem OvhdMon_GPhysTraced                 :     875    875      - |    193    193      -
2025-01-12T21:08:15.550Z In(05) vmx OvhdMem OvhdMon_GPhysHWMMU                  :    4374   4374      - |   2287   2287      -
2025-01-12T21:08:15.550Z In(05) vmx OvhdMem OvhdMon_GPhysNoTrace                :     266    266      - |     69     69      -
2025-01-12T21:08:15.550Z In(05) vmx OvhdMem OvhdMon_PhysMemGart                 :     104    104      - |     96     96      -
2025-01-12T21:08:15.550Z In(05) vmx OvhdMem OvhdMon_PhysMemErr                  :       7      7      - |      0      0      -
2025-01-12T21:08:15.550Z In(05) vmx OvhdMem OvhdMon_VProbe                      :       1      1      - |      0      0      -
2025-01-12T21:08:15.550Z In(05) vmx OvhdMem Total anonymous                     :    9461  12511      - |   6176   6788      -
2025-01-12T21:08:15.550Z In(05) vmx OvhdMem Actual maximum                      :           9461        |         11823
2025-01-12T21:08:15.550Z In(05)+ vmx 
2025-01-12T21:08:15.550Z In(05) vmx VMMEM: Maximum Reservation: 2713MB (MainMem=8192MB)
2025-01-12T21:08:15.550Z In(05) vmx MemSched: BALLOON HIST [0, 2097152]: 61 61 0 0 0 0 0 0 0 0 0 0
2025-01-12T21:08:15.550Z In(05) vmx MemSched: BALLOON P50 1 P70 1 P90 1 MIN 0 MAX 0
2025-01-12T21:08:15.550Z In(05) vmx MemSched: SWAP HIST [0, 2097152]: 61 61 0 0 0 0 0 0 0 0 0 0
2025-01-12T21:08:15.550Z In(05) vmx MemSched: SWAP P50 1 P70 1 P90 1 MIN 0 MAX 0
2025-01-12T21:08:15.550Z In(05) vmx MemSched: LOCK HIST [0, 2097152]: 0 1 60 0 0 0 0 0 0 0 0 0
2025-01-12T21:08:15.550Z In(05) vmx MemSched: LOCK P50 20 P70 20 P90 20 MIN 4614 MAX 403761
2025-01-12T21:08:15.550Z In(05) vmx MemSched: LOCK_TARGET HIST [0, 2097152]: 0 0 0 0 0 0 0 0 61 0 0 0
2025-01-12T21:08:15.550Z In(05) vmx MemSched: LOCK_TARGET P50 80 P70 80 P90 80 MIN 1468477 MAX 1468477
2025-01-12T21:08:15.550Z In(05) vmx MemSched: ACTIVE_PCT HIST [0, 100]: 1 61 0 0 0 0 0 0 0 0 0 0
2025-01-12T21:08:15.550Z In(05) vmx MemSched: ACTIVE_PCT P50 10 P70 10 P90 10 MIN 0 MAX 8
2025-01-12T21:08:15.550Z In(05) vmx MemSched: NUM_VMS HIST [0, 10]: 0 0 61 0 0 0 0 0 0 0 0 0
2025-01-12T21:08:15.550Z In(05) vmx MemSched: NUM_VMS P50 20 P70 20 P90 20 MIN 1 MAX 1
2025-01-12T21:08:15.550Z In(05) vmx MemSched: HOSTLOCK HIST [0, 1579008]: 0 1 0 60 0 0 0 0 0 0 0 0
2025-01-12T21:08:15.550Z In(05) vmx MemSched: HOSTLOCK P50 30 P70 30 P90 30 MIN 4614 MAX 403761
2025-01-12T21:08:15.550Z In(05) vmx TOOLS received request in VMX to set option 'enableDnD' -> '0'
2025-01-12T21:08:15.550Z In(05) vmx GuestRpc: Attempt to send TCLO msg for 'toolbox-dnd' while GuestRpc is powering off.
2025-01-12T21:08:15.550Z In(05) vmx GuestRpc: Attempt to send TCLO msg for 'toolbox' while GuestRpc is powering off.
2025-01-12T21:08:15.550Z In(05) vmx TOOLS received request in VMX to set option 'copypaste' -> '0'
2025-01-12T21:08:15.550Z In(05) vmx GuestRpc: Attempt to send TCLO msg for 'toolbox-dnd' while GuestRpc is powering off.
2025-01-12T21:08:15.550Z In(05) vmx GuestRpc: Attempt to send TCLO msg for 'toolbox' while GuestRpc is powering off.
2025-01-12T21:08:15.551Z In(05) vmx HgfsServerManagerVigorExit: Destroy:
2025-01-12T21:08:15.551Z In(05) vmx Tools: ToolsRunningStatus_Exit, delayedRequest is 0x0
2025-01-12T21:08:15.551Z In(05) vmx Tools: Changing running status: 1 => 0.
2025-01-12T21:08:15.551Z In(05) vmx Tools: [RunningStatus] Last heartbeat value 60 (last received 0s ago)
2025-01-12T21:08:15.552Z Wa(03) vmx Guest attempted to revert hostVerifiedSamlToken capability; ignoring.
2025-01-12T21:08:15.553Z In(05) vmx GuestRpc: Closing channel 0 connection 4
2025-01-12T21:08:15.553Z Wa(03) vmx VMCI QueuePair: Couldn't inform guest about peer detach event.
2025-01-12T21:08:15.553Z In(05) vmx GuestRpc: Closing channel 1 connection 5
2025-01-12T21:08:15.553Z Wa(03) vmx VMCI QueuePair: Couldn't inform guest about peer detach event.
2025-01-12T21:08:15.553Z In(05) vmx USB: DevID(40e0f0002): Disconnecting device.
2025-01-12T21:08:15.553Z In(05) vmx USB: DevID(20e0f0002): Disconnecting device.
2025-01-12T21:08:15.554Z In(05) vmx USB: DevID(200000040e0f0003): Disconnecting device.
2025-01-12T21:08:15.555Z In(05) vmx USB: DevID(10e0f0002): Disconnecting device.
2025-01-12T21:08:15.559Z In(05) usbCCIDEnumCards USB-CCID: Card enum thread exiting.
2025-01-12T21:08:15.562Z In(05) vmx SOUNDLIB: Closing Wave sound backend.
2025-01-12T21:08:15.565Z In(05) mks MKSControlMgr: disconnected
2025-01-12T21:08:15.565Z In(05) mks MKS-RenderMain: Stopping MKSBasicOps
2025-01-12T21:08:15.565Z In(05) mks MKS-RenderMain: Stopping MKSBasicOps
2025-01-12T21:08:15.565Z In(05) mks MKS-RenderMain: Stopped MKSBasicOps
2025-01-12T21:08:15.567Z In(05) mks MKS PowerOff
2025-01-12T21:08:15.567Z In(05) mks MKS thread is exiting
2025-01-12T21:08:15.567Z In(05) svga SVGA thread is exiting
2025-01-12T21:08:15.568Z Wa(03) vmx 
2025-01-12T21:08:15.579Z In(05) vmx AIOWIN32C: asyncOps=744 syncOps=11 bufSize=448Kb fixedOps=202
2025-01-12T21:08:15.579Z In(05) aioCompletion AIO thread processed 744 completions
2025-01-12T21:08:17.015Z In(05) deviceThread Device thread is exiting
2025-01-12T21:08:17.016Z In(05) vmx Vix: [mainDispatch.c:1171]: VMAutomationPowerOff: Powering off.
2025-01-12T21:08:17.018Z In(05) vmx Win32U_GetFileAttributes: GetFileAttributesExW("C:\Users\<USER>\Desktop\Kali 2024 x64 Customized by zSecurity v1.2.vmwarevm\Kali 2024 x64 Customized by zSecurity v1.2.vmpl", ...) failed, error: 2
2025-01-12T21:08:17.019Z In(05) vmx Policy_SavePolicyFile: invalid arguments to function.
2025-01-12T21:08:17.019Z In(05) vmx PolicyVMX_Exit: Could not write out policies: 15.
2025-01-12T21:08:17.019Z In(05) vmx WORKER: asyncOps=3 maxActiveOps=1 maxPending=1 maxCompleted=2
2025-01-12T21:08:17.019Z In(05) PowerNotifyThread PowerNotify thread exiting.
2025-01-12T21:08:17.715Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=1, newAppState=1875, success=1 additionalError=0
2025-01-12T21:08:17.715Z In(05) vmx Vix: [mainDispatch.c:4230]: VMAutomation: Ignoring ReportPowerOpFinished because the VMX is shutting down.
2025-01-12T21:08:17.715Z In(05) vmx VMXSTATS: Ready to cleanup and munmap 1B10D1D0000.
2025-01-12T21:08:17.715Z No(00) vmx ConfigDB: Setting cleanShutdown = "TRUE"
2025-01-12T21:08:17.756Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=0, newAppState=1870, success=1 additionalError=0
2025-01-12T21:08:17.756Z In(05) vmx Vix: [mainDispatch.c:4230]: VMAutomation: Ignoring ReportPowerOpFinished because the VMX is shutting down.
2025-01-12T21:08:17.756Z In(05) vmx Transitioned vmx/execState/val to suspended
2025-01-12T21:08:17.756Z In(05) vmx VMX idle exit
2025-01-12T21:08:17.756Z In(05) vmx WQPoolFreePoll : pollIx = 3, signalHandle = 1092
2025-01-12T21:08:17.758Z In(05) vmx Vix: [mainDispatch.c:817]: VMAutomation_LateShutdown()
2025-01-12T21:08:17.758Z In(05) vmx Vix: [mainDispatch.c:772]: VMAutomationCloseListenerSocket. Closing listener socket.
2025-01-12T21:08:17.758Z In(05) vmx Flushing VMX VMDB connections
2025-01-12T21:08:17.758Z In(05) vmx VmdbDbRemoveCnx: Removing Cnx from Db for '/db/connection/#1/'
2025-01-12T21:08:17.758Z In(05) vmx VmdbCnxDisconnect: Disconnect: closed pipe for pub cnx '/db/connection/#1/' (0)
2025-01-12T21:08:17.758Z In(05) vmx VigorTransport_ServerDestroy: server destroyed.
2025-01-12T21:08:17.758Z In(05) vmx WQPoolFreePoll : pollIx = 2, signalHandle = 2976
2025-01-12T21:08:17.758Z In(05) vmx WQPoolFreePoll : pollIx = 1, signalHandle = 840
2025-01-12T21:08:17.763Z In(05) vmx VMX exit (0).
2025-01-12T21:08:17.763Z In(05) vmx OBJLIB-LIB: ObjLib cleanup done.
2025-01-12T21:08:17.763Z In(05) vmx AIOMGR-S : stat o=9 r=90 w=61 i=0 br=6144855 bw=6003505
