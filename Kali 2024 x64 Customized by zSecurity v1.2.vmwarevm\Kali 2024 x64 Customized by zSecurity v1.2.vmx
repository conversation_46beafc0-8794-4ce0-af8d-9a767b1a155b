#!/usr/bin/vmware
.encoding = "UTF-8"
config.version = "8"
virtualHW.version = "21"
pciBridge0.present = "TRUE"
pciBridge4.present = "TRUE"
pciBridge4.virtualDev = "pcieRootPort"
pciBridge4.functions = "8"
pciBridge5.present = "TRUE"
pciBridge5.virtualDev = "pcieRootPort"
pciBridge5.functions = "8"
pciBridge6.present = "TRUE"
pciBridge6.virtualDev = "pcieRootPort"
pciBridge6.functions = "8"
pciBridge7.present = "TRUE"
pciBridge7.virtualDev = "pcieRootPort"
pciBridge7.functions = "8"
vmci0.present = "TRUE"
hpet0.present = "TRUE"
nvram = "Kali 2024 x64 Customized by zSecurity v1.2.nvram"
virtualHW.productCompatibility = "hosted"
powerType.powerOff = "soft"
powerType.powerOn = "soft"
powerType.suspend = "soft"
powerType.reset = "soft"
displayName = "Kali 2024 x64 Customized by zSecurity v1.2"
usb.vbluetooth.startConnected = "TRUE"
guestOS = "debian12-64"
tools.syncTime = "TRUE"
tools.upgrade.policy = "upgradeAtPowerCycle"
sound.autoDetect = "TRUE"
sound.fileName = "-1"
sound.present = "TRUE"
memsize = "8192"
scsi0.virtualDev = "lsilogic"
scsi0.present = "TRUE"
scsi0:0.fileName = "disk-000001.vmdk"
ide1:0.deviceType = "cdrom-raw"
ide1:0.fileName = "auto detect"
ide1:0.present = "TRUE"
usb.present = "TRUE"
ehci.present = "TRUE"
ethernet0.connectionType = "nat"
ethernet0.addressType = "generated"
ethernet0.linkStatePropagation.enable = "TRUE"
ethernet0.present = "TRUE"
extendedConfigFile = "Kali 2024 x64 Customized by zSecurity v1.2.vmxf"
numvcpus = "2"
gui.perVMWindowAutofitMode = "resize"
gui.perVMFullscreenAutofitMode = "resize"
scsi0:0.present = "TRUE"
numa.autosize.cookie = "20012"
numa.autosize.vcpu.maxPerVirtualNode = "2"
uuid.bios = "56 4d 40 f8 4f 80 56 23-17 f1 7f d0 7c 71 5a 8c"
uuid.location = "56 4d 40 f8 4f 80 56 23-17 f1 7f d0 7c 71 5a 8c"
scsi0:0.redo = ""
pciBridge0.pciSlotNumber = "17"
pciBridge4.pciSlotNumber = "21"
pciBridge5.pciSlotNumber = "22"
pciBridge6.pciSlotNumber = "23"
pciBridge7.pciSlotNumber = "24"
scsi0.pciSlotNumber = "16"
usb.pciSlotNumber = "32"
ethernet0.pciSlotNumber = "33"
sound.pciSlotNumber = "34"
ehci.pciSlotNumber = "35"
vmci0.pciSlotNumber = "36"
svga.vramSize = "268435456"
vmotion.checkpointFBSize = "4194304"
vmotion.checkpointSVGAPrimarySize = "268435456"
vmotion.svga.mobMaxSize = "268435456"
vmotion.svga.graphicsMemoryKB = "262144"
ethernet0.generatedAddress = "00:0c:29:71:5a:8c"
ethernet0.generatedAddressOffset = "0"
vmci0.id = "-586922912"
monitor.phys_bits_used = "45"
cleanShutdown = "TRUE"
softPowerOff = "FALSE"
usb:1.speed = "2"
usb:1.present = "TRUE"
usb:1.deviceType = "hub"
usb:1.port = "1"
usb:1.parent = "-1"
svga.guestBackedPrimaryAware = "TRUE"
guestOS.detailed.data = "architecture='X86' bitness='64' distroAddlVersion='2024.3' distroName='Kali GNU/Linux' distroVersion='2024.3' familyName='Linux' kernelVersion='6.8.11-amd64' prettyName='Kali GNU/Linux Rolling'"
toolsInstallManager.updateCounter = "1"
isolation.tools.hgfs.disable = "TRUE"
hgfs.mapRootShare = "TRUE"
hgfs.linkRootShare = "TRUE"
sharedFolder0.present = "TRUE"
sharedFolder0.enabled = "TRUE"
sharedFolder0.readAccess = "TRUE"
sharedFolder0.writeAccess = "TRUE"
sharedFolder0.hostPath = "/Volumes/Data/Shared"
sharedFolder0.guestName = "Shared"
sharedFolder0.expiration = "never"
sharedFolder.maxNum = "1"
ide1:0.startConnected = "FALSE"
ide1:0.autodetect = "TRUE"
floppy0.present = "FALSE"
vmxstats.filename = "Kali 2024 x64 Customized by zSecurity v1.2.scoreboard"
tools.capability.verifiedSamlToken = "TRUE"
guestInfo.detailed.data = "architecture='X86' bitness='64' distroAddlVersion='2024.3' distroName='Kali GNU/Linux' distroVersion='2024.3' familyName='Linux' kernelVersion='6.8.11-amd64' prettyName='Kali GNU/Linux Rolling'"
cpuid.coresPerSocket.cookie = "2"
usb.generic.allowHID = "TRUE"
usb_xhci.present = "TRUE"
gui.lastPoweredViewMode = "fullscreen"
usb_xhci.pciSlotNumber = "160"
checkpoint.vmState = "Kali 2024 x64 Customized by zSecurity v1.2-f65b28b0.vmss"
gui.stretchGuestMode = "fullfill"
tools.remindInstall = "FALSE"
usb_xhci:4.present = "TRUE"
usb_xhci:4.deviceType = "hid"
usb_xhci:4.port = "4"
usb_xhci:4.parent = "-1"
usb_xhci:6.speed = "2"
usb_xhci:6.present = "TRUE"
usb_xhci:6.deviceType = "hub"
usb_xhci:6.port = "6"
usb_xhci:6.parent = "-1"
usb_xhci:7.speed = "4"
usb_xhci:7.present = "TRUE"
usb_xhci:7.deviceType = "hub"
usb_xhci:7.port = "7"
usb_xhci:7.parent = "-1"
