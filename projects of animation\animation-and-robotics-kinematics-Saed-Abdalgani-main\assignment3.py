#%%
import vedo as vd
vd.settings.default_backend= 'vtk'
import numpy as np

#%% class for a robot arm
def Rot(angle, axis):
    # calculate the rotation matrix for a given angle and axis using <PERSON><PERSON><PERSON>' formula
    # return a 3x3 numpy array
    # also see scipy.spatial.transform.Rotation.from_rotvec
    axis = np.array(axis)
    axis = axis/np.linalg.norm(axis)
    I = np.eye(3)
    K = np.array([[0, -axis[2], axis[1]],
                    [axis[2], 0, -axis[0]],
                    [-axis[1], axis[0], 0]])
    R = I + np.sin(angle)*K + (1-np.cos(angle))*np.dot(K,K)
    return R
    
class SimpleArm:
    def __init__(self, n=3, link_lengths=1, joint_limits=None, joint_types=None):
        self.n = n  # number of links
        self.joint_values = [0] * self.n  # joint values (angles for revolute, displacements for prismatic)
        self.link_lengths = link_lengths

        # Define joint types: 'revolute' or 'prismatic'
        if joint_types is None:
            self.joint_types = ['revolute'] * n  # Default to all revolute joints
        else:
            # Make sure we have the right number of joint types
            if len(joint_types) != n:
                raise ValueError(f"Expected {n} joint types, got {len(joint_types)}")
            # Validate joint types
            for jt in joint_types:
                if jt not in ['revolute', 'prismatic']:
                    raise ValueError(f"Invalid joint type: {jt}. Must be 'revolute' or 'prismatic'")
            self.joint_types = joint_types

    def FK(self, angles=None): 
        # calculate the forward kinematics of the arm

        # angles is a list of joint angles. If angles is None, the current joint angles are used
        if angles is not None:
            self.angles = angles
        
        # Initial rotation matrix
        Ri = np.eye(3)

        # First joint
        Ri_1 = Rot(self.angles[0], [0, 0, 1]) @ Ri  # Compute the rotation matrix for the first joint
        self.Jw[1, :] = Ri_1 @ self.Jl[1, :] + self.Jw[0, :]  # Update the position of the first joint in world coordinates

        # Second joint
        Ri_2 = Rot(self.angles[1], [0, 0, 1]) @ Ri_1
        self.Jw[2, :] = Ri_2 @ self.Jl[2, :] + self.Jw[1, :]

        # Third joint
        Ri_3 = Rot(self.angles[2], [0, 0, 1]) @ Ri_2
        self.Jw[3, :] = Ri_3 @ self.Jl[3, :] + self.Jw[2, :]


        return self.Jw[-1,:] # return the position of the end effector
        
    def IK(self, target):
        # calculate the inverse kinematics of the arm
        # target is the position of the end effector in world coordinates
        # return a list of joint angles
        pass
    
    def VelocityJacobian(self, angles=None):
        # calculate the velocity jacobian of the arm
        # return a 3x3 numpy array
        pass

    def draw(self):
        vd_arm = vd.Assembly()
        vd_arm += vd.Sphere(pos = self.Jw[0,:], r=0.05)
        for i in range(1,self.n+1):
            vd_arm += vd.Cylinder(pos = [self.Jw[i-1,:], self.Jw[i,:]], r=0.02)
            vd_arm += vd.Sphere(pos = self.Jw[i,:], r=0.05)
        return vd_arm
        

#%%
activeJoint = 0
IK_target = [1,1,0]
def OnSliderAngle(widget, event):
    global activeJoint
    arm.angles[activeJoint] = widget.value
    arm.FK()
    plt.remove("Assembly")
    plt.add(arm.draw())
    plt.render()

def OnCurrentJoint(widget, event):
    global activeJoint
    activeJoint = round(widget.value)
    sliderAngle.value = arm.angles[activeJoint]


def LeftButtonPress(evt):
    global IK_target
    IK_target = evt.picked3d
    plt.remove("Sphere")
    plt.add(vd.Sphere(pos = IK_target, r=0.05, c='b'))
    plt.render()


arm = SimpleArm(3)
plt = vd.Plotter()
plt += arm.draw()
plt += vd.Sphere(pos = IK_target, r=0.05, c='b').draggable(True)
plt += vd.Plane(s=[2.1*arm.n,2.1*arm.n]) # a plane to catch mouse events
sliderCurrentJoint = plt.add_slider(OnCurrentJoint, 0, arm.n-1, 0, title="Current joint", pos=3, delayed=True)
sliderAngle =  plt.add_slider(OnSliderAngle,-np.pi,np.pi,0., title="Joint Angle", pos=4)
plt.add_callback('LeftButtonPress', LeftButtonPress) # add Mouse callback
plt.user_mode('2d').show(zoom="tightest")

plt.close()

# %%
