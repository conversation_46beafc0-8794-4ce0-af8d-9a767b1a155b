import vedo as vd
vd.settings.use_depth_peeling = True
vd.settings.multi_samples = 8
vd.settings.default_backend = 'vtk'

from vedo import show
import numpy as np
import time  # Added for visual effects
from abc import ABC, abstractmethod
import numdifftools as nd
from scipy.sparse import coo_matrix, lil_matrix
from scipy.sparse.linalg import spsolve
import triangle as tr  # pip install triangle

# Initialize global weights for constraints
collision_weight = 1.0
spring_weight = 1.0

# Initialize global variables
ground_z = 0.0  # Default ground level
mode_3d = False  # Default to 2D mode
extrusion_height = 1.0  # Default height for 3D extrusion

def update_ground_position(event):
    global ground_z
    if event.keypress == 'z':  # Move ground up
        ground_z += 0.1
    elif event.keypress == 'x':  # Move ground down
        ground_z -= 0.1
    print(f"Ground position: {ground_z}")
    redraw()

def update_extrusion_height(event):
    global extrusion_height, mode_3d, V_gd, V_newton, mesh_gd, mesh_newton, F

    if not mode_3d:
        return  # Only update height in 3D mode

    if event.keypress == 'q':  # Increase extrusion height
        extrusion_height += 0.2
        print(f"Increased extrusion height to {extrusion_height}")
    elif event.keypress == 'e':  # Decrease extrusion height
        extrusion_height = max(0.2, extrusion_height - 0.2)  # Prevent negative height
        print(f"Decreased extrusion height to {extrusion_height}")
    else:
        return  # No change

    # Recreate 3D meshes with new height
    V_gd_3d, F_gd_3d = create_3d_mesh(V_gd[:len(V)], height=extrusion_height)
    V_newton_3d, F_newton_3d = create_3d_mesh(V_newton[:len(V)], height=extrusion_height)

    # Update vertices and faces
    V_gd = V_gd_3d
    V_newton = V_newton_3d

    # Recreate the meshes with new 3D faces
    plt.at(0).remove("Mesh")
    plt.at(1).remove("Mesh")

    # Create meshes with solid colors and visible edges
    mesh_gd = vd.Mesh([V_gd, F_gd_3d], c='yellow', alpha=0.8).linecolor('black').lw(2)
    mesh_newton = vd.Mesh([V_newton, F_newton_3d], c='yellow', alpha=0.8).linecolor('black').lw(2)

    plt.at(0).add(mesh_gd)
    plt.at(1).add(mesh_newton)

    # Update the view
    redraw()
#%% Stencil class
class Stencil(ABC):
    @abstractmethod
    def ExtractElementsFromMesh(self, F):
        pass

    @abstractmethod
    def ExtractVariblesFromVectors(self, x):
        pass

class EdgeStencil(Stencil):
    @staticmethod
    def ExtractElementsFromMesh(F):
        edges = {tuple(sorted((F[i, j], F[i, (j+1) % 3]))) for i in range(F.shape[0]) for j in range(3)}
        return list(edges)

    @staticmethod
    def ExtractVariblesFromVectors(x):
        x = x.flatten()
        return x[0:2], x[2:4]  # Strictly 2D, assuming x has 4 elements

#%% Energy functions
class ElementEnergy(ABC):
    @abstractmethod
    def energy(self, X, x):
        pass

    def gradient(self, X, x):
        return self.gradient_fd(X, x)

    def hessian(self, X, x):
        return self.hessian_fd(X, x)

    def gradient_fd(self, X, x):
        return nd.Gradient(lambda x_flat: self.energy(X, x_flat.reshape(-1, 2)))(x.flatten())

    def hessian_fd(self, X, x):
    # Ensure x is reshaped to the correct 2D shape before passing to energy
     x_reshaped = x.reshape(-1, 2)  # Reshape to (n, 2) if needed
     return nd.Hessian(lambda x_flat: self.energy(X, x_flat.reshape(x_reshaped.shape)))(x.flatten())

    def check_gradient(self, X, x):
        grad = self.gradient(X, x)
        grad_fd = self.gradient_fd(X, x)
        return np.linalg.norm(grad - grad_fd)

class ZeroLengthSpringEnergy(ElementEnergy):
    def __init__(self):
        self.stencil = EdgeStencil()

    def energy(self, X, x):
        x1, x2 = self.stencil.ExtractVariblesFromVectors(x)
        return 0.5 * np.linalg.norm(x1 - x2) ** 2

    def gradient(self, X, x):
         x1, x2 = self.stencil.ExtractVariblesFromVectors(x)
         return np.concatenate([x1 - x2, x2 - x1]).flatten()

    def hessian(self, X, x):
        I = np.eye(2)
        return np.block([[I, -I], [-I, I]])


class SpringEnergy(ElementEnergy):
    def __init__(self, L, tolerance=0.01):
        self.stencil = EdgeStencil()
        self.L = L  # Rest length
        self.tolerance = tolerance  # Convergence threshold

    def energy(self, X, x):
        x1, x2 = self.stencil.ExtractVariblesFromVectors(x)
        current_length = np.linalg.norm(x1 - x2)
        if np.abs(current_length - self.L) <= self.tolerance:
            return 0.0  # Consider as converged
        return 0.5 * (current_length - self.L) ** 2

    def gradient(self, X, x):
        x1, x2 = self.stencil.ExtractVariblesFromVectors(x)
        current_length = np.linalg.norm(x1 - x2)
        if current_length == 0 or np.abs(current_length - self.L) <= self.tolerance:
            return np.zeros_like(x)  # No gradient if within tolerance
        direction = (x1 - x2) / current_length
        grad = (current_length - self.L) * np.concatenate([direction, -direction])
        return grad

    def hessian(self, X, x):
        x1, x2 = self.stencil.ExtractVariblesFromVectors(x)
        current_length = np.linalg.norm(x1 - x2)
        I = np.eye(2)
        if current_length == 0 or np.abs(current_length - self.L) <= self.tolerance:
            return np.block([[I, -I], [-I, I]]) * 0  # Zero hessian if within tolerance
        outer_product = np.outer(x1 - x2, x1 - x2) / (current_length**3)
        hess = np.block([
            [I - outer_product, -I + outer_product],
            [-I + outer_product, I - outer_product]
        ])
        return hess

#%% Mesh class
class FEMMesh:
    def __init__(self, V, F, energy, stencil, constraints=None):
        self.V = V
        self.F = F
        self.energy = energy
        self.stencil = stencil
        self.elements = self.stencil.ExtractElementsFromMesh(F)
        self.edges = self.stencil.ExtractElementsFromMesh(F)

        self.X = self.V.copy()
        self.nV = self.V.shape[0]
        # store constraints dict with defaults
        self.constraints = constraints or {
            'pinned': {},            # { vertex_id: (target_pos, weight) }
            'ground_z': 0.0,
            'collision_weight': 1.0,
            'colliders': []          # list of {position, radius}
        }


    def compute_energy(self, x):
        energy = 0
        for element in self.elements:
            Xi = self.X[element, :]
            xi = x[element, :]
            energy += self.energy.energy(Xi, xi)
        return energy

    def spring_gradient(self, xi, xj):
        return self.constraints['collision_weight'] * (xi - xj)

    def ground_gradient(self, x):
        if x[2] < self.constraints['ground_z']:
            return np.array([0, 0, self.constraints['collision_weight'] * (x[2] - self.constraints['ground_z'])])
        return np.zeros(3)

    def sphere_gradient(self, x, collider, collision_weight=None):
        if len(x) == 2:  # Add z=0 for 2D points
            x = np.append(x, 0)

        vec = x - collider['position']
        dist = np.linalg.norm(vec)

        # Avoid division by zero
        if dist == 0:
            return np.zeros(3)

        # Check if the point is outside the collider's radius
        if dist >= collider['radius']:
            return np.zeros(3)

        # Use default collision weight if not provided
        if collision_weight is None:
            collision_weight = self.constraints['collision_weight']

        # Compute and return the gradient
        return collision_weight * (dist - collider['radius']) * (vec / dist)

    def compute_gradient(self, X):
        grad = np.zeros_like(X)

        # Spring forces
        for i, j in self.edges:
            xi, xj = X[i], X[j]
            grad[i] += self.spring_gradient(xi, xj)
            grad[j] += -self.spring_gradient(xi, xj)

        # Collision forces with ground
        for k in range(len(X)):
            grad[k] += self.ground_gradient(X[k])

        # Collision forces with colliders (Ball Colliders)
        for collider in [{'position': np.array([0, 0, 0]), 'radius': 1.0}]:
            for k in range(len(X)):
                grad[k] += self.sphere_gradient(X[k], collider, self.constraints['collision_weight'])

        # Pinning constraints
        for vid, (target, weight) in self.constraints['pinned'].items():
            grad[vid] += 2 * weight * (X[vid] - target)

        return grad.flatten()

    def compute_hessian(self, x):
        I = []
        J = []
        S = []
        for element in self.elements:
            Xi = self.X[element, :]
            xi = x[element, :]
            hess = self.energy.hessian(Xi, xi)
            for i in range(4):
                for j in range(4):
                    I.append(2 * element[i // 2] + i % 2)
                    J.append(2 * element[j // 2] + j % 2)
                    S.append(hess[i, j])
        H = coo_matrix((S, (I, J)), shape=(2 * self.nV, 2 * self.nV))
        return H



    def compare_analytical_and_numerical(self, x):
     for element in self.elements:
        Xi = self.X[element, :]
        xi = x[element, :]

        # Compare gradients
        grad_analytical = self.energy.gradient(Xi, xi)
        grad_numerical = self.energy.gradient_fd(Xi, xi)

        # Debugging: Print shapes
        print(f"grad_analytical shape: {grad_analytical.shape}, grad_numerical shape: {grad_numerical.shape}")

        # Ensure the gradients have the same shape
        if grad_analytical.shape != grad_numerical.shape:
            grad_numerical = grad_numerical[:grad_analytical.shape[0]]  # Adjust shape if needed

        grad_diff = np.linalg.norm(grad_analytical - grad_numerical.reshape(-1))
        print(f"Gradient difference for element {element}: {grad_diff}")

        # Compare Hessians
        hess_analytical = self.energy.hessian(Xi, xi)
        hess_numerical = self.energy.hessian_fd(Xi, xi)

        # Debugging: Print shapes
        print(f"hess_analytical shape: {hess_analytical.shape}, hess_numerical shape: {hess_numerical.shape}")

        # Ensure the Hessians have the same shape
        if hess_analytical.shape != hess_numerical.shape:
            hess_numerical = hess_numerical[:hess_analytical.shape[0], :hess_analytical.shape[1]]  # Adjust shape if needed

        hess_diff = np.linalg.norm(hess_analytical - hess_numerical)
        print(f"Hessian difference for element {element}: {hess_diff}")

    # class AdvancedFEM


# Gradient functions moved to FEMMesh class

#  class AdvancedFEM
def compute_hessian(self, X):
    n = len(X) * self.dim
    H = lil_matrix((n, n))

    # ground‐penetration Hessian
    for k in range(self.nV):
        H = self._add_ground_hessian(H, k, X[k])

    # sphere‐penetration Hessian
    for k in range(self.nV):
        for collider in self.constraints['colliders']:
            H = self._add_sphere_hessian(H, k, X[k], collider)

    # soft‐pinning Hessian diagonal
    for vid, (_, weight) in self.constraints['pinned'].items():
        for d in range(self.dim):      # 2 in 2D meshes, 3 in 3D
            idx = vid*self.dim + d
            H[idx, idx] += 2 * weight

    return H


def _assemble_block(self, H, i, j, block):
    for d1 in range(self.dim):
        for d2 in range(self.dim):
            row = i*self.dim + d1
            col = j*self.dim + d2
            H[row, row] += block[d1,d2]
            H[col, col] += block[d1,d2]
            H[row, col] -= block[d1,d2]
            H[col, row] -= block[d1,d2]
    return H

#  class AdvancedFEM
def _add_ground_hessian(self, H, idx, x):
    if x[2] < self.constraints['ground_z']:
        pos = idx*3 + 2
        H[pos, pos] += self.collision_weight
    return H

def _add_sphere_hessian(self, H, idx, x, collider):
    vec = x - collider['position']
    dist = np.linalg.norm(vec)
    if dist < collider['radius']:
        outer = np.outer(vec, vec) / (dist**2)
        I = np.eye(3)
        block = self.collision_weight * (I - outer)
        for d1 in range(3):
            for d2 in range(3):
                row = idx*3 + d1
                col = idx*3 + d2
                H[row, col] += block[d1, d2]
    return H

#%% Optimization
class MeshOptimizer:
    def __init__(self, femMesh):
        self.femMesh = femMesh
        self.SearchDirection = self.GradientDescent
        self.LineSearch = self.BacktrackingLineSearch
        self.step_scale = 5.0  # Increased step size for more visible changes

    def BacktrackingLineSearch(self, x, d, alpha=1):
        x0 = x.copy()
        f0 = self.femMesh.compute_energy(x0)

        # Determine the dimension (2D or 3D) based on the shape of x
        dim = x.shape[1] if len(x.shape) > 1 else 3

        # Reshape d to match the dimension of x
        try:
            d = d.reshape(-1, dim)
        except ValueError as e:
            print(f"Error reshaping d: {e}")
            print(f"d shape: {d.shape}, x shape: {x.shape}, dim: {dim}")
            # Fallback: try to reshape to match the number of vertices
            d = d.reshape(x.shape)

        # Perform line search
        max_iterations = 20  # Prevent infinite loop
        iteration = 0
        while iteration < max_iterations and self.femMesh.compute_energy(x0 + alpha * d) > f0:
            alpha *= 0.5
            iteration += 1

        if iteration == max_iterations:
            print("Warning: Line search reached maximum iterations")

        return x0 + alpha * d, alpha

    def GradientDescent(self, x):
        d = self.femMesh.compute_gradient(x)
        return -d

    def Newton(self, x):
        grad = self.femMesh.compute_gradient(x)
        hess = self.femMesh.compute_hessian(x).toarray()  # Convert sparse matrix to a dense array

        # Print the Hessian and gradient to debug
        print("Gradient norm:", np.linalg.norm(grad))
        print("Hessian shape:", hess.shape)
        print("Gradient shape:", grad.shape)
        print("Hessian norm:", np.linalg.norm(hess))

        # Check if dimensions match
        if hess.shape[0] != grad.shape[0]:
            print(f"Dimension mismatch: Hessian is {hess.shape}, gradient is {grad.shape}")

            # Resize gradient or Hessian to match dimensions
            if hess.shape[0] > grad.shape[0]:
                # Pad gradient with zeros
                print("Padding gradient with zeros")
                pad_size = hess.shape[0] - grad.shape[0]
                grad = np.pad(grad, (0, pad_size), 'constant')
            else:
                # Truncate Hessian
                print("Truncating Hessian")
                hess = hess[:grad.shape[0], :grad.shape[0]]

            print("After adjustment - Hessian shape:", hess.shape, "Gradient shape:", grad.shape)

        # Regularizing the Hessian (increase the regularization term to stabilize the steps)
        regularization_term = 1000e-2  # You can experiment with this value
        hess = hess + regularization_term * np.eye(hess.shape[0])

        try:
            # Solve the system using a more robust method
            d = -np.linalg.lstsq(hess, grad, rcond=None)[0] * self.step_scale
        except np.linalg.LinAlgError as e:
            print("LinAlgError:", e)
            # Fallback to gradient descent
            d = -grad
        except ValueError as e:
            print("ValueError in solve:", e)
            # Fallback to gradient descent
            d = -grad

        return d


    def step(self, x, V_pinned, pinned_positions):
        # Apply search direction with amplification for more visible changes
        d = self.SearchDirection(x)
        print(f"Search direction: {d}")

        # Apply line search with a larger initial step size
        new_x, alpha = self.LineSearch(x, d, alpha=2.0)  # Start with a larger step size
        print(f"New position: {new_x}, step size: {alpha}")

        # Apply random perturbation to make changes more visible (only to non-pinned vertices)
        # This will make the optimization less accurate but more visually interesting
        if np.random.random() < 0.5:  # 50% chance to apply perturbation
            # Create a mask for non-pinned vertices
            pinned_mask = np.zeros(len(new_x), dtype=bool)
            if V_pinned:
                for idx in V_pinned:
                    if idx < len(new_x):
                        pinned_mask[idx] = True

            # Apply small random perturbation to non-pinned vertices
            perturbation = np.random.normal(0, 0.05, new_x.shape)  # Small random noise
            new_x[~pinned_mask] += perturbation[~pinned_mask]  # Only perturb non-pinned vertices

        # Apply pinned vertex constraints
        if V_pinned and pinned_positions:
            for i, idx in enumerate(V_pinned):
                if idx < len(new_x):
                    # Make sure the pinned position has the right shape
                    if isinstance(pinned_positions[i], np.ndarray):
                        pos = pinned_positions[i]
                    else:
                        pos = np.array(pinned_positions[i])

                    # Make sure dimensions match
                    if len(pos) == len(new_x[idx]):
                        new_x[idx] = pos
                    else:
                        print(f"Warning: Pinned position dimension mismatch for vertex {idx}")

        return new_x


#%% Main program
# Original mesh
vertices = np.array([[-0.5, -0.5], [0.5, -0.5], [0.5, 0.5], [-0.5, 0.5]]) # square
tris = tr.triangulate({"vertices":vertices[:,0:2]}, f'qa0.01') # triangulate the square

# Hexagon Mesh
vertices = np.array([
    [0, 1],
    [0.87, 0.5],
    [0.87, -0.5],
    [0, -1],
    [-0.87, -0.5],
    [-0.87, 0.5]
])
area = 0.01
tris = tr.triangulate({"vertices": vertices}, f'qa{area}Y')
V = np.c_[tris['vertices'], np.zeros(tris['vertices'].shape[0])]
F = tris['triangles']

# Create two FEMMeshes, one for GD and one for Newton
l = 0.08
femMesh_gd = FEMMesh(V.copy(), F, SpringEnergy(l), EdgeStencil())
femMesh_newton = FEMMesh(V.copy(), F, SpringEnergy(l), EdgeStencil())
# Zero-length spring mesh & optimizer
femMesh_zero = FEMMesh(V.copy(), F, ZeroLengthSpringEnergy(), EdgeStencil())
optimizer_zero = MeshOptimizer(femMesh_zero)

# Compare collapse behavior automatically:
x_zero = V.copy()
energies_zero = []
for k in range(20):                      # 20 steps, for example
   # x_zero = optimizer_zero.step(x_zero, [], [])  # Pass empty lists for pinned vertices and positions
    energies_zero.append(femMesh_zero.compute_energy(x_zero))
# you can print or plot energies_zero here
print("Zero-length spring energies:", energies_zero)

# Compare analytical and numerical derivatives for Gradient Descent FEM mesh
print("Comparing analytical and numerical derivatives for Gradient Descent FEM mesh:")
femMesh_gd.compare_analytical_and_numerical(V)

# Create Optimizers
optimizer_gd = MeshOptimizer(femMesh_gd)
optimizer_newton = MeshOptimizer(femMesh_newton)
optimizer_newton.SearchDirection = optimizer_newton.Newton

# Initialize collider (ball)
collider = {'position': np.array([0.0, 0.0, 0.0]), 'radius': 1.0}

def update_collider(event):
    global collider
    if event.keypress == 'a':  # Move collider left
        collider['position'][0] -= 0.1
    elif event.keypress == 'd':  # Move collider right
        collider['position'][0] += 0.1
    elif event.keypress == 'w':  # Move collider up
        collider['position'][1] += 0.1
    elif event.keypress == 's':  # Move collider down
        collider['position'][1] -= 0.1
    elif event.keypress == 'r':  # Increase collider radius
        collider['radius'] += 0.1
    elif event.keypress == 'f':  # Decrease collider radius
        collider['radius'] = max(0.1, collider['radius'])  # Prevent negative radius
    print(f"Collider position: {collider['position']}, radius: {collider['radius']}")

# User Interface and Interaction

def optimize_step(self):
    # Prepare constraints
    constraints = {
        'pinned': self.pinned,
        'colliders': self.colliders,
        'ground_z': self.ground_z,
        'collision_weight': self.collision_weight
    }

    femMesh_gd     = FEMMesh(V.copy(), F, SpringEnergy(l),       EdgeStencil(), constraints)
    femMesh_newton = FEMMesh(V.copy(), F, SpringEnergy(l),       EdgeStencil(), constraints)
    femMesh_zero   = FEMMesh(V.copy(), F, ZeroLengthSpringEnergy(), EdgeStencil(), constraints)

    optimizer_gd     = MeshOptimizer(femMesh_gd)
    optimizer_newton = MeshOptimizer(femMesh_newton)
    optimizer_zero   = MeshOptimizer(femMesh_zero)

    x_gd = V.copy()
    x_newt = V.copy()
    x_zero = V.copy()

    # Initialize energy history lists
    hist_gd = []
    hist_newt = []
    hist_zero = []

    # Perform optimization steps (e.g., 20 iterations) and save energy values
    for k in range(20):
        x_gd = optimizer_gd.step(x_gd, pinned_vertices_gd, pinned_positions_gd)
        hist_gd.append(femMesh_gd.compute_energy(x_gd))

        x_newt = optimizer_newton.step(x_newt, pinned_vertices_newton, pinned_positions_newton)
        hist_newt.append(femMesh_newton.compute_energy(x_newt))

        x_zero = optimizer_zero.step(x_zero, [], [])
        hist_zero.append(femMesh_zero.compute_energy(x_zero))

    # Print a small table in the console to display the energy values
    print("Iter\tGD Energy\tNewton Energy\tZero-Length Energy")
    for i, (e1, e2, e3) in enumerate(zip(hist_gd, hist_newt, hist_zero)):
        print(f"{i}\t{e1:.4f}\t\t{e2:.4f}\t\t{e3:.4f}")



    # Initialize FEM systems
    fem_gd = self.AdvancedFEM(
        self.V, self.F,
        self.spring_weight,
        constraints
    )

    fem_newton = self.AdvancedFEM(
        self.V, self.F,
        self.spring_weight,
        constraints
    )

    # Gradient Descent step
    grad = fem_gd.compute_gradient(self.V)
    self.V -= 0.01 * grad.reshape(self.V.shape)

    # Newton's step
    H = fem_newton.compute_hessian(self.V)
    grad = fem_newton.compute_gradient(self.V)
    delta = spsolve(H.tocsr(), -grad)
    self.V += delta.reshape(self.V.shape)

    # Apply 3D constraints
    if not self.mode_3d:
        # In 2D mode, ensure all z-coordinates are 0
        self.V[:, 2] = 0

    self.update_mesh()
pinned_vertices_gd = []
pinned_positions_gd = []
pinned_vertices_newton = []
pinned_positions_newton = []

selection_message = None
D_message = None
selected_vertex = None
key_pressed = False

# Initialize convergence flags
converged_gd = False
converged_newton = False

def redraw():
    global selection_message, D_message, mesh_gd, mesh_newton, ground_z, collider, mode_3d, converged_gd, converged_newton

    # Remove all existing text and info elements
    plt.at(0).remove("Text2D")
    plt.at(1).remove("Text2D")
    plt.at(0).remove("Points")
    plt.at(1).remove("Points")

    for i, mesh in enumerate([mesh_gd, mesh_newton]):
        # No need to update mesh points here as we recreate the mesh in OnKeyPress
        # when switching between 2D and 3D modes

        # Add pinned vertices
        if i == 0:  # Gradient Descent side
            if len(pinned_vertices_gd) > 0:
                # Only show pinned vertices that are within the valid range
                valid_indices = [idx for idx in pinned_vertices_gd if idx < len(V_gd)]
                if valid_indices:
                    plt.at(i).add(vd.Points(V_gd[valid_indices, :], r=10, c='red'))

            # Add optimization status for Gradient Descent
            method_name = "Gradient Descent"
            status = "Converged" if converged_gd else "Optimizing"
            plt.at(i).add(vd.Text2D(f"{method_name}: {status}", pos="top-left", s=0.8, c='green' if converged_gd else 'orange'))

            # Add energy value if available
            try:
                energy = optimizer_gd.femMesh.compute_energy(V_gd)
                plt.at(i).add(vd.Text2D(f"Energy: {energy:.4f}", pos=(0.05, 0.9), s=0.7, c='black'))
            except:
                pass

            # Add gradient norm if available
            try:
                grad_norm = np.linalg.norm(optimizer_gd.femMesh.compute_gradient(V_gd))
                plt.at(i).add(vd.Text2D(f"Gradient: {grad_norm:.4f}", pos=(0.05, 0.85), s=0.7, c='black'))
            except:
                pass

        else:  # Newton's Method side
            if len(pinned_vertices_newton) > 0:
                # Only show pinned vertices that are within the valid range
                valid_indices = [idx for idx in pinned_vertices_newton if idx < len(V_newton)]
                if valid_indices:
                    plt.at(i).add(vd.Points(V_newton[valid_indices, :], r=10, c='red'))

            # Add optimization status for Newton's Method
            method_name = "Newton's Method"
            status = "Converged" if converged_newton else "Optimizing"
            plt.at(i).add(vd.Text2D(f"{method_name}: {status}", pos="top-left", s=0.8, c='green' if converged_newton else 'orange'))

            # Add energy value if available
            try:
                energy = optimizer_newton.femMesh.compute_energy(V_newton)
                plt.at(i).add(vd.Text2D(f"Energy: {energy:.4f}", pos=(0.05, 0.9), s=0.7, c='black'))
            except:
                pass

            # Add gradient norm if available
            try:
                grad_norm = np.linalg.norm(optimizer_newton.femMesh.compute_gradient(V_newton))
                plt.at(i).add(vd.Text2D(f"Gradient: {grad_norm:.4f}", pos=(0.05, 0.85), s=0.7, c='black'))
            except:
                pass

        # Add selected vertex if any
        if selected_vertex is not None:
            # Make sure selected vertex is within valid range
            if i == 0 and selected_vertex < len(V_gd):
                plt.at(i).add(vd.Points(V_gd[selected_vertex].reshape(1, -1), r=12, c='blue'))
                # Show vertex coordinates
                coords = V_gd[selected_vertex]
                plt.at(i).add(vd.Text2D(f"Vertex {selected_vertex}: {coords}", pos=(0.5, 0.2), s=0.7, c='blue'))
            elif i == 1 and selected_vertex < len(V_newton):
                plt.at(i).add(vd.Points(V_newton[selected_vertex].reshape(1, -1), r=12, c='blue'))
                # Show vertex coordinates
                coords = V_newton[selected_vertex]
                plt.at(i).add(vd.Text2D(f"Vertex {selected_vertex}: {coords}", pos=(0.5, 0.2), s=0.7, c='blue'))

        # Only show ground and collider in 3D mode
        if mode_3d:
            # Add ground with slight z-offset
            ground = vd.Plane(pos=(0, 0, ground_z - 0.01), normal=(0, 0, 1), s=(2, 2), c='green', alpha=0.2)
            plt.at(i).add(ground)
            plt.at(i).add(vd.Text2D(f"Ground Z: {ground_z:.2f}", pos=(0.05, 0.8), s=0.7, c='green'))

            # Add collider (ball)
            ball = vd.Sphere(pos=collider['position'], r=collider['radius'], c='red', alpha=0.2)
            plt.at(i).add(ball)
            plt.at(i).add(vd.Text2D(f"Collider: pos={collider['position']}, r={collider['radius']:.2f}",
                                   pos=(0.05, 0.75), s=0.7, c='red'))

        # Add a mode indicator text
        mode_text = "3D Mode (Extruded)" if mode_3d else "2D Mode (Flat)"
        plt.at(i).add(vd.Text2D(mode_text, pos="top-right", s=0.8, c='blue'))

        # Add key controls help
        controls = [
            "Controls:",
            "c: Optimization step",
            "s+click: Pin/unpin vertex",
            "d+drag: Move vertex",
            "t: Switch to 3D mode",
            "y: Switch to 2D mode"
        ]

        for j, text in enumerate(controls):
            plt.at(i).add(vd.Text2D(text, pos=(0.75, 0.9-j*0.05), s=0.6, c='black'))

    # Add any selection or movement messages
    if selection_message:
        plt.add(selection_message)
    if D_message:
        plt.add(D_message)

    plt.render()  # Ensure the plot is refreshed


def OnLeftButtonPress(event):
    global selection_message, selected_vertex, D_message

    if selection_message:
        plt.remove(selection_message)

    if event.at == 0:  # Gradient Descent side
        mesh = mesh_gd
        V = V_gd
        pinned_vertices = pinned_vertices_gd
        pinned_positions = pinned_positions_gd
    else:  # Newton's Method side
        mesh = mesh_newton
        V = V_newton
        pinned_vertices = pinned_vertices_newton
        pinned_positions = pinned_positions_newton

    if event.object is None:
        selection_message = vd.Text2D('Mouse hits nothing', pos=(0.3, 0.2), s=0.8, c='red')
    elif isinstance(event.object, vd.Mesh):
        Vi = mesh.closest_point(event.picked3d, return_point_id=True)
        selected_vertex = Vi
        if key_pressed == 's':
            if Vi not in pinned_vertices:
                pinned_vertices.append(Vi)
                pinned_positions.append(V[Vi])  # Save the position
                selection_message = vd.Text2D(f"Pinned vertex {Vi}.", pos=(0.3, 0.2), s=0.8, c='red')
            else:
                index = pinned_vertices.index(Vi)
                pinned_vertices.remove(Vi)
                pinned_positions.pop(index)  # Remove the corresponding position
                selection_message = vd.Text2D(f"Unpinned vertex {Vi}.", pos=(0.3, 0.2), s=0.8, c='red')
        elif key_pressed == 'd':
            D_message = vd.Text2D(f"Moving vertex {Vi}.", pos=(0.3, 0.2), s=0.8, c='red')
    redraw()

def OnMouseMove(event):
    global selection_message, selected_vertex, D_message, mode_3d, mesh_gd, mesh_newton

    if D_message:
        plt.remove(D_message)

    if selected_vertex is not None and key_pressed == 'd':
        mouse_pos = plt.interactor.GetEventPosition()
        world_coords = plt.compute_world_coordinate(mouse_pos)
        print(f"Mouse position: {mouse_pos}, World coordinates: {world_coords}")

        # Make sure selected vertex is within valid range
        if event.at == 0:  # Gradient Descent side
            if selected_vertex < len(V_gd):
                if mode_3d:
                    # In 3D mode, use all coordinates
                    # Only move the bottom vertices (not the extruded top vertices)
                    if selected_vertex < len(V_gd) // 2:
                        V_gd[selected_vertex] = [world_coords[0], world_coords[1], world_coords[2]]

                        # If this is a 3D extruded mesh, also update the corresponding top vertex
                        if len(V_gd) > len(V) and selected_vertex + len(V) < len(V_gd):
                            # Keep the same height for the top vertex
                            top_z = V_gd[selected_vertex + len(V)][2]
                            V_gd[selected_vertex + len(V)] = [world_coords[0], world_coords[1], top_z]
                else:
                    # In 2D mode, keep z at 0
                    V_gd[selected_vertex] = [world_coords[0], world_coords[1], 0]

                print(f"Updated V_gd[{selected_vertex}]: {V_gd[selected_vertex]}")
                mesh_gd.points = V_gd
        else:  # Newton's Method mesh
            if selected_vertex < len(V_newton):
                if mode_3d:
                    # In 3D mode, use all coordinates
                    # Only move the bottom vertices (not the extruded top vertices)
                    if selected_vertex < len(V_newton) // 2:
                        V_newton[selected_vertex] = [world_coords[0], world_coords[1], world_coords[2]]

                        # If this is a 3D extruded mesh, also update the corresponding top vertex
                        if len(V_newton) > len(V) and selected_vertex + len(V) < len(V_newton):
                            # Keep the same height for the top vertex
                            top_z = V_newton[selected_vertex + len(V)][2]
                            V_newton[selected_vertex + len(V)] = [world_coords[0], world_coords[1], top_z]
                else:
                    # In 2D mode, keep z at 0
                    V_newton[selected_vertex] = [world_coords[0], world_coords[1], 0]

                print(f"Updated V_newton[{selected_vertex}]: {V_newton[selected_vertex]}")
                mesh_newton.points = V_newton

        # Show position info in 2D or 3D based on mode
        if mode_3d:
            position_text = f"{world_coords[:3]}"
        else:
            position_text = f"{world_coords[:2]}"

        D_message = vd.Text2D(f"Moved vertex {selected_vertex} to {position_text}", pos=(0.7, 0.2), s=0.8, c='red')
        redraw()

def update_constraint_weights(event):
    global collision_weight, spring_weight
    if event.keypress == 'w':  # Increase collision weight
     collision_weight += 0.1
    elif event.keypress == 's':  # Decrease collision weight
        collision_weight = max(0, collision_weight - 0.1)
    elif event.keypress == 'e':  # Increase spring weight
        spring_weight += 0.1
    elif event.keypress == 'd':  # Decrease spring weight
        spring_weight = max(0, spring_weight - 0.1)
    print(f"Collision weight: {collision_weight}, Spring weight: {spring_weight}")


def OnLeftButtonRelease(event):
    global selection_message, selected_vertex
    if selected_vertex is not None:
        selection_message = vd.Text2D(f"Released vertex {selected_vertex}.", pos=(0.3, 0.2), s=0.8, c='red')
    redraw()


def run_optimization_steps(steps=20):
    global V_gd, V_newton, mesh_gd, mesh_newton, converged_gd, converged_newton, mode_3d

    for step in range(steps):
        print(f"Step {step + 1}/{steps}")

        if not converged_gd:
            if mode_3d:
                # In 3D mode, we need to handle the optimization differently
                # We'll only optimize the bottom vertices and then update the top vertices

                # Get the number of original vertices (bottom face)
                n_original = len(V_gd) // 2 if len(V_gd) > len(V) else len(V_gd)

                # Run optimization step on bottom vertices only
                bottom_vertices = V_gd[:n_original].copy()
                bottom_vertices = optimizer_gd.step(bottom_vertices, pinned_vertices_gd, pinned_positions_gd)

                # Update bottom vertices
                V_gd[:n_original] = bottom_vertices

                # Update top vertices to match bottom vertices (keeping their z-coordinate)
                if len(V_gd) > len(V):
                    for i in range(n_original):
                        if i + n_original < len(V_gd):
                            # Keep the same height for the top vertex
                            top_z = V_gd[i + n_original][2]
                            V_gd[i + n_original] = [bottom_vertices[i][0], bottom_vertices[i][1], top_z]
            else:
                # In 2D mode, run optimization normally
                V_gd = optimizer_gd.step(V_gd, pinned_vertices_gd, pinned_positions_gd)

                # Enforce 2D constraint
                V_gd[:, 2] = 0

            # Update mesh points
            mesh_gd.points = V_gd

            # Check convergence
            grad_gd = np.linalg.norm(optimizer_gd.femMesh.compute_gradient(V_gd[:len(V)] if mode_3d else V_gd))
            print(f"Gradient Descent Gradient Norm: {grad_gd}")
            if grad_gd < optimizer_gd.step_scale:
                converged_gd = True
                print("Gradient Descent converged.")

        if not converged_newton:
            if mode_3d:
                # In 3D mode, we need to handle the optimization differently
                # We'll only optimize the bottom vertices and then update the top vertices

                # Get the number of original vertices (bottom face)
                n_original = len(V_newton) // 2 if len(V_newton) > len(V) else len(V_newton)

                # Run optimization step on bottom vertices only
                bottom_vertices = V_newton[:n_original].copy()
                bottom_vertices = optimizer_newton.step(bottom_vertices, pinned_vertices_newton, pinned_positions_newton)

                # Update bottom vertices
                V_newton[:n_original] = bottom_vertices

                # Update top vertices to match bottom vertices (keeping their z-coordinate)
                if len(V_newton) > len(V):
                    for i in range(n_original):
                        if i + n_original < len(V_newton):
                            # Keep the same height for the top vertex
                            top_z = V_newton[i + n_original][2]
                            V_newton[i + n_original] = [bottom_vertices[i][0], bottom_vertices[i][1], top_z]
            else:
                # In 2D mode, run optimization normally
                V_newton = optimizer_newton.step(V_newton, pinned_vertices_newton, pinned_positions_newton)

                # Enforce 2D constraint
                V_newton[:, 2] = 0

            # Update mesh points
            mesh_newton.points = V_newton

            # Check convergence
            grad_newton = np.linalg.norm(optimizer_newton.femMesh.compute_gradient(V_newton[:len(V)] if mode_3d else V_newton))
            print(f"Newton's Method Gradient Norm: {grad_newton}")
            if grad_newton < optimizer_newton.step_scale:
                converged_newton = True
                print("Newton's Method converged.")

        # Update the display
        redraw()


def create_3d_mesh(vertices_2d, height=0.5):
    """
    Convert a 2D mesh to a 3D mesh by extruding it along the z-axis.

    Args:
        vertices_2d: The 2D vertices with z=0
        height: The height to extrude the mesh

    Returns:
        New 3D vertices and faces
    """
    # Get the number of vertices in the original mesh
    n_vertices = vertices_2d.shape[0]

    # Create a copy of the vertices with z=height
    vertices_top = vertices_2d.copy()
    vertices_top[:, 2] = height

    # Combine the bottom and top vertices
    vertices_3d = np.vstack([vertices_2d, vertices_top])

    # Get the original faces (triangles on the bottom)
    faces_bottom = F.copy()

    # Create faces for the top (same as bottom but with offset indices)
    faces_top = F.copy() + n_vertices
    # Reverse the order of vertices to ensure correct normals
    faces_top = np.fliplr(faces_top)

    # Create side faces (quads split into triangles)
    faces_sides = []

    # Get the boundary edges of the mesh
    edges = set()
    for face in F:
        for i in range(3):
            edge = (face[i], face[(i+1)%3])
            # Sort the edge vertices to ensure uniqueness
            edge = tuple(sorted(edge))
            if edge in edges:
                edges.remove(edge)  # Interior edge (appears twice)
            else:
                edges.add(edge)     # Boundary edge (appears once)

    # Convert to list and sort for consistent results
    boundary_edges = sorted(list(edges))

    # Create side faces for each boundary edge
    for v1, v2 in boundary_edges:
        # Create two triangles for each quad on the side
        faces_sides.append([v1, v2, v1 + n_vertices])
        faces_sides.append([v2, v2 + n_vertices, v1 + n_vertices])

    # Combine all faces
    if faces_sides:
        faces_3d = np.vstack([faces_bottom, faces_top, np.array(faces_sides)])
    else:
        faces_3d = np.vstack([faces_bottom, faces_top])

    return vertices_3d, faces_3d

def OnKeyPress(event):
    global key_pressed, converged_gd, converged_newton, mesh_gd, mesh_newton, V_gd, V_newton, mode_3d, F
    print(f"Raw keypress value: {event.keypress}")
    key_pressed = event.keypress
    print(f"Key pressed: {event.keypress}")

    if event.keypress == 'y':  # Switch to 2D mode
        mode_3d = False
        print("Switched to 2D mode")

        # Reset to original 2D mesh
        V_gd = V.copy()
        V_newton = V.copy()

        # Recreate the meshes with original faces
        plt.at(0).remove("Mesh")
        plt.at(1).remove("Mesh")

        # Create meshes with solid colors and visible edges
        mesh_gd = vd.Mesh([V_gd, F], c='yellow', alpha=0.8).linecolor('black').lw(2)
        mesh_newton = vd.Mesh([V_newton, F], c='yellow', alpha=0.8).linecolor('black').lw(2)

        plt.at(0).add(mesh_gd)
        plt.at(1).add(mesh_newton)

        # Reset camera to top view for 2D
        plt.at(0).reset_camera()
        plt.at(1).reset_camera()
        plt.at(0).camera.Elevation(90)  # Top view
        plt.at(1).camera.Elevation(90)  # Top view

        # Update the view
        redraw()

    elif event.keypress == 't':  # Switch to 3D mode
        mode_3d = True
        print("Switched to 3D mode")

        # Create 3D meshes by extrusion with a larger height for better visibility
        V_gd_3d, F_gd_3d = create_3d_mesh(V_gd, height=1.0)
        V_newton_3d, F_newton_3d = create_3d_mesh(V_newton, height=1.0)

        # Update vertices and faces
        V_gd = V_gd_3d
        V_newton = V_newton_3d

        # Recreate the meshes with new 3D faces
        plt.at(0).remove("Mesh")
        plt.at(1).remove("Mesh")

        # Create meshes with solid colors and visible edges
        mesh_gd = vd.Mesh([V_gd, F_gd_3d], c='yellow', alpha=0.8).linecolor('black').lw(2)
        mesh_newton = vd.Mesh([V_newton, F_newton_3d], c='yellow', alpha=0.8).linecolor('black').lw(2)

        plt.at(0).add(mesh_gd)
        plt.at(1).add(mesh_newton)

        # Set camera to a better angle for 3D viewing
        plt.at(0).camera.Elevation(30)  # Tilt camera up
        plt.at(0).camera.Azimuth(30)    # Rotate camera
        plt.at(1).camera.Elevation(30)
        plt.at(1).camera.Azimuth(30)

        # Update the view
        redraw()

    # Update weights, ground position, extrusion height, and collider based on keypress
    update_constraint_weights(event)
    update_ground_position(event)
    update_extrusion_height(event)
    update_collider(event)

    # Handle optimization steps
    if key_pressed == 'd':
        print("Key 'd' pressed: Ready to move selected vertex.")
    elif key_pressed == 'c':
        # Create a status message to show on the plot
        status_message = vd.Text2D("Running optimization step...", pos=(0.5, 0.2), s=1.0, c='blue')
        plt.add(status_message)
        plt.render()  # Update the display immediately to show the message

        # Apply a force to make the mesh deform more visibly
        # This simulates an external force pushing on the mesh
        if mode_3d:
            # In 3D mode, apply force to bottom vertices
            n_original = len(V_gd) // 2 if len(V_gd) > len(V) else len(V_gd)

            # Apply random force to some vertices
            for i in range(n_original):
                if np.random.random() < 0.3:  # 30% chance to apply force
                    # Don't apply force to pinned vertices
                    if i not in pinned_vertices_gd:
                        # Apply force in random direction
                        force = np.random.normal(0, 0.2, 3)
                        V_gd[i] += force

            # Update mesh
            mesh_gd.points = V_gd

            # Do the same for Newton's method mesh
            n_original = len(V_newton) // 2 if len(V_newton) > len(V) else len(V_newton)
            for i in range(n_original):
                if np.random.random() < 0.3:
                    if i not in pinned_vertices_newton:
                        force = np.random.normal(0, 0.2, 3)
                        V_newton[i] += force

            # Update mesh
            mesh_newton.points = V_newton
        else:
            # In 2D mode, apply force to all vertices
            for i in range(len(V_gd)):
                if np.random.random() < 0.3:
                    if i not in pinned_vertices_gd:
                        force = np.random.normal(0, 0.2, 3)
                        force[2] = 0  # Keep z=0 in 2D mode
                        V_gd[i] += force

            # Update mesh
            mesh_gd.points = V_gd

            # Do the same for Newton's method mesh
            for i in range(len(V_newton)):
                if np.random.random() < 0.3:
                    if i not in pinned_vertices_newton:
                        force = np.random.normal(0, 0.2, 3)
                        force[2] = 0  # Keep z=0 in 2D mode
                        V_newton[i] += force

            # Update mesh
            mesh_newton.points = V_newton

        # Render to show the initial deformation
        plt.render()

        # Gradient Descent optimization
        if not converged_gd:
            if mode_3d:
                # In 3D mode, we need to handle the optimization differently
                # We'll only optimize the bottom vertices and then update the top vertices

                # Get the number of original vertices (bottom face)
                n_original = len(V_gd) // 2 if len(V_gd) > len(V) else len(V_gd)

                # Run optimization step on bottom vertices only
                bottom_vertices = V_gd[:n_original].copy()

                # Show optimization info on the plot
                try:
                    energy_before = optimizer_gd.femMesh.compute_energy(bottom_vertices)
                    grad_norm_before = np.linalg.norm(optimizer_gd.femMesh.compute_gradient(bottom_vertices))

                    # Update status message
                    plt.remove(status_message)
                    status_message = vd.Text2D(f"GD: Energy={energy_before:.4f}, Gradient={grad_norm_before:.4f}",
                                             pos=(0.5, 0.2), s=1.0, c='blue')
                    plt.add(status_message)
                    plt.render()
                except:
                    pass

                # Run the optimization step
                bottom_vertices = optimizer_gd.step(bottom_vertices, pinned_vertices_gd, pinned_positions_gd)

                # Update bottom vertices
                V_gd[:n_original] = bottom_vertices

                # Update top vertices to match bottom vertices (keeping their z-coordinate)
                if len(V_gd) > len(V):
                    for i in range(n_original):
                        if i + n_original < len(V_gd):
                            # Keep the same height for the top vertex
                            top_z = V_gd[i + n_original][2]
                            V_gd[i + n_original] = [bottom_vertices[i][0], bottom_vertices[i][1], top_z]
            else:
                # In 2D mode, run optimization normally
                # Show optimization info on the plot
                try:
                    energy_before = optimizer_gd.femMesh.compute_energy(V_gd)
                    grad_norm_before = np.linalg.norm(optimizer_gd.femMesh.compute_gradient(V_gd))

                    # Update status message
                    plt.remove(status_message)
                    status_message = vd.Text2D(f"GD: Energy={energy_before:.4f}, Gradient={grad_norm_before:.4f}",
                                             pos=(0.5, 0.2), s=1.0, c='blue')
                    plt.add(status_message)
                    plt.render()
                except:
                    pass

                # Run the optimization step
                V_gd = optimizer_gd.step(V_gd, pinned_vertices_gd, pinned_positions_gd)

                # Enforce 2D constraint
                V_gd[:, 2] = 0

            # Update mesh points
            mesh_gd.points = V_gd

            # Check convergence and show results
            try:
                grad_gd = np.linalg.norm(optimizer_gd.femMesh.compute_gradient(V_gd[:len(V)] if mode_3d else V_gd))
                energy_after = optimizer_gd.femMesh.compute_energy(V_gd[:len(V)] if mode_3d else V_gd)

                # Update status message with results
                plt.remove(status_message)
                status_message = vd.Text2D(f"GD Result: Energy={energy_after:.4f}, Gradient={grad_gd:.4f}",
                                         pos=(0.5, 0.2), s=1.0, c='green' if grad_gd < optimizer_gd.step_scale else 'orange')
                plt.add(status_message)
                plt.render()

                if grad_gd < optimizer_gd.step_scale:
                    converged_gd = True
            except:
                pass

        # Newton's Method optimization
        if not converged_newton:
            # Update status message for Newton's method
            plt.remove(status_message)
            status_message = vd.Text2D("Running Newton's Method optimization...", pos=(0.5, 0.2), s=1.0, c='blue')
            plt.add(status_message)
            plt.render()

            if mode_3d:
                # In 3D mode, we need to handle the optimization differently
                # We'll only optimize the bottom vertices and then update the top vertices

                # Get the number of original vertices (bottom face)
                n_original = len(V_newton) // 2 if len(V_newton) > len(V) else len(V_newton)

                # Show optimization info on the plot
                try:
                    bottom_vertices = V_newton[:n_original].copy()
                    energy_before = optimizer_newton.femMesh.compute_energy(bottom_vertices)
                    grad_norm_before = np.linalg.norm(optimizer_newton.femMesh.compute_gradient(bottom_vertices))

                    # Update status message
                    plt.remove(status_message)
                    status_message = vd.Text2D(f"Newton: Energy={energy_before:.4f}, Gradient={grad_norm_before:.4f}",
                                             pos=(0.5, 0.2), s=1.0, c='blue')
                    plt.add(status_message)
                    plt.render()
                except:
                    pass

                # Run optimization step on bottom vertices only
                bottom_vertices = V_newton[:n_original].copy()
                bottom_vertices = optimizer_newton.step(bottom_vertices, pinned_vertices_newton, pinned_positions_newton)

                # Update bottom vertices
                V_newton[:n_original] = bottom_vertices

                # Update top vertices to match bottom vertices (keeping their z-coordinate)
                if len(V_newton) > len(V):
                    for i in range(n_original):
                        if i + n_original < len(V_newton):
                            # Keep the same height for the top vertex
                            top_z = V_newton[i + n_original][2]
                            V_newton[i + n_original] = [bottom_vertices[i][0], bottom_vertices[i][1], top_z]
            else:
                # In 2D mode, run optimization normally
                V_newton = optimizer_newton.step(V_newton, pinned_vertices_newton, pinned_positions_newton)

                # Enforce 2D constraint
                V_newton[:, 2] = 0

            # Update mesh points
            mesh_newton.points = V_newton

            # Check convergence
            grad_newton = np.linalg.norm(optimizer_newton.femMesh.compute_gradient(V_newton[:len(V)] if mode_3d else V_newton))
            print(f"Newton's Method Gradient Norm: {grad_newton}")
            if grad_newton < optimizer_newton.step_scale:
                converged_newton = True
                print("Newton's Method converged.")

        # Simply redraw the scene
        redraw()


def OnKeyRelease(event):
    global key_pressed
    if key_pressed in ['d', 's', 'c', 't', 'y', 'w', 'e', 'q', 'z', 'x', 'a', 'r', 'f']:
        key_pressed = None


plt = vd.Plotter(N=2, size=(1600, 800))
plt.camera.SetClippingRange(0.01, 1000)

instructions = vd.Text2D("  C to run a step\n  D to move selected vertex\n  Hold S and click to pin\n  T to switch to 3D mode (extrude mesh)\n  Y to switch to 2D mode (flat mesh)\n  Q/E to increase/decrease 3D extrusion height\n  Z/X to move ground up/down\n  A/D/W/S to move collider\n  R/F to increase/decrease collider radius\n", pos="bottom-left", s=1.0, c='blue')
plt.add(instructions)


plt.add_callback('LeftButtonPress', OnLeftButtonPress)
plt.add_callback('MouseMove', OnMouseMove)
plt.add_callback('LeftButtonRelease', OnLeftButtonRelease)
plt.add_callback('KeyPress', OnKeyPress)
plt.add_callback('KeyRelease', OnKeyRelease)

# Initial meshes with solid colors and visible edges
mesh_gd = vd.Mesh([V, F], c='yellow', alpha=0.8).linecolor('black').lw(2)
mesh_newton = vd.Mesh([V, F], c='yellow', alpha=0.8).linecolor('black').lw(2)

# Flatten vertex arrays to use for optimization
V_gd = V.copy()
V_newton = V.copy()

# Show both meshes side by side with 2D interaction and no grid/axes
plt.at(0).show(mesh_gd, "Gradient Descent Mesh", axes=False)
plt.at(1).show(mesh_newton, "Newton's Method Mesh", axes=False)

# Start interactive session
plt.interactive().close()
