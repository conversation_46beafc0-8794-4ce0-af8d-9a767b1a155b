#%% imports
import vedo as vd
import numpy as np
from vedo.pyplot import plot
from vedo import Latex
import random


vd.settings.default_backend= 'vtk'
rainbow_colors = [
    "#FF0000",  # Red
    "#FF7F00",  # Orange
    "#FFFF00",  # Yellow
    "#00FF00",  # Green
    "#0000FF",  # Blue
    "#4B0082",  # Indigo
    "#8B00FF"   # Violet
]

# Global learning rate
learning_rate = 0.1


#%% Callbacks
msg_grad = vd.Text2D(pos='bottom-left', font="VictorMono", c="red") # an empty text
msg_newton = vd.Text2D(pos='bottom-right', font="VictorMono", c="blue") # an empty text


def OnMouseMove(evt):                ### called every time mouse moves!
    global Xi, old_grad, gradient_values

    if evt.object is None:          # mouse hits nothing, return.
        return                       

    pt  = evt.picked3d               # 3d coords of point under mouse
    X   = np.array([pt[0],pt[1],objective(pt[0],pt[1])])  # X = (x,y,e(x,y))
    Xi = np.append(Xi,[X],axis=0)             # append to the list of points

    if len(Xi) > 1:               # need at least two points to compute a distance
        txt =(
            f"X:  {vd.precision(X,2)}\n"
            f"dX: {vd.precision(Xi[-1,0:2] - Xi[-2,0:2],2)}\n"
            f"dE: {vd.precision(Xi[-1,2] - Xi[-2,2],2)}\n"
        )
        #ar = vd.Arrow(Xi[-2,:], Xi[-1,:], s=0.001, c='orange5')
        #plt.add(ar) # add the arrow
    else:
        txt = f"X: {vd.precision(X,2)}"

    c = vd.Cylinder([np.append(Xi[-1,0:2], 0.0), Xi[-1,:]], r=0.01, c='orange5')
    plt.remove("Cylinder")    
    fp = fplt3d[0].flagpole(txt, point=X,s=0.08, c='k', font="Quikhand")
    fp.follow_camera()                 # make it always face the camera
    plt.remove("FlagPole") # remove the old flagpole

    plt.add(fp, c) # add the new flagpole and new cylinder

    plt.render()   # re-render the scene



def OnKeyPress(evt):               ### called every time a key is pressed
    global gradient_values, old_grad, old_newton, Xi_grad, Xi_newton
    if evt.keypress in ['c', 'C']: # reset Xi and the arrows
        Xi = np.empty((0, 3))
        plt.remove("Arrow").render()
        plt.remove("Sphere").render()
        gradient_values.clear()  # Clear function values
        global grad_candidate, grad_path, newton_candidate, newton_path

    if evt.keypress in ['g', 'G']:  # g key triggers a single gradient descent step
        if len(gradient_values) == 0:
            return  # Do nothing if no point exists
            
        # Compute the new candidate position with correct z-value
        #new_x, new_y = step(grad_candidate[:2], gradient_direction)
        new_x, new_y = optimize(objective, grad_candidate[:2], gradient_direction)

        new_z = objective(new_x, new_y)  # Calculate z based on the function
        new_candidate = np.array([new_x, new_y, new_z])
        
        # Add the new candidate to the grad_path and visualize with an arrow
        grad_path.append(new_candidate)  # Append the new candidate to the grad_path
        plt.add(vd.Arrow(grad_candidate, new_candidate, s=0.001, c='orange'))  # Draw an arrow
        plt.add(vd.Sphere(new_candidate, r=0.05, c='red'))  # Mark the new candidate
        # Update the current candidate
        grad_candidate = new_candidate
        
        # Update the 2D plot with the new function value
        obj = objective(grad_candidate[0], grad_candidate[1])  # Calculate z based on the function
        gradient_values.append(obj)  # Store the function value
        plt.remove(old_grad)  # Remove the previous clone
        new_grad = plot(list(range(len(gradient_values))), gradient_values, c='red', title="Gradient descent", xtitle="Point", ytitle="Value")
        old_grad = new_grad.clone2d(pos="top-left", size=1, ontop=True)
        plt.add(old_grad)  # Add the updated 2D plot

        # Update the text message
        X   = np.array([new_x,new_y,new_z])  # X = (x,y,e(x,y))
        Xi_grad = np.append(Xi_grad,[X],axis=0)
        if len(Xi_grad) > 1:               # need at least two points to compute a distance
            txt =(
            f"X:  {vd.precision(X,2)}\n"
            f"dX: {vd.precision(Xi_grad[-1,0:2] - Xi_grad[-2,0:2],2)}\n"
            f"dE: {vd.precision(Xi_grad[-1,2] - Xi_grad[-2,2],2)}\n"
            )
        else:
            txt = f"X: {vd.precision(X,2)}"
    
        msg_grad.text(txt) 

        plt.render()  # Re-render to update the scene



    if evt.keypress in ['n', 'N']:  # n key triggers a single newton step
        if len(newton_values) == 0:
            return
        # Calculate the Newton's method step direction
        #step = Newton_direction(objective, newton_candidate[:2])

        # Compute the new candidate position with correct z-value
        #new_x, new_y = step(newton_candidate[:2], Newton_direction)
        new_x, new_y = optimize(objective, newton_candidate[:2], Newton_direction)

        new_z = objective(new_x, new_y)  # Calculate z based on the function
        new_candidate = np.array([new_x, new_y, new_z])

        # Add the new candidate to the newton_path and visualize with an arrow
        newton_path.append(new_candidate)  # Append the new candidate to the newton_path
        plt.add(vd.Arrow(newton_candidate, new_candidate, s=0.001, c='blue'))  # Draw an arrow
        plt.add(vd.Sphere(new_candidate, r=0.05, c='blue'))  # Mark the new candidate
        # Update the current candidate
        newton_candidate = new_candidate

        # Update the 2D plot with the new function value
        obj = objective(newton_candidate[0], newton_candidate[1])  # Calculate z based on the function
        newton_values.append(obj)  # Store the function value
        plt.remove(old_newton)  # Remove the previous clone
        new_newton = plot(list(range(len(newton_values))), newton_values, c='blue', title="Newton's Method", xtitle="Point", ytitle="Value")
        old_newton = new_newton.clone2d(pos="top-right", size=1, ontop=True)
        plt.add(old_newton)  # Add the updated 2D plot

        # Update the text message
        X   = np.array([new_x,new_y,new_z])
        Xi_newton = np.append(Xi_newton,[X],axis=0)
        if len(Xi_newton) > 1:               # need at least two points to compute a distance
            txt =(
            f"X:  {vd.precision(X,2)}\n"
            f"dX: {vd.precision(Xi_newton[-1,0:2] - Xi_newton[-2,0:2],2)}\n"
            f"dE: {vd.precision(Xi_newton[-1,2] - Xi_newton[-2,2],2)}\n"
            )
        else:
            txt = f"X: {vd.precision(X,2)}"

        msg_newton.text(txt)


        plt.render()  # Re-render to update the scene



def OnLeftButtonClick(evt):
    if evt.picked3d is None:
        return  # Do nothing if nothing is clicked

    point = evt.picked3d
    global grad_candidate, newton_candidate, grad_path, gradient_values, newton_values, old_grad, old_newton  # These must be declared global to modify them within the function
    
    x, y = point[0], point[1]
    z = objective(x, y)  # Obtain z from the function

    gradient_values.clear()
    gradient_values.append(z)  # Store the function value
    grad_candidate = np.array([x,y,z])
    grad_path = [grad_candidate]  # Reset the grad_path to the new candidate
    
    newton_values.clear()
    newton_values.append(z)  # Store the function value
    newton_candidate = np.array([x,y,z])
    newton_path = [newton_candidate]  # Reset the newton_path to the new candidate

    plt.remove(old_grad)  # Remove previous 2D plot
    new_grad = plot(list(range(len(gradient_values))), gradient_values, c='red', title="Gradient descent", xtitle="Point", ytitle="Value")
    old_grad = new_grad.clone2d(pos="top-left", size=1, ontop=True)
    plt.add(old_grad)  # Add the updated 2D plot

    plt.remove(old_newton)  # Remove previous 2D plot
    new_newton = plot(list(range(len(newton_values))), newton_values, c='blue', title="Newton's Method", xtitle="Point", ytitle="Value")
    old_newton = new_newton.clone2d(pos="top-right", size=1, ontop=True)
    plt.add(old_newton)  # Add the updated 2D plot

    # Clear previous visualization and add a sphere at the new candidate position
    plt.remove("Arrow")  # Remove existing arrows
    plt.remove("Sphere")  # Remove existing spheres
    plt.add(vd.Sphere(grad_candidate, r=0.05, c='orange'))  # Mark the new candidate
    plt.render()  # Re-render to update the scene



def OnRightButtonClick(evt):
    if evt.picked3d is None:
        return  # No object under right-click
    
    # Choose a random color from the global list
    new_color = random.choice(rainbow_colors)
    print(new_color)
    
    # Change the color of the plot
    fplt3d[0].c(new_color)  # Change the mesh color
    
    # Re-render the plotter to reflect the color change
    plt.render()


def OnSliderAlpha(widget, event): ### called every time the slider is moved
    val = widget.value         # get the slider value
    fplt3d[0].alpha(val)       # set the alpha (transparency) value of the surface
    fplt3d[1].alpha(val)       # set the alpha (transparency) value of the isolines


#%% Optimization functions
def gradient_fd(func, X, h=0.001): # finite difference gradient
    x, y = X[0], X[1]
    gx = (func(x+h, y) - func(x-h, y)) / (2*h)
    gy = (func(x, y+h) - func(x, y-h)) / (2*h)
    return gx, gy

def Hessian_fd(func, X, h=0.001): # finite difference Hessian
    x, y = X[0], X[1]
    gxx = (func(x+h, y) - 2*func(x, y) + func(x-h, y)) / h**2
    gyy = (func(x, y+h) - 2*func(x, y) + func(x, y-h)) / h**2
    gxy = (func(x+h, y+h) - func(x+h, y-h) - func(x-h, y+h) + func(x-h, y-h)) / (4*h**2)
    H = np.array([[gxx, gxy], [gxy, gyy]])
    return H

def gradient_direction(func, X): # compute gradient step direction
    x, y = X[0], X[1]
    gx, gy = gradient_fd(func, X)
    return -np.array([gx, gy])

def Newton_direction(func, X):   # compute Newton step direction
    x, y = X[0], X[1]
    gx, gy = gradient_fd(func, X)
    H = Hessian_fd(func, [x, y])
    d = -np.linalg.solve(H, np.array([gx, gy]))
    return d[0],d[1]

def line_search(func, X, dx, dy): 
    x, y = X[0], X[1]
    alpha = 0.6
    while func(x, y) < func(x + alpha*dx, y + alpha*dy):  # If the function value increases, reduce alpha
        alpha *= 0.5                              # by half and try again
    return alpha

def step(X, search_direction_function):
    x, y = X[0], X[1]
    dx, dy = search_direction_function(objective, [x, y])
    alpha = line_search(objective ,[x, y], dx, dy)
    return x + alpha*dx, y + alpha*dy

def optimize(func, X, search_direction_function, tol=0.0005, iter_max=1000):
    x, y = X[0], X[1]
    new_x, new_y = x, y
    for i in range(iter_max):
        new_x, new_y = step([x, y], search_direction_function)
        if np.linalg.norm(gradient_fd(func, [x, y])) < tol:
            break
        x, y = new_x, new_y
    return x, y

# Initialize 
Xi = np.empty((0, 3))
Xi_grad = np.empty((0, 3))
Xi_newton = np.empty((0, 3))

gradient_values = []
newton_values = []
grad_candidate = np.array([0, 0])
grad_path = [grad_candidate]
newton_candidate = np.array([0, 0])
newton_path = [newton_candidate]



#%% Plotting

def objective(x,y):
    #x, y = X[0], X[1]
    return np.sin(2*x*y) * np.cos(3*y)/2+1/2

plt = vd.Plotter(bg2='lightblue')  # Create the plotter
fplt3d = plot(lambda x,y: objective(x,y), c='terrain')      # create a plot from the function e. fplt3d is a list containing surface mesh, isolines, and axis
fplt2d = fplt3d.clone()            # clone the plot to create a 2D plot


fplt2d[0].lighting('off')          # turn off lighting for the 2D plot
fplt2d[0].vertices[:,2] = 0        # set the z-coordinate of the mesh to 0
fplt2d[1].vertices[:,2] = 0        # set the z-coordinate of the isolines to 0

x = [0]
y = [0]
# Prepare grad plot
new_grad = plot(x, y, c='red', title="Gradient Descent", xtitle="Point", ytitle="Value")  # Create a 2D line plot from the data
old_grad = new_grad.clone2d(pos="top-left", scale=0.8, ontop=True)  # Create a 2D line plot from the data

# Preapre newton plot
new_newton = plot(x, y, c='blue', title="Newton's Method", xtitle="Point", ytitle="Value")  # Create a 2D line plot from the data
old_newton = new_newton.clone2d(pos="top-right", scale=0.8, ontop=True)  # Create a 2D line plot from the data

plt.add_callback('mouse move', OnMouseMove) # add Mouse move callback
plt.add_callback('key press', OnKeyPress) # add Keyboard callback
plt.add_callback("right mouse click", OnRightButtonClick)
plt.add_callback("mouse click", OnLeftButtonClick)
plt.add_slider(OnSliderAlpha,0.,1.,1., title="Alpha", pos=3 ) # add a slider for the alpha value of the surface
plt.show([fplt3d, fplt2d, old_grad, old_newton], msg_grad, msg_newton, __doc__, viewup='z')

plt.close()
# %%