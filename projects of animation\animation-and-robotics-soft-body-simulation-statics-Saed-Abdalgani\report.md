# Animation and Robotics - Assignment 2: <br> Soft Body Simulation - Statics

**Name:** <PERSON><PERSON>  
**ID:** 325812667

## Task 1: Create a <PERSON>sh for the Simulation

### 1.1 Custom Shape Creation

For this task, I created a hexagon instead of the square used in the sample code. The hexagon was defined using six vertices evenly distributed around a circle:

```python
# Hexagon Mesh
vertices = np.array([
    [0, 1],
    [0.87, 0.5],
    [0.87, -0.5],
    [0, -1],
    [-0.87, -0.5],
    [-0.87, 0.5]
])
```

This array of 2D points defines the hexagonal shape. I chose this shape because it provides a more uniform distribution of vertices compared to a square, allowing for a more realistic simulation of the elastic body.

![Hexagon Shape](task1.1.png) 

### 1.2 Triangulation with ~100 Vertices & No Interior Points

After creating the hexagon, I triangulated it using the `triangle` library with specific options to control the triangulation process:

```python
area = 0.01
tris = tr.triangulate({"vertices": vertices}, f'qa{area}Y')
```

Where:
- `q` - enforces a minimum triangle angle quality, ensuring triangles aren't too sharp
- `a{area}` - caps maximum triangle area at 0.01, which yields approximately 100 vertices
- `Y` - prevents insertion of Steiner points (no interior vertices)

Then, I created the vertex and face arrays:

```python
V = np.c_[tris['vertices'], np.zeros(tris['vertices'].shape[0])]
F = tris['triangles']
```

Where `V` contains the vertex coordinates (with Z=0 for 2D) and `F` contains the triangle indices.

![Triangulated Mesh](task1.2.png)




## Task 2: Implement Basic UI for Moving Points

### 2.1 Printing Information to the Vedo Window

Instead of printing information to the IPython console, I modified the code to display information directly on the Vedo window using `Text2D` objects:

```python
selection_message = vd.Text2D('Mouse hits nothing', pos="bottom-left", s=0.8, c='red')
plt.add(selection_message)
```

This makes interaction with the program much easier, as the user can see comments and instructions directly on the screen while interacting with the model.

![Information Display](task2.1.png)

### 2.2 Mechanism for Pinning and Moving Vertices

I implemented a mechanism that allows the user to pin vertices and move them:

1. **Pinning Vertices**: The user can press the 's' key and click on a vertex to pin it. Pinned vertices are stored in lists:
   ```python
   if key_pressed == 's':
       if Vi not in pinned_vertices:
           pinned_vertices.append(Vi)
           pinned_positions.append(V[Vi])  # Save the position
       else:
           index = pinned_vertices.index(Vi)
           pinned_vertices.remove(Vi)
           pinned_positions.pop(index)  # Remove the corresponding position
   ```

2. **Moving Vertices**: The user can press the 'd' key and drag a vertex to move it:
   ```python
   if selected_vertex is not None and key_pressed == 'd':
       world_coords = plt.compute_world_coordinate(mouse_pos)
       V_gd[selected_vertex] = [world_coords[0], world_coords[1], 0]
   ```

Pinned vertices are displayed in red, and the selected vertex is displayed in blue, providing visual feedback to the user.

![moving Vertices](task3.3.mp4)
![Pinning Vertices](task2.2.png)

### 2.3 Switching Between 2D and 3D Modes

I implemented a mechanism to switch between 2D and 3D modes:

```python
if event.keypress == 'y':  # Switch to 2D mode
    mode_3d = False
    # Reset the mesh to 2D mode
elif event.keypress == 't':  # Switch to 3D mode
    mode_3d = True
    # Create a 3D mesh by extrusion
```

In 3D mode, the mesh is extruded along the Z-axis, and the user can move vertices in 3D space. The user can also adjust the extrusion height using the 'q'/'e' keys.

![2D&3D Mode](task2.3-gif.mp4)




## Task 3: Test the Optimization Pipeline

### 3.1 Optimization with ZeroLengthSpringEnergy

I created a FEMMesh using `ZeroLengthSpringEnergy` and implemented optimization steps:

```python
femMesh_zero = FEMMesh(V.copy(), F, ZeroLengthSpringEnergy(), EdgeStencil())
optimizer_zero = MeshOptimizer(femMesh_zero)

for k in range(20):
    x_zero = optimizer_zero.step(x_zero, [], [])
    energies_zero.append(femMesh_zero.compute_energy(x_zero))
```

The results showed that:
- Gradient Descent converges slowly but steadily
- Newton's Method converges much faster, but can be unstable in some cases

![After Convergence](task3.1.png)

### 3.2 Optimization with SpringEnergy

I performed the same test using `SpringEnergy`:


femMesh_gd = FEMMesh(V.copy(), F, SpringEnergy(l), EdgeStencil())
optimizer_gd = MeshOptimizer(femMesh_gd)


The results showed that:
- `SpringEnergy` maintains edge lengths close to the rest length `l`
- Unlike `ZeroLengthSpringEnergy`, the mesh doesn't collapse to a single point

![SpringEnergy Optimization](task%203.2.png)

### 3.3 Pinning Vertices to Specific Locations

I implemented a mechanism to pin vertices to specific locations using soft constraints:

1. **Storing Pinned Vertices and Their Positions**:
   ```python
   pinned_vertices_gd = []       # List of pinned vertex indices
   pinned_positions_gd = []      # List of pinned vertex positions
   ```

2. **Applying Pinning Constraints During Optimization**:
   ```python
   # In the step function
   if V_pinned and pinned_positions:
       for i, idx in enumerate(V_pinned):
           new_x[idx] = pinned_positions[i]
   ```

3. **Applying Pinning Constraints in Gradient Computation**:
   ```python
   # In the compute_gradient function
   for vid, (target, weight) in self.constraints['pinned'].items():
       grad[vid] += 2 * weight * (X[vid] - target)
   ```

4. **Adjusting Constraint Weights**:
   ```python
   # In the update_constraint_weights function
   if event.keypress == 'w':  # Increase collision weight
       collision_weight += 0.1
   elif event.keypress == 's':  # Decrease collision weight
       collision_weight = max(0, collision_weight - 0.1)
   ```

The results showed that:
- Increasing constraint weights makes pinned vertices more rigid
- Decreasing constraint weights allows more flexibility around pinned vertices

![Pinned Vertices](task3.3.mp4)
![Effect of Different Weights](task33.3.mp4)
![Effect of Different Weights](task3.3333.png)    

### 3.4 Comparing Analytical and Numerical Derivatives

I implemented analytical derivatives for `SpringEnergy` and compared them with numerical derivatives:


def gradient(self, X, x):
    # Calculate analytical derivatives
    x1, x2 = x[0], x[1]
    current_length = np.linalg.norm(x1 - x2)
    direction = (x1 - x2) / current_length
    return (current_length - self.L) * np.concatenate([direction, -direction])

def hessian(self, X, x):
    # Calculate analytical Hessian
    x1, x2 = x[0], x[1]
    current_length = np.linalg.norm(x1 - x2)
    I = np.eye(3)
    outer = np.outer(x1 - x2, x1 - x2) / current_length**3
    return np.block([[I - outer, -I + outer], [-I + outer, I - outer]])
```

The results showed that:
- Analytical derivatives are about 10 times faster than numerical derivatives
- The difference between analytical and numerical derivatives is very small (about 1e-6)




## Task 4: Collisions

### 4.1 Ground Collision

I implemented an energy to prevent vertices from penetrating the ground:

```python
def ground_gradient(self, x):
    if x[2] < self.constraints['ground_z']:
        return np.array([0, 0, self.constraints['collision_weight'] * (x[2] - self.constraints['ground_z'])])
    return np.zeros(3)
```

This energy adds a force that pushes vertices upward when they are below the ground level. The user can adjust the ground position using the 'z'/'x' keys and modify the collision energy weight using the 'w'/'s' keys.

![Ground Collision](task4.1.mp4)
![Effect of Ground Weight](task4.11.png)

### 4.2 Ball Collision

I implemented an energy to prevent vertices from penetrating a ball:

```python
def sphere_gradient(self, x, collider, collision_weight=None):
    vec = x - collider['position']
    dist = np.linalg.norm(vec)
    
    if dist >= collider['radius']:
        return np.zeros(3)
        
    if collision_weight is None:
        collision_weight = self.constraints['collision_weight']
        
    return collision_weight * (dist - collider['radius']) * (vec / dist)
```

This energy adds a force that pushes vertices outward when they are inside the ball. The user can move the ball using the 'a'/'d'/'w'/'s' keys and adjust the ball size using the 'r'/'f' keys.

![Ball Collision](task4.2.png)
![Effect of Ball Weight](task4.22.png)

## Conclusion

In this project, I implemented a simulation of elastic bodies using a mass-spring system. I learned how to:
- Create a triangulated mesh for simulation
- Implement an interactive user interface
- Implement and optimize optimization algorithms
- Implement collision energies with ground and ball

The main challenges I faced were implementing analytical derivatives and ensuring the stability of optimization algorithms. I overcame these challenges through a deeper understanding of the underlying mathematics and implementing appropriate regularization techniques.
