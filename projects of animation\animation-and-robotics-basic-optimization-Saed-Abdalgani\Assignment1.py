#%% [1] IMPORTS
import vedo as vd
import numpy as np
from vedo.pyplot import plot
from vedo import Latex
import time
import random
import timeit  # Added for timing tests

vd.settings.default_backend = 'vtk'


#%% [2] GLOBAL VARIABLES & SETTINGS

rainbow_colors = [
    "#FF0000",  # Red
    "#FF7F00",  # Orange
    "#FFFF00",  # Yellow
    "#00FF00",  # Green
    "#0000FF",  # Blue
    "#4B0082",  # Indigo
    "#8B00FF"   # Violet
]

# For text messages on screen
msg_grad   = vd.Text2D(pos='bottom-left',  font="VictorMono", c="red")
msg_newton = vd.Text2D(pos='bottom-right', font="VictorMono", c="blue")

# For storing the mouse path
Xi = np.empty((0, 3))       # The array of points from mouse movement

# We also keep separate arrays for gradient descent vs. Newton paths
Xi_grad    = np.empty((0, 3))
Xi_newton  = np.empty((0, 3))

gradient_values = []  # f-values for gradient descent steps
newton_values   = []  # f-values for Newton steps
dot_values      = []  # store dot(gradient, direction) for Newton steps

# Initialize x, y, z with default values (e.g., 0.0)
x, y, z = 0.0, 0.0, 0.0
grad_candidate = np.array([x, y, z])
grad_path        = [grad_candidate]
newton_candidate = np.array([x, y, z])
newton_path      = [newton_candidate]

# We keep references to 2D plots so we can remove them & replace them
old_grad   = None
old_newton = None
old_dot    = None   # for the dot product 2D plot

# For real-time plotting of mouse path function values
mouse_values = []
old_clone_xi = None

# For toggling or disabling the continuous path creation by the mouse
MOUSE_CREATES_PATH = True  # If you want to disable, set this to False

# GUI-based function switching
# We'll have two objective modes: "sin-cos" and "poly"
OBJECTIVE_MODE = "sin-cos"  # default

# We’ll keep a global step size for gradient descent, plus # steps
GD_STEP_SIZE = 0.1
NUM_STEPS = 5

# If Hessian is indefinite, how to fallback:
#   1 => fallback to gradient descent
#   2 => shift Hessian
HESSIAN_FALLBACK_METHOD = 1

# Tolerances and such
TOL = 1e-6
MAX_ITER = 300


#%% [3] OBJECTIVE FUNCTIONS & SWITCHING

def objective_sin_cos(x, y):
    """
    sin-cos function from your code:
      f(x,y) = 0.5 + 0.5*sin(2*x*y)*cos(3*y)
    (the factor 0.5 is embedded in the expression)
    """
    return np.sin(2*x*y)*np.cos(3*y)/2.0 + 0.5

def objective_poly(x, y):
    """
    A simple polynomial:
      f(x,y) = 0.5*(x^2 + y^2)
    """
    return 0.5*(x**2 + y**2)

def current_objective(x, y):
    """
    Master objective that calls the chosen function
    based on OBJECTIVE_MODE
    """
    global OBJECTIVE_MODE
    if OBJECTIVE_MODE == "sin-cos":
        return objective_sin_cos(x, y)
    else:
        return objective_poly(x, y)

def objective(x, y):
    """
    Preserving the original signature used in version 1,
    but it actually delegates to current_objective
    """
    return current_objective(x, y)


#%% [4] ANALYTIC VS. NUMERIC DERIVATIVES

# ~~~ ANALYTIC for sin-cos function ~~~
def gradient_analytic_sin_cos(x, y):
    # from your snippet
    dx = 2.0*y*np.cos(2.0*x*y)*np.cos(3.0*y)/2.0
    dy = (2.0*x*np.cos(2.0*x*y)*np.cos(3.0*y)/2.0
          - 3.0*np.sin(2.0*x*y)*np.sin(3.0*y)/2.0)
    return np.array([dx, dy])

def hessian_analytic_sin_cos(x, y):
    dx2 = -4.0*(y**2)*np.sin(2.0*x*y)*np.cos(3.0*y)/2.0
    term1 = -4.0*(x**2)*np.sin(2.0*x*y)*np.cos(3.0*y)/2.0
    term2 = -9.0*np.sin(2.0*x*y)*np.cos(3.0*y)/2.0
    term3 = -12.0*x*np.cos(2.0*x*y)*np.sin(3.0*y)/2.0
    dy2 = term1 + term2 + term3
    dxy = (
        2.0*np.cos(2.0*x*y)*np.cos(3.0*y)/2.0
        - 6.0*x*y*np.sin(2.0*x*y)*np.cos(3.0*y)/2.0
        - 3.0*np.cos(2.0*x*y)*np.sin(3.0*y)/2.0
    )
    return np.array([[dx2, dxy], [dxy, dy2]])

# ~~~ ANALYTIC for polynomial ~~~
def gradient_analytic_poly(x, y):
    return np.array([x, y])

def hessian_analytic_poly(x, y):
    return np.array([[1.0, 0.0], [0.0, 1.0]])


def gradient_analytic(x, y):
    """
    Master gradient that picks the correct function
    """
    global OBJECTIVE_MODE
    if OBJECTIVE_MODE == "sin-cos":
        return gradient_analytic_sin_cos(x, y)
    else:
        return gradient_analytic_poly(x, y)

def hessian_analytic(x, y):
    """
    Master Hessian that picks the correct function
    """
    global OBJECTIVE_MODE
    if OBJECTIVE_MODE == "sin-cos":
        return hessian_analytic_sin_cos(x, y)
    else:
        return hessian_analytic_poly(x, y)

# ~~~ FINITE-DIFFERENCE ~~~
def gradient_fd(func, X, h=1e-5):
    x, y = X
    gx = (func(x+h, y) - func(x-h, y)) / (2.0*h)
    gy = (func(x, y+h) - func(x, y-h)) / (2.0*h)
    return np.array([gx, gy])

def Hessian_fd(func, X, h=1e-5):
    x, y = X
    f00 = func(x, y)
    fph = func(x+h, y)
    fmh = func(x-h, y)
    f0p = func(x, y+h)
    f0m = func(x, y-h)
    fpp = func(x+h, y+h)
    fpm = func(x+h, y-h)
    fmp = func(x-h, y+h)
    fmm = func(x-h, y-h)

    gxx = (fph - 2*f00 + fmh)/(h**2)
    gyy = (f0p - 2*f00 + f0m)/(h**2)
    gxy = (fpp - fpm - fmp + fmm)/(4*h**2)
    return np.array([[gxx, gxy], [gxy, gyy]])

#%% [5] INDEFINITE HESSIAN FALLBACK

def is_positive_def(H):
    """Check if Hessian is positive-definite by looking at eigenvalues."""
    e, _ = np.linalg.eig(H)
    return np.all(e > 0)

def fallback_newton_direction(x, y, numeric=True):
    """
    Newton direction with fallback if Hessian is indefinite:
      1) fallback to gradient descent, or
      2) shift Hessian until positive-def.
    """
    global HESSIAN_FALLBACK_METHOD

    # pick numeric or analytic
    if numeric:
        g = gradient_fd(lambda xx, yy: objective(xx, yy), [x, y])
        H = Hessian_fd(lambda xx, yy: objective(xx, yy), [x, y])
    else:
        g = gradient_analytic(x, y)
        H = hessian_analytic(x, y)

    # Check if H is invertible & positive-def
    try:
        # If H is indefinite and we want to fix it:
        if not is_positive_def(H):
            if HESSIAN_FALLBACK_METHOD == 1:
                # fallback to gradient descent direction
                return fallback_gd_direction(x, y, numeric)
            else:
                # shift Hessian
                shift_amt = 1e-5
                while not is_positive_def(H):
                    H += shift_amt*np.eye(2)
        # Solve
        d = -np.linalg.solve(H, g)
    except np.linalg.LinAlgError:
        # fallback to gradient descent direction
        return fallback_gd_direction(x, y, numeric)

    return d

def fallback_gd_direction(x, y, numeric=True):
    if numeric:
        g = gradient_fd(lambda xx, yy: objective(xx, yy), [x, y])
    else:
        g = gradient_analytic(x, y)
    return -g


#%% [6] LINE SEARCH & OPTIMIZATION STEPS

def line_search(func, X, d, alpha=1.0):
    """
    A standard backtracking line search with Armijo condition.
    """
    c = 1e-4
    grad = gradient_fd(func, X)
    f_val = func(X[0], X[1])

    while func(X[0] + alpha*d[0], X[1] + alpha*d[1]) > f_val + c*alpha*np.dot(grad, d):
        alpha *= 0.5
        if alpha < 1e-7:
            break
    return alpha

def gradient_descent_step(x, y, step_size=GD_STEP_SIZE):
    """
    One step of gradient descent with a fixed step size (no line search).
    Using numeric gradient for demonstration.
    """
    g = gradient_fd(lambda xx, yy: objective(xx, yy), [x, y])
    return x - step_size*g[0], y - step_size*g[1]

def gradient_descent_step_line_search(x, y):
    """
    One step of gradient descent with line search.
    """
    g = gradient_fd(lambda xx, yy: objective(xx, yy), [x, y])
    d = -g
    alpha = line_search(lambda xx, yy: objective(xx, yy), [x, y], d)
    return x + alpha*d[0], y + alpha*d[1]

def newton_step_line_search(x, y):
    """
    Newton step using fallback approach and line search.
    """
    d = fallback_newton_direction(x, y, numeric=True)  # we use numeric here
    alpha = line_search(lambda xx, yy: objective(xx, yy), [x, y], d)
    return x + alpha*d[0], y + alpha*d[1]


#%% [7] GUI CALLBACKS

def OnMouseMove(evt):
    """
    1) Real-time function-value plotting for Xi.
    2) The user wanted to disable it for the optimization,
       but let's keep it toggled with MOUSE_CREATES_PATH = True/False.
    """
    global Xi, mouse_values, old_clone_xi, MOUSE_CREATES_PATH

    if not MOUSE_CREATES_PATH:
        return

    if evt.picked3d is None:  # Check if no 3D object is picked
        return

    pt3d = evt.picked3d
    x, y = pt3d[0], pt3d[1]
    z = objective(x, y)


    X = np.array([x, y, z])
    Xi = np.append(Xi, [X], axis=0)

    # real-time text
    if len(Xi) > 1:
        txt = (
            f"X:  {vd.precision(X,2)}\n"
            f"dX: {vd.precision(Xi[-1,0:2] - Xi[-2,0:2],2)}\n"
            f"dE: {vd.precision(Xi[-1,2] - Xi[-2,2],2)}\n"
        )
        # optional arrow
        #arrow = vd.Arrow(Xi[-2], Xi[-1], s=0.001, c='orange5')
        #plt.add(arrow)
    else:
        txt = f"X: {vd.precision(X,2)}"

    # show a cylinder from z=0 up to the surface
    cyl = vd.Cylinder(
        [np.append(Xi[-1,0:2], 0.0), Xi[-1,:]],
        r=0.01,
        c='orange5'
    )
    plt.remove("Cylinder")
    cyl.name = "Cylinder"

    # flagpole
    fp = fplt3d[0].flagpole(txt, point=X, s=0.08, c='k', font="Quikhand")
    fp.follow_camera()
    plt.remove("FlagPole")
    fp.name = "FlagPole"

    plt.add(fp, cyl)

    # add the function value to mouse_values, and plot in real-time
    mouse_values.append(z)
    # remove old clone
    global old_clone_xi
    if old_clone_xi is not None:
        plt.remove(old_clone_xi)

    curve = plot(
        range(len(mouse_values)), mouse_values,
        title="Mouse Path f-values",
        xtitle="Index",
        ytitle="f(x,y)",
        c='magenta'
    )
    clone_xi = curve.clone2d(pos=(0.01, 0.7), size=0.35, ontop=True)
    old_clone_xi = clone_xi
    plt.add(clone_xi)

    plt.render()

def OnKeyPress(evt):
   
    global Xi, mouse_values, old_clone_xi
    global gradient_values, old_grad, old_newton, dot_values, old_dot
    global grad_candidate, grad_path, newton_candidate, newton_path
    global OBJECTIVE_MODE
    if evt.keypress in ['t', 'T']:   
        toggle_objective_mode(evt)
    if evt.keypress in ['c', 'C']:
        # reset Xi and the arrows
        Xi = np.empty((0,3))
        mouse_values = []
        if old_clone_xi is not None:
            plt.remove(old_clone_xi)
            old_clone_xi = None
        plt.remove("Arrow").render()
        plt.remove("Sphere").render()
        gradient_values.clear()
        dot_values.clear()
        # grad_candidate, grad_path, newton_candidate, newton_path are also
        # accessible/assignable now that we've declared them global above.

    # Single-step gradient descent
    if evt.keypress in ['g', 'G']:
        if len(gradient_values) == 0:
            return
        new_x, new_y = gradient_descent_step_line_search(grad_candidate[0], grad_candidate[1])
        new_z = objective(new_x, new_y)
        new_candidate = np.array([new_x, new_y, new_z])

        grad_path.append(new_candidate)
        plt.add(vd.Arrow(grad_candidate, new_candidate, s=0.001, c='orange'))
        plt.add(vd.Sphere(new_candidate, r=0.05, c='red'))

        grad_candidate = new_candidate

        gradient_values.append(new_z)
        if old_grad is not None:
            plt.remove(old_grad)
        new_grad_plot = plot(
            range(len(gradient_values)), gradient_values,
            c='red',
            title="Gradient descent",
            xtitle="Point",
            ytitle="Value"
        )
        clone_g = new_grad_plot.clone2d(pos="top-left", size=1, ontop=True)
        old_grad = clone_g
        plt.add(clone_g)

        # text message
        global Xi_grad
        Xi_grad = np.append(Xi_grad, [new_candidate], axis=0)
        if len(Xi_grad) > 1:
            txt = (
                f"X:  {vd.precision(new_candidate,2)}\n"
                f"dX: {vd.precision(Xi_grad[-1,0:2] - Xi_grad[-2,0:2],2)}\n"
                f"dE: {vd.precision(Xi_grad[-1,2] - Xi_grad[-2,2],2)}\n"
            )
        else:
            txt = f"X: {vd.precision(new_candidate,2)}"
        msg_grad.text(txt)
        plt.render()


    # Single-step Newton
    if evt.keypress in ['n', 'N']:
        if len(newton_values) == 0:
            return
        new_x, new_y = newton_step_line_search(newton_candidate[0], newton_candidate[1])
        new_z = objective(new_x, new_y)
        new_candidate = np.array([new_x, new_y, new_z])

        newton_path.append(new_candidate)
        plt.add(vd.Arrow(newton_candidate, new_candidate, s=0.001, c='blue'))
        plt.add(vd.Sphere(new_candidate, r=0.05, c='blue'))

        newton_candidate = new_candidate

        newton_values.append(new_z)
        if old_newton is not None:
            plt.remove(old_newton)
        new_newton_plot = plot(
            range(len(newton_values)), newton_values,
            c='blue',
            title="Newton's Method",
            xtitle="Point",
            ytitle="Value"
        )
        clone_n = new_newton_plot.clone2d(pos="top-right", size=1, ontop=True)
        old_newton = clone_n
        plt.add(clone_n)

        # Dot product with gradient for newton steps
        grad_n = gradient_fd(lambda xx,yy: objective(xx,yy), new_candidate[:2])
        direction_approx = new_candidate[:2] - newton_candidate[:2]  # Will be zero if done after update
        if len(newton_path) > 1:
            old_pt = newton_path[-2][:2]
            direction_approx = new_candidate[:2] - old_pt
        dot_val = np.dot(grad_n, direction_approx)
        dot_values.append(dot_val)

        # Plot the dot values in top center
        if old_dot is not None:
            plt.remove(old_dot)
        dot_plot = plot(
            range(len(dot_values)), dot_values,
            c='green',
            title="Newton Dot(grad, dir)",
            xtitle="Step",
            ytitle="DotProd"
        )
        cloned_dot = dot_plot.clone2d(pos=(0.3,0.7), size=0.2, ontop=True)
        old_dot = cloned_dot
        plt.add(cloned_dot)

        global Xi_newton
        Xi_newton = np.append(Xi_newton, [new_candidate], axis=0)
        if len(Xi_newton) > 1:
            txt = (
                f"X:  {vd.precision(new_candidate,2)}\n"
                f"dX: {vd.precision(Xi_newton[-1,0:2] - Xi_newton[-2,0:2],2)}\n"
                f"dE: {vd.precision(Xi_newton[-1,2] - Xi_newton[-2,2],2)}\n"
            )
        else:
            txt = f"X: {vd.precision(new_candidate,2)}"
        msg_newton.text(txt)

        plt.render()



def OnLeftButtonClick(evt):
    """
    Clear the path, set the initial guess for optimization to the clicked point.
    """
    if evt.picked3d is None:
        return

    point = evt.picked3d
    x, y = point[0], point[1]
    z = objective(x, y)

    global grad_candidate, newton_candidate, grad_path, gradient_values, newton_values
    global old_grad, old_newton, old_dot, dot_values

    # Clear old paths
    gradient_values.clear()
    newton_values.clear()
    dot_values.clear()

    grad_candidate = np.array([x,y,z])
    grad_path = [grad_candidate]

    newton_candidate = np.array([x,y,z])
    newton_path = [newton_candidate]

    # remove old 2D clones
    if old_grad is not None:
        plt.remove(old_grad)
        old_grad = None
    if old_newton is not None:
        plt.remove(old_newton)
        old_newton = None
    if old_dot is not None:
        plt.remove(old_dot)
        old_dot = None

    gradient_values.append(z)
    newton_values.append(z)

    # show the new 2D plots
    new_grad_plot = plot(
        range(len(gradient_values)), gradient_values,
        c='red',
        title="Gradient descent",
        xtitle="Point",
        ytitle="Value"
    )
    
    old_grad = new_grad_plot.clone2d(pos="top-left", size=1, ontop=True)
    plt.add(old_grad)

    new_newton_plot = plot(
        range(len(newton_values)), newton_values,
        c='blue',
        title="Newton's Method",
        xtitle="Point",
        ytitle="Value"
    )
    old_newton = new_newton_plot.clone2d(pos="top-right", size=1, ontop=True)
    plt.add(old_newton)

    # also remove old shapes
    plt.remove("Arrow")
    plt.remove("Sphere")

    sphere = vd.Sphere(grad_candidate, r=0.05, c='orange')
    sphere.name = "Sphere"
    plt.add(sphere)
    plt.render()


def OnRightButtonClick(evt):
    """
    Right-click callback that changes the color of the mesh to a random color
    (already implemented from version 1).
    """
    if evt.picked3d is None:
        return
    new_color = random.choice(rainbow_colors)
    print("New color:", new_color)
    fplt3d[0].c(new_color)
    plt.render()

def OnSliderAlpha(widget, event):
    """
    Slider controlling the alpha of the surface
    """
    val = widget.value
    fplt3d[0].alpha(val)
    fplt3d[1].alpha(val)


#%% [8] EXTRA SLIDERS/BUTTONS

def on_slider_step_size(widget, event):
    """
    Let the user set the step size for gradient descent from a slider
    """
    global GD_STEP_SIZE
    GD_STEP_SIZE = widget.value

def on_slider_num_steps(widget, event):
    """
    Let the user set how many steps to run from a slider
    """
    global NUM_STEPS
    # convert to int
    NUM_STEPS = int(widget.value)

def toggle_objective_mode(evt):
    """
    Toggle function switching between 'sin-cos' and 'poly'
    Then we re-draw the main surface.
    """
    global OBJECTIVE_MODE
    if OBJECTIVE_MODE == "sin-cos":
        OBJECTIVE_MODE = "poly"
    else:
        OBJECTIVE_MODE = "sin-cos"

    redraw_surface()
    plt.render()

def run_multi_steps(evt, state=None):
    global plt
    global grad_candidate, newton_candidate
    global gradient_values, newton_values
    global old_grad, old_newton, old_dot
    global dot_values

    for _ in range(NUM_STEPS):
        # Gradient Descent step
        xg, yg, zg = grad_candidate
        nxg, nyg = gradient_descent_step(xg, yg, step_size=GD_STEP_SIZE)
        nzg = objective(nxg, nyg)
        new_g = np.array([nxg, nyg, nzg])
        plt.add(vd.Arrow(grad_candidate, new_g, s=0.001, c='red'))
        grad_candidate = new_g
        gradient_values.append(nzg)

        # Newton step
        xn, yn, zn = newton_candidate
        nnx, nny = newton_step_line_search(xn, yn)
        nnz = objective(nnx, nny)
        new_n = np.array([nnx, nny, nnz])
        plt.add(vd.Arrow(newton_candidate, new_n, s=0.001, c='blue'))
        newton_candidate = new_n
        newton_values.append(nnz)

        # Dot product for Newton
        grad_n = gradient_fd(lambda xx, yy: objective(xx, yy), [nnx, nny])
        direction_approx = new_n[:2] - np.array([xn, yn])
        dot_val = np.dot(grad_n, direction_approx)
        dot_values.append(dot_val)

    # Update 2D plots
    if old_grad is not None:
        plt.remove(old_grad)
    new_grad_plot = plot(
        range(len(gradient_values)), gradient_values,
        title="GD path: f-values",
        xtitle="Iter",
        ytitle="f(x,y)",
        c='red'
    )
    new_clone_grad = new_grad_plot.clone2d(pos=(0.02, 0.7), size=0.2, ontop=True)
    old_grad = new_clone_grad
    plt.add(new_clone_grad)

    if old_newton is not None:
        plt.remove(old_newton)
    new_newton_plot = plot(
        range(len(newton_values)), newton_values,
        title="Newton path: f-values",
        xtitle="Iter",
        ytitle="f(x,y)",
        c='blue'
    )
    new_clone_newton = new_newton_plot.clone2d(pos=(0.25, 0.7), size=0.2, ontop=True)
    old_newton = new_clone_newton
    plt.add(new_clone_newton)

    if old_dot is not None:
        plt.remove(old_dot)
    dot_plot = plot(
        range(len(dot_values)), dot_values,
        title="Newton: Dot(grad, dir)",
        xtitle="Step",
        ytitle="Dot",
        c='green'
    )
    new_clone_dot = dot_plot.clone2d(pos=(0.48, 0.7), size=0.2, ontop=True)
    old_dot = new_clone_dot
    plt.add(new_clone_dot)

    plt.render()

def compare_numeric_analytic(evt, state=None):
    points_to_test = [(0.3, 0.5), (1.0, 2.0), (-1.2, 0.8)]
    eps_list = [1e-3, 1e-5, 1e-7]

    start_num = time.time()
    for (xx, yy) in points_to_test:
        for eps in eps_list:
            _ = gradient_fd(lambda x, y: objective(x, y), [xx, yy], eps)
            _ = Hessian_fd(lambda x, y: objective(x, y), [xx, yy], eps)
    numeric_time = time.time() - start_num

    start_ana = time.time()
    for (xx, yy) in points_to_test:
        _ = gradient_analytic(xx, yy)
        _ = hessian_analytic(xx, yy)
    analytic_time = time.time() - start_ana

    x_test, y_test = (0.3, 0.4)
    msg_lines = [
        "=== Gradient Differences ===",
        f"Numeric FD total time: {numeric_time:.6f} s",
        f"Analytic total time:   {analytic_time:.6f} s",
        "",
    ]
    for eps in eps_list:
        g_num = gradient_fd(lambda x, y: objective(x, y), [x_test, y_test], eps)
        g_ana = gradient_analytic(x_test, y_test)
        diff = np.linalg.norm(g_num - g_ana)
        msg_lines.append(f"eps={eps:.0e} -> diff={diff:.2e}")

    msg = "\n".join(msg_lines)
    print(msg)  # Print the message to the terminal 

    def test_different_initial_points(evt, state=None):
     global plt
    init_points = [(0.1, 0.1), (1.0, 1.0), (2.0, -1.0), (-1.0, 2.0)]
    def stopping_criterion(x, y, iteration):
        g = gradient_fd(lambda xx, yy: objective(xx, yy), [x, y])
        if np.linalg.norm(g) < TOL:
            return True
        if iteration >= MAX_ITER:
            return True
        return False

    print("=== Testing different initial points for GD & Newton ===")
    for (ix, iy) in init_points:
        print(f"  Start: ({ix}, {iy})")
        # gradient descent
        xg, yg = ix, iy
        iter_count_g = 0
        while True:
            iter_count_g += 1
            xg_new, yg_new = gradient_descent_step_line_search(xg, yg)
            if stopping_criterion(xg_new, yg_new, iter_count_g):
                break
            xg, yg = xg_new, yg_new
        print(f"    GD converged in {iter_count_g} steps -> final = ({xg_new:.4f},{yg_new:.4f})")

        # newton
        xn, yn = ix, iy
        iter_count_n = 0
        while True:
            iter_count_n += 1
            xn_new, yn_new = newton_step_line_search(xn, yn)
            if stopping_criterion(xn_new, yn_new, iter_count_n):
                break
            xn, yn = xn_new, yn_new
        print(f"    Newton converged in {iter_count_n} steps -> final=({xn_new:.4f},{yn_new:.4f})")

#%% [9] REDRAW SURFACE WHEN OBJECTIVE SWITCHES

fplt3d = None
fplt2d = None

def redraw_surface():
    global fplt3d, fplt2d
    if fplt3d is not None:
        for o in fplt3d:
            plt.remove(o)
    # recompute
    fplt3d = plot(lambda xx, yy: objective(xx, yy), c='terrain')
    fplt2d = fplt3d.clone()
    fplt2d[0].lighting('off')
    fplt2d[0].vertices[:,2] = 0
    fplt2d[1].vertices[:,2] = 0
    plt.add(fplt3d, fplt2d)
    plt.render()

#%% [10] WRAPPING UP MAIN

# We'll create a Plotter, add callbacks, sliders, and buttons

plt = vd.Plotter(bg2='lightblue')

# initial draw
fplt3d = plot(lambda x,y: objective(x,y), c='terrain')
fplt2d = fplt3d.clone()
fplt2d[0].lighting('off')
fplt2d[0].vertices[:,2] = 0
fplt2d[1].vertices[:,2] = 0

plt.add_callback('mouse move',    OnMouseMove)
plt.add_callback('key press',     OnKeyPress)
plt.add_callback("right mouse click", OnRightButtonClick)
plt.add_callback("mouse click",   OnLeftButtonClick)

plt.add_slider(OnSliderAlpha, 0., 1., 1., title="Alpha", pos=3)

# slider for GD step size
plt.add_slider(
    on_slider_step_size,
    0.01, 1.0,
    value=GD_STEP_SIZE,
    title="GD Step Size",
    pos=4
)

# slider for number of steps
plt.add_slider(
    on_slider_num_steps,
    1, 30,
    value=NUM_STEPS,
    title="# Steps",
    pos=5
)

#%% [11] TIMING TEST FUNCTION

def timing_test():
    """
    Perform timing tests for numerical and analytical gradient and Hessian computations.
    """
    test_point = [0.2, 0.8]  # Test point

    # Execute the function 5000 times and take the average execution time
    numerical_grad_time = timeit.timeit(lambda: gradient_fd(objective, test_point), number=5000) / 5000
    anal_grad_time = timeit.timeit(lambda: gradient_analytic(test_point[0], test_point[1]), number=5000) / 5000

    numerical_hess_time = timeit.timeit(lambda: Hessian_fd(objective, test_point), number=5000) / 5000
    anal_hess_time = timeit.timeit(lambda: hessian_analytic(test_point[0], test_point[1]), number=5000) / 5000

    # Convert to nanoseconds
    numerical_grad_time_ns = numerical_grad_time * 1e9
    anal_grad_time_ns = anal_grad_time * 1e9

    numerical_hess_time_ns = numerical_hess_time * 1e9
    anal_hess_time_ns = anal_hess_time * 1e9

    # Print the results
    print("=== Timing Results ===")
    print(f"Numerical Gradient Time: {numerical_grad_time_ns:.2f} ns")
    print(f"Analytical Gradient Time: {anal_grad_time_ns:.2f} ns")
    print(f"Numerical Hessian Time: {numerical_hess_time_ns:.2f} ns")
    print(f"Analytical Hessian Time: {anal_hess_time_ns:.2f} ns")

def test_gradient_descent_step_sizes():
    """
    Test gradient descent with different step sizes and report the results.
    """
    step_sizes = [0.01, 0.05, 0.1, 0.5, 1.0]  # Different step sizes to test
    initial_point = [0.5, 0.5]  # Starting point for the test

    print("=== Testing Gradient Descent with Different Step Sizes ===")
    for step_size in step_sizes:
        x, y = initial_point  # Reset to the initial point for each step size
        path = []
        for _ in range(20):  # Run 20 iterations
            path.append((x, y, objective(x, y)))
            x, y = gradient_descent_step(x, y, step_size=step_size)
        print(f"Step Size: {step_size:.2f}, Final Point: ({x:.4f}, {y:.4f}), Final Value: {objective(x, y):.4f}")


def compare_numerical_vs_analytical():
    """
    Compare the time taken for numerical vs. analytical gradient and Hessian computations.
    """
    test_point = [0.5, 0.5]  # Test point

    numerical_grad_time = timeit.timeit(lambda: gradient_fd(objective, test_point), number=1000)
    analytical_grad_time = timeit.timeit(lambda: gradient_analytic(test_point[0], test_point[1]), number=1000)

    numerical_hess_time = timeit.timeit(lambda: Hessian_fd(objective, test_point), number=1000)
    analytical_hess_time = timeit.timeit(lambda: hessian_analytic(test_point[0], test_point[1]), number=1000)

    print("=== Numerical vs Analytical Timing ===")
    print(f"Numerical Gradient Time: {numerical_grad_time:.6f} s")
    print(f"Analytical Gradient Time: {analytical_grad_time:.6f} s")
    print(f"Numerical Hessian Time: {numerical_hess_time:.6f} s")
    print(f"Analytical Hessian Time: {analytical_hess_time:.6f} s")

def compare_finite_difference_eps():
    test_point = [0.5, 0.5]  # Test point
    eps_values = [1e-3, 1e-5, 1e-7]  # Different ε values to test

    print("=== Finite Difference Gradient Comparison ===")
    for eps in eps_values:
        numerical_grad = gradient_fd(objective, test_point, h=eps)
        analytical_grad = gradient_analytic(test_point[0], test_point[1])
        diff = np.linalg.norm(numerical_grad - analytical_grad)
        print(f"ε: {eps:.0e}, Numerical Gradient: {numerical_grad}, Analytical Gradient: {analytical_grad}, Difference: {diff:.2e}")

#%% [12] MAIN EXECUTION

if __name__ == "__main__":
    # Perform timing tests
    timing_test()

    # Compare numeric vs. analytic derivatives
    print("\n=== Comparing Numeric vs. Analytic Derivatives ===")
    compare_numeric_analytic(None)

    # Test different initial points
    print("\n=== Testing Different Initial Points ===")
    def test_different_initial_points(evt, state=None):
        """
        Test gradient descent and Newton's method with different initial points.
        """
        init_points = [(0.1, 0.1), (1.0, 1.0), (2.0, -1.0), (-1.0, 2.0)]
        def stopping_criterion(x, y, iteration):
            g = gradient_fd(lambda xx, yy: objective(xx, yy), [x, y])
            if np.linalg.norm(g) < TOL:
                return True
            if iteration >= MAX_ITER:
                return True
            return False
    
        print("=== Testing different initial points for GD & Newton ===")
        for (ix, iy) in init_points:
            print(f"  Start: ({ix}, {iy})")
            # gradient descent
            xg, yg = ix, iy
            iter_count_g = 0
            while True:
                iter_count_g += 1
                xg_new, yg_new = gradient_descent_step_line_search(xg, yg)
                if stopping_criterion(xg_new, yg_new, iter_count_g):
                    break
                xg, yg = xg_new, yg_new
            print(f"    GD converged in {iter_count_g} steps -> final = ({xg_new:.4f},{yg_new:.4f})")
    
            # newton
            xn, yn = ix, iy
            iter_count_n = 0
            while True:
                iter_count_n += 1
                xn_new, yn_new = newton_step_line_search(xn, yn)
                if stopping_criterion(xn_new, yn_new, iter_count_n):
                    break
                xn, yn = xn_new, yn_new
            print(f"    Newton converged in {iter_count_n} steps -> final=({xn_new:.4f},{yn_new:.4f})")
    
    test_different_initial_points(None)


    # Test Newton's method behavior
    def test_newton_behavior():
        """
        Test Newton's method behavior for different starting points.
        """
        test_points = [
            (0.1, 0.1),  # Near a minimum
            (1.0, 0.0),  # Near a saddle point
            (-1.0, -1.0)  # Near a maximum
        ]
    
        print("=== Testing Newton's Method Behavior ===")
        for x, y in test_points:
            print(f"Starting Point: ({x}, {y})")
            for _ in range(10):  # Run 10 iterations
                x_new, y_new = newton_step_line_search(x, y)
                print(f"  Point: ({x_new:.4f}, {y_new:.4f}), Value: {objective(x_new, y_new):.4f}")
                x, y = x_new, y_new
    
    test_newton_behavior()

    # Compare optimization methods
    def compare_methods():
        """
        Compare gradient descent, vanilla Newton's method, and Hessian modification methods.
        """
        initial_points = [(0.5, 0.5), (1.0, 1.0), (-1.0, -1.0)]  # Different starting points
    
        print("=== Comparing Optimization Methods ===")
        for x, y in initial_points:
            print(f"Starting Point: ({x}, {y})")
    
            # Gradient Descent
            x_g, y_g = x, y
            for _ in range(20):
                x_g, y_g = gradient_descent_step_line_search(x_g, y_g)
            print(f"  Gradient Descent Final: ({x_g:.4f}, {y_g:.4f}), Value: {objective(x_g, y_g):.4f}")
    
            # Vanilla Newton's Method
            x_n, y_n = x, y
            for _ in range(20):
                x_n, y_n = newton_step_line_search(x_n, y_n)
            print(f"  Vanilla Newton Final: ({x_n:.4f}, {y_n:.4f}), Value: {objective(x_n, y_n):.4f}")
    
            # Hessian Modification Method 1
            global HESSIAN_FALLBACK_METHOD
            HESSIAN_FALLBACK_METHOD = 1
            x_h1, y_h1 = x, y
            for _ in range(20):
                x_h1, y_h1 = newton_step_line_search(x_h1, y_h1)
            print(f"  Hessian Fallback 1 Final: ({x_h1:.4f}, {y_h1:.4f}), Value: {objective(x_h1, y_h1):.4f}")
    
            # Hessian Modification Method 2
            HESSIAN_FALLBACK_METHOD = 2
            x_h2, y_h2 = x, y
            for _ in range(20):
                x_h2, y_h2 = newton_step_line_search(x_h2, y_h2)
            print(f"  Hessian Fallback 2 Final: ({x_h2:.4f}, {y_h2:.4f}), Value: {objective(x_h2, y_h2):.4f}")
    
    compare_methods()

    # Compare numerical vs analytical computations
    compare_numerical_vs_analytical()

    # Compare finite difference gradients for different ε values
    compare_finite_difference_eps()

    # Show the plot
    plt.show([fplt3d, fplt2d, msg_grad, msg_newton], __doc__, viewup='z')
    plt.close()




