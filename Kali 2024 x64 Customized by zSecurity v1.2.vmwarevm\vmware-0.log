2025-03-24T01:30:29.616Z In(05) vmx Log for VMware Workstation pid=14004 version=17.5.2 build=build-23775571 option=Release
2025-03-24T01:30:29.616Z In(05) vmx The host is x86_64.
2025-03-24T01:30:29.616Z In(05) vmx Host codepage=windows-1255 encoding=windows-1255
2025-03-24T01:30:29.616Z In(05) vmx Host is Windows 11 Pro, 64-bit (Build 26100.3476)
2025-03-24T01:30:29.616Z In(05) vmx Host offset from UTC is -02:00.
2025-03-24T01:30:29.590Z In(05) vmx VTHREAD 15012 "vmx"
2025-03-24T01:30:29.595Z In(05) vmx LOCALE windows-1255 -> NULL User=409 System=40d
2025-03-24T01:30:29.595Z In(05) vmx Msg_SetLocaleEx: HostLocale=windows-1255 UserLocale=NULL
2025-03-24T01:30:29.601Z In(05) vmx DictionaryLoad: Cannot open file "C:\Users\<USER>\AppData\Roaming\VMware\config.ini": The system cannot find the file specified.
2025-03-24T01:30:29.601Z In(05) vmx Msg_Reset:
2025-03-24T01:30:29.601Z In(05) vmx [msg.dictionary.load.openFailed] Cannot open file "C:\Users\<USER>\AppData\Roaming\VMware\config.ini": The system cannot find the file specified.
2025-03-24T01:30:29.601Z In(05) vmx ----------------------------------------
2025-03-24T01:30:29.601Z In(05) vmx ConfigDB: Failed to load C:\Users\<USER>\AppData\Roaming\VMware\config.ini
2025-03-24T01:30:29.601Z In(05) vmx Win32U_GetFileAttributes: GetFileAttributesExW("C:\Users\<USER>\Desktop\Kali 2024 x64 Customized by zSecurity v1.2.vmwarevm\Kali 2024 x64 Customized by zSecurity v1.2.vmpl", ...) failed, error: 2
2025-03-24T01:30:29.601Z In(05) vmx OBJLIB-LIB: Objlib initialized.
2025-03-24T01:30:29.603Z In(05) vmx DictionaryLoad: Cannot open file "C:\Users\<USER>\AppData\Roaming\VMware\config.ini": The system cannot find the file specified.
2025-03-24T01:30:29.603Z In(05) vmx [msg.dictionary.load.openFailed] Cannot open file "C:\Users\<USER>\AppData\Roaming\VMware\config.ini": The system cannot find the file specified.
2025-03-24T01:30:29.603Z In(05) vmx PREF Optional preferences file not found at C:\Users\<USER>\AppData\Roaming\VMware\config.ini. Using default values.
2025-03-24T01:30:29.607Z In(05) vmx SSL Error: error:80000002:system library::No such file or directory
2025-03-24T01:30:29.607Z In(05) vmx SSL Error: error:10000080:BIO routines::no such file
2025-03-24T01:30:29.607Z In(05) vmx SSL Error: error:07000072:configuration file routines::no such file
2025-03-24T01:30:29.607Z Wa(03) vmx SSLConfigLoad: Failed to load OpenSSL config file.
2025-03-24T01:30:29.609Z In(05) vmx lib/ssl: OpenSSL using default provider
2025-03-24T01:30:29.611Z In(05) vmx lib/ssl: Client usage
2025-03-24T01:30:29.611Z In(05) vmx lib/ssl: protocol list tls1.2
2025-03-24T01:30:29.611Z In(05) vmx lib/ssl: protocol min 0x303 max 0x303
2025-03-24T01:30:29.611Z In(05) vmx lib/ssl: protocol list tls1.2 (openssl flags 0x36000000)
2025-03-24T01:30:29.611Z In(05) vmx lib/ssl: cipher list ECDHE+AESGCM:RSA+AESGCM:ECDHE+AES:RSA+AES
2025-03-24T01:30:29.611Z In(05) vmx lib/ssl: cipher suites TLS_AES_128_GCM_SHA256:TLS_AES_256_GCM_SHA384
2025-03-24T01:30:29.611Z In(05) vmx lib/ssl: curves list prime256v1:secp384r1:secp521r1
2025-03-24T01:30:29.611Z In(05) vmx lib/ssl: Server usage
2025-03-24T01:30:29.611Z In(05) vmx lib/ssl: protocol list tls1.2
2025-03-24T01:30:29.611Z In(05) vmx lib/ssl: protocol min 0x303 max 0x303
2025-03-24T01:30:29.611Z In(05) vmx lib/ssl: protocol list tls1.2 (openssl flags 0x36000000)
2025-03-24T01:30:29.611Z In(05) vmx lib/ssl: cipher list ECDHE+AESGCM:RSA+AESGCM:ECDHE+AES:RSA+AES
2025-03-24T01:30:29.611Z In(05) vmx lib/ssl: cipher suites TLS_AES_128_GCM_SHA256:TLS_AES_256_GCM_SHA384
2025-03-24T01:30:29.611Z In(05) vmx lib/ssl: curves list prime256v1:secp384r1:secp521r1
2025-03-24T01:30:29.620Z In(05) vmx Hostname=DESKTOP-Q1DBRSH
2025-03-24T01:30:29.626Z In(05) vmx IP=fe80::77:3f79:dcd3:7165%16
2025-03-24T01:30:29.626Z In(05) vmx IP=fe80::7e94:4b4a:8f20:117e%7
2025-03-24T01:30:29.626Z In(05) vmx IP=fe80::857d:fad3:4e57:9c68%4
2025-03-24T01:30:29.626Z In(05) vmx IP=************
2025-03-24T01:30:29.626Z In(05) vmx IP=************
2025-03-24T01:30:29.626Z In(05) vmx IP=**************
2025-03-24T01:30:29.648Z In(05) vmx System uptime 711792430764 us
2025-03-24T01:30:29.648Z In(05) vmx Command line: "C:\Program Files (x86)\VMware\VMware Workstation\x64\vmware-vmx.exe" "-T" "querytoken" "-s" "vmx.stdio.keep=TRUE" "-#" "product=1;name=VMware Workstation;version=17.5.2;buildnumber=23775571;licensename=VMware Workstation;licenseversion=17.0;" "-@" "pipe=\\.\pipe\vmxc21add496e5deeb1;msgs=ui" "C:\Users\<USER>\Desktop\Kali 2024 x64 Customized by zSecurity v1.2.vmwarevm\Kali 2024 x64 Customized by zSecurity v1.2.vmx"
2025-03-24T01:30:29.648Z In(05) vmx Msg_SetLocaleEx: HostLocale=windows-1255 UserLocale=NULL
2025-03-24T01:30:29.671Z In(05) vmx WQPoolAllocPoll : pollIx = 1, signalHandle = 848
2025-03-24T01:30:29.671Z In(05) vmx WQPoolAllocPoll : pollIx = 2, signalHandle = 856
2025-03-24T01:30:29.671Z In(05) vmx VigorTransport listening on fd 808
2025-03-24T01:30:29.671Z In(05) vmx Vigor_Init 1
2025-03-24T01:30:29.671Z In(05) vmx Connecting 'ui' to pipe '\\.\pipe\vmxc21add496e5deeb1' with user '(null)'
2025-03-24T01:30:29.671Z In(05) vmx VMXVmdb: Local connection timeout: 60000 ms.
2025-03-24T01:30:29.680Z In(05) vmx VmdbAddConnection: cnxPath=/db/connection/#1/, cnxIx=1
2025-03-24T01:30:29.680Z In(05) vmx Vix: [mainDispatch.c:488]: VMAutomation: Initializing VMAutomation.
2025-03-24T01:30:29.680Z In(05) vmx Vix: [mainDispatch.c:740]: VMAutomationOpenListenerSocket() listening
2025-03-24T01:30:29.688Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=0, newAppState=1870, success=1 additionalError=0
2025-03-24T01:30:29.688Z In(05) vmx Transitioned vmx/execState/val to poweredOff
2025-03-24T01:30:29.688Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=1, newAppState=1873, success=1 additionalError=0
2025-03-24T01:30:29.688Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=2, newAppState=1877, success=1 additionalError=0
2025-03-24T01:30:29.688Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=3, newAppState=1881, success=1 additionalError=0
2025-03-24T01:30:29.711Z In(05) vmx IOPL_VBSRunning: VBS is set to 0
2025-03-24T01:30:29.720Z In(05) vmx IOCTL_VMX86_SET_MEMORY_PARAMS already set
2025-03-24T01:30:29.720Z In(05) vmx FeatureCompat: No EVC masks.
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID vendor: GenuineIntel
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID family: 0x6 model: 0x7e stepping: 0x5
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID codename: Ice Lake-U/Y
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID name: Intel(R) Core(TM) i3-1005G1 CPU @ 1.20GHz
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 00000000, 0: 0x0000001b 0x756e6547 0x6c65746e 0x49656e69
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 00000001, 0: 0x000706e5 0x00100800 0x7ffafbbf 0xbfebfbff
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 00000002, 0: 0x00feff01 0x000000f0 0x00000000 0x00000000
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 00000003, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 00000004, 0: 0x1c004121 0x02c0003f 0x0000003f 0x00000000
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 00000004, 1: 0x1c004122 0x01c0003f 0x0000003f 0x00000000
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 00000004, 2: 0x1c004143 0x01c0003f 0x000003ff 0x00000000
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 00000004, 3: 0x1c03c163 0x03c0003f 0x00000fff 0x00000006
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 00000004, 4: 0x00000000 0x00000000 0x00000000 0x00000000
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 00000005, 0: 0x00000040 0x00000040 0x00000003 0x11121020
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 00000006, 0: 0x0017aff7 0x00000002 0x00000009 0x00000000
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 00000007, 0: 0x00000000 0xf2bf27ef 0x40405f4e 0xbc000410
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 00000008, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 00000009, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 0000000a, 0: 0x08300805 0x00000000 0x0000000f 0x00008604
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 0000000b, 0: 0x00000001 0x00000002 0x00000100 0x00000000
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 0000000b, 1: 0x00000004 0x00000004 0x00000201 0x00000000
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 0000000b, 2: 0x00000000 0x00000000 0x00000002 0x00000000
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 0000000c, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 0000000d, 0: 0x000002e7 0x00000a80 0x00000a88 0x00000000
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 0000000d, 1: 0x0000000f 0x00000a00 0x00002100 0x00000000
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 0000000d, 2: 0x00000100 0x00000240 0x00000000 0x00000000
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 0000000d, 3: 0x00000000 0x00000000 0x00000000 0x00000000
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 0000000d, 4: 0x00000000 0x00000000 0x00000000 0x00000000
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 0000000d, 5: 0x00000040 0x00000440 0x00000000 0x00000000
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 0000000d, 6: 0x00000200 0x00000480 0x00000000 0x00000000
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 0000000d, 7: 0x00000400 0x00000680 0x00000000 0x00000000
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 0000000d, 8: 0x00000080 0x00000000 0x00000001 0x00000000
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 0000000d, 9: 0x00000008 0x00000a80 0x00000000 0x00000000
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 0000000d, a: 0x00000000 0x00000000 0x00000000 0x00000000
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 0000000d, b: 0x00000000 0x00000000 0x00000000 0x00000000
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 0000000d, c: 0x00000000 0x00000000 0x00000000 0x00000000
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 0000000d, d: 0x00000008 0x00000000 0x00000001 0x00000000
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 0000000e, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 0000000f, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 0000000f, 1: 0x00000000 0x00000000 0x00000000 0x00000000
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 00000010, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 00000010, 1: 0x00000000 0x00000000 0x00000000 0x00000000
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 00000010, 2: 0x00000000 0x00000000 0x00000000 0x00000000
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 00000010, 3: 0x00000000 0x00000000 0x00000000 0x00000000
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 00000011, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 00000012, 0: 0x00000063 0x00000001 0x00000000 0x00002f1f
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 00000012, 1: 0x000000b6 0x00000000 0x000002e7 0x00000000
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 00000012, 2: 0x50180001 0x00000000 0x0bc00001 0x00000000
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 00000012, 3: 0x00000000 0x00000000 0x00000000 0x00000000
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 00000013, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 00000014, 0: 0x00000001 0x0000000f 0x00000007 0x00000000
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 00000014, 1: 0x02490002 0x003f1fff 0x00000000 0x00000000
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 00000015, 0: 0x00000002 0x0000003e 0x0249f000 0x00000000
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 00000016, 0: 0x000004b0 0x00000d48 0x00000064 0x00000000
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 00000017, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 00000018, 0: 0x00000007 0x00000000 0x00000000 0x00000000
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 00000018, 1: 0x00000000 0x00080007 0x00000001 0x00004122
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 00000018, 2: 0x00000000 0x0010000f 0x00000001 0x00004125
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 00000018, 3: 0x00000000 0x00040001 0x00000010 0x00004024
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 00000018, 4: 0x00000000 0x00040006 0x00000008 0x00004024
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 00000018, 5: 0x00000000 0x00080008 0x00000001 0x00004124
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 00000018, 6: 0x00000000 0x00080007 0x00000080 0x00004043
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 00000018, 7: 0x00000000 0x00080009 0x00000080 0x00004043
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 00000019, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 0000001a, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 0000001b, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 80000000, 0: 0x80000008 0x00000000 0x00000000 0x00000000
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 80000001, 0: 0x00000000 0x00000000 0x00000121 0x2c100800
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 80000002, 0: 0x65746e49 0x2952286c 0x726f4320 0x4d542865
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 80000003, 0: 0x33692029 0x3030312d 0x20314735 0x20555043
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 80000004, 0: 0x2e312040 0x48473032 0x0000007a 0x00000000
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 80000005, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 80000006, 0: 0x00000000 0x00000000 0x01006040 0x00000000
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 80000007, 0: 0x00000000 0x00000000 0x00000000 0x00000100
2025-03-24T01:30:29.720Z In(05) vmx hostCPUID level 80000008, 0: 0x00003027 0x00000000 0x00000000 0x00000000
2025-03-24T01:30:29.720Z In(05) vmx CPUID differences from hostCPUID.
2025-03-24T01:30:29.720Z In(05) vmx Physical APIC IDs: 0-3
2025-03-24T01:30:29.720Z In(05) vmx Physical X2APIC IDs: 0-3
2025-03-24T01:30:29.720Z In(05) vmx Common: MSR       0x3a =            0x60005
2025-03-24T01:30:29.720Z In(05) vmx Common: MSR      0x480 =   0xda050000000013
2025-03-24T01:30:29.720Z In(05) vmx Common: MSR      0x481 =       0xff00000016
2025-03-24T01:30:29.720Z In(05) vmx Common: MSR      0x482 = 0xfff9fffe0401e172
2025-03-24T01:30:29.720Z In(05) vmx Common: MSR      0x483 =  0x37fffff00036dff
2025-03-24T01:30:29.720Z In(05) vmx Common: MSR      0x484 =    0x6ffff000011ff
2025-03-24T01:30:29.720Z In(05) vmx Common: MSR      0x485 =         0x7004c1e7
2025-03-24T01:30:29.720Z In(05) vmx Common: MSR      0x486 =         0x80000021
2025-03-24T01:30:29.720Z In(05) vmx Common: MSR      0x487 =         0xffffffff
2025-03-24T01:30:29.720Z In(05) vmx Common: MSR      0x488 =             0x2000
2025-03-24T01:30:29.720Z In(05) vmx Common: MSR      0x489 =           0x772fff
2025-03-24T01:30:29.720Z In(05) vmx Common: MSR      0x48a =               0x2e
2025-03-24T01:30:29.720Z In(05) vmx Common: MSR      0x48b = 0x335fbfff00000000
2025-03-24T01:30:29.720Z In(05) vmx Common: MSR      0x48c =      0xf0106734141
2025-03-24T01:30:29.720Z In(05) vmx Common: MSR      0x48d =       0xff00000016
2025-03-24T01:30:29.720Z In(05) vmx Common: MSR      0x48e = 0xfff9fffe04006172
2025-03-24T01:30:29.720Z In(05) vmx Common: MSR      0x48f =  0x37fffff00036dfb
2025-03-24T01:30:29.720Z In(05) vmx Common: MSR      0x490 =    0x6ffff000011fb
2025-03-24T01:30:29.720Z In(05) vmx Common: MSR      0x491 =                0x1
2025-03-24T01:30:29.720Z In(05) vmx Common: MSR      0x492 =                  0
2025-03-24T01:30:29.720Z In(05) vmx Common: MSR 0xc0010114 =                  0
2025-03-24T01:30:29.720Z In(05) vmx Common: MSR       0xce =         0x80000000
2025-03-24T01:30:29.720Z In(05) vmx Common: MSR      0x10a =               0x2b
2025-03-24T01:30:29.720Z In(05) vmx Common: MSR      0x122 =                  0
2025-03-24T01:30:29.720Z In(05) vmx VMMon_GetkHzEstimate: Calculated 1190389 kHz
2025-03-24T01:30:29.720Z In(05) vmx TSC Hz estimates: vmmon 1190389000, remembered 0, osReported 1190000000. Using 1190389000 Hz.
2025-03-24T01:30:29.720Z In(05) vmx TSC first measured delta 79
2025-03-24T01:30:29.720Z In(05) vmx TSC min delta 60
2025-03-24T01:30:29.720Z In(05) vmx PTSC: RefClockToPTSC 0 @ 10000000Hz -> 0 @ 1190389000Hz
2025-03-24T01:30:29.720Z In(05) vmx PTSC: RefClockToPTSC ((x * 3994282675) >> 25) + -81926555099163
2025-03-24T01:30:29.720Z In(05) vmx PTSC: tscOffset -81970679330177
2025-03-24T01:30:29.720Z In(05) vmx PTSC: using TSC
2025-03-24T01:30:29.720Z In(05) vmx PTSC: hardware TSCs are synchronized.
2025-03-24T01:30:29.720Z In(05) vmx PTSC: hardware TSCs may have been adjusted by the host.
2025-03-24T01:30:29.720Z In(05) vmx PTSC: current PTSC=37202600987
2025-03-24T01:30:29.728Z In(05) vmx WQPoolAllocPoll : pollIx = 3, signalHandle = 1128
2025-03-24T01:30:29.769Z In(05) vmx ConfigCheck: No rules file found. Checks are disabled.
2025-03-24T01:30:29.769Z In(05) vmx changing directory to C:\Users\<USER>\Desktop\Kali 2024 x64 Customized by zSecurity v1.2.vmwarevm\.
2025-03-24T01:30:29.769Z In(05) vmx Config file: C:\Users\<USER>\Desktop\Kali 2024 x64 Customized by zSecurity v1.2.vmwarevm\Kali 2024 x64 Customized by zSecurity v1.2.vmx
2025-03-24T01:30:29.769Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=1, newAppState=1875, success=1 additionalError=0
2025-03-24T01:30:29.769Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=2, newAppState=1878, success=1 additionalError=0
2025-03-24T01:30:29.772Z In(05) vmx LogRotation: Rotating to a new log file (keepOld 3) took 0.001602 seconds.
2025-03-24T01:30:29.784Z No(00) vmx LogVMXReplace: Successful switching from temporary to permanent log file took 0.013259 seconds.
2025-03-24T01:30:29.805Z No(00) vmx ConfigDB: Setting ide1:0.fileName = "auto detect"
2025-03-24T01:30:29.807Z Wa(03) vmx PowerOn
2025-03-24T01:30:29.807Z In(05) vmx VMX_PowerOn: VMX build 23775571, UI build 23775571
2025-03-24T01:30:29.807Z In(05) vmx HostWin32: WIN32 NUMA node 0, CPU mask 0x000000000000000f
2025-03-24T01:30:29.814Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=0, newAppState=1871, success=1 additionalError=0
2025-03-24T01:30:29.817Z In(05) vmx VMXSTATS: Successfully created stats file 'Kali 2024 x64 Customized by zSecurity v1.2.scoreboard'
2025-03-24T01:30:29.817Z In(05) vmx VMXSTATS: Update Product Information: VMware Workstation	17.5.2	build-23775571	Release  TotalBlockSize: 56
2025-03-24T01:30:29.819Z In(05) vmx HOST Windows version 10.0, build 26100, platform 2, ""
2025-03-24T01:30:29.819Z In(05) vmx DICT --- GLOBAL SETTINGS C:\ProgramData\VMware\VMware Workstation\settings.ini
2025-03-24T01:30:29.819Z In(05) vmx DICT          printers.enabled = "FALSE"
2025-03-24T01:30:29.819Z In(05) vmx DICT --- NON PERSISTENT (null)
2025-03-24T01:30:29.819Z In(05) vmx DICT --- USER PREFERENCES C:\Users\<USER>\AppData\Roaming\VMware\preferences.ini
2025-03-24T01:30:29.819Z In(05) vmx DICT pref.keyboardAndMouse.vmHotKey.enabled = "FALSE"
2025-03-24T01:30:29.819Z In(05) vmx DICT pref.keyboardAndMouse.vmHotKey.count = "0"
2025-03-24T01:30:29.819Z In(05) vmx DICT pref.wspro.firstRunDismissedVersion = "17.5.2"
2025-03-24T01:30:29.819Z In(05) vmx DICT pref.updatesVersionIgnore.numItems = "2"
2025-03-24T01:30:29.819Z In(05) vmx DICT pref.updatesVersionIgnore0.key = <not printed>
2025-03-24T01:30:29.819Z In(05) vmx DICT pref.updatesVersionIgnore0.value = "35f211f8-915f-4000-8d14-a379e4990924"
2025-03-24T01:30:29.819Z In(05) vmx DICT   pref.lastUpdateCheckSec = "1742778428"
2025-03-24T01:30:29.819Z In(05) vmx DICT pref.ws.session.window.count = "1"
2025-03-24T01:30:29.819Z In(05) vmx DICT pref.ws.session.window0.tab.count = "2"
2025-03-24T01:30:29.819Z In(05) vmx DICT pref.ws.session.window0.tab0.cnxType = "vmdb"
2025-03-24T01:30:29.819Z In(05) vmx DICT pref.ws.session.window0.tab1.cnxType = "vmdb"
2025-03-24T01:30:29.819Z In(05) vmx DICT pref.ws.session.window0.sidebar = "TRUE"
2025-03-24T01:30:29.819Z In(05) vmx DICT pref.ws.session.window0.sidebar.width = "200"
2025-03-24T01:30:29.819Z In(05) vmx DICT pref.ws.session.window0.statusBar = "TRUE"
2025-03-24T01:30:29.819Z In(05) vmx DICT pref.ws.session.window0.tabs = "TRUE"
2025-03-24T01:30:29.819Z In(05) vmx DICT pref.ws.session.window0.thumbnailBar = "FALSE"
2025-03-24T01:30:29.819Z In(05) vmx DICT pref.ws.session.window0.thumbnailBar.size = "100"
2025-03-24T01:30:29.819Z In(05) vmx DICT pref.ws.session.window0.thumbnailBar.view = "opened-vms"
2025-03-24T01:30:29.819Z In(05) vmx DICT pref.ws.session.window0.placement.left = "459"
2025-03-24T01:30:29.819Z In(05) vmx DICT pref.ws.session.window0.placement.top = "272"
2025-03-24T01:30:29.819Z In(05) vmx DICT pref.ws.session.window0.placement.right = "1383"
2025-03-24T01:30:29.819Z In(05) vmx DICT pref.ws.session.window0.placement.bottom = "883"
2025-03-24T01:30:29.819Z In(05) vmx DICT pref.ws.session.window0.maximized = "TRUE"
2025-03-24T01:30:29.819Z In(05) vmx DICT  pref.fullscreen.autohide = "TRUE"
2025-03-24T01:30:29.819Z In(05) vmx DICT pref.ws.session.window0.tab2.cnxType = "vmdb"
2025-03-24T01:30:29.819Z In(05) vmx DICT  pref.sharedFolder.maxNum = "1"
2025-03-24T01:30:29.819Z In(05) vmx DICT pref.sharedFolder0.vmPath = "/vm/#3babcb943734f8d6/"
2025-03-24T01:30:29.819Z In(05) vmx DICT pref.sharedFolder0.guestName = "Shared"
2025-03-24T01:30:29.819Z In(05) vmx DICT pref.sharedFolder0.hostPath = "/Volumes/Data/Shared"
2025-03-24T01:30:29.819Z In(05) vmx DICT pref.sharedFolder0.enabled = "FALSE"
2025-03-24T01:30:29.819Z In(05) vmx DICT  hint.vmui.showNewUSBDevs = "FALSE"
2025-03-24T01:30:29.819Z In(05) vmx DICT             hints.hideAll = "FALSE"
2025-03-24T01:30:29.819Z In(05) vmx DICT pref.updatesVersionIgnore1.key = <not printed>
2025-03-24T01:30:29.819Z In(05) vmx DICT pref.updatesVersionIgnore1.value = "bb61d294-c7fd-4b93-bdb3-48a9b5b74f44"
2025-03-24T01:30:29.819Z In(05) vmx DICT         vmWizard.guestKey = "debian11-64"
2025-03-24T01:30:29.819Z In(05) vmx DICT vmWizard.installMediaType = "later"
2025-03-24T01:30:29.819Z In(05) vmx DICT pref.ws.session.window0.tab3.cnxType = "vmdb"
2025-03-24T01:30:29.819Z In(05) vmx DICT pref.ws.session.window0.tab0.dest = ""
2025-03-24T01:30:29.819Z In(05) vmx DICT pref.ws.session.window0.tab0.file = ""
2025-03-24T01:30:29.819Z In(05) vmx DICT pref.ws.session.window0.tab0.type = "home"
2025-03-24T01:30:29.819Z In(05) vmx DICT pref.ws.session.window0.tab0.focused = "FALSE"
2025-03-24T01:30:29.819Z In(05) vmx DICT pref.ws.session.window0.tab1.dest = ""
2025-03-24T01:30:29.819Z In(05) vmx DICT pref.ws.session.window0.tab1.file = "C:\Users\<USER>\Desktop\PT\Debian 11.x 64-bit.vmx"
2025-03-24T01:30:29.819Z In(05) vmx DICT pref.ws.session.window0.tab1.type = "vm"
2025-03-24T01:30:29.819Z In(05) vmx DICT pref.ws.session.window0.tab1.focused = "TRUE"
2025-03-24T01:30:29.819Z In(05) vmx DICT --- USER DEFAULTS C:\Users\<USER>\AppData\Roaming\VMware\config.ini
2025-03-24T01:30:29.819Z In(05) vmx DICT --- HOST DEFAULTS C:\ProgramData\VMware\VMware Workstation\config.ini
2025-03-24T01:30:29.819Z In(05) vmx DICT         authd.client.port = "902"
2025-03-24T01:30:29.819Z In(05) vmx DICT           authd.proxy.nfc = "vmware-hostd:ha-nfc"
2025-03-24T01:30:29.819Z In(05) vmx DICT installerDefaults.autoSoftwareUpdateEnabled = "yes"
2025-03-24T01:30:29.819Z In(05) vmx DICT installerDefaults.autoSoftwareUpdateEnabled.epoch = "5203"
2025-03-24T01:30:29.819Z In(05) vmx DICT installerDefaults.componentDownloadEnabled = "yes"
2025-03-24T01:30:29.819Z In(05) vmx DICT installerDefaults.dataCollectionEnabled = "yes"
2025-03-24T01:30:29.819Z In(05) vmx DICT installerDefaults.dataCollectionEnabled.epoch = "5203"
2025-03-24T01:30:29.819Z In(05) vmx DICT --- SITE DEFAULTS C:\ProgramData\VMware\VMware Workstation\config.ini
2025-03-24T01:30:29.819Z In(05) vmx DICT         authd.client.port = "902"
2025-03-24T01:30:29.819Z In(05) vmx DICT           authd.proxy.nfc = "vmware-hostd:ha-nfc"
2025-03-24T01:30:29.819Z In(05) vmx DICT installerDefaults.autoSoftwareUpdateEnabled = "yes"
2025-03-24T01:30:29.819Z In(05) vmx DICT installerDefaults.autoSoftwareUpdateEnabled.epoch = "5203"
2025-03-24T01:30:29.819Z In(05) vmx DICT installerDefaults.componentDownloadEnabled = "yes"
2025-03-24T01:30:29.819Z In(05) vmx DICT installerDefaults.dataCollectionEnabled = "yes"
2025-03-24T01:30:29.819Z In(05) vmx DICT installerDefaults.dataCollectionEnabled.epoch = "5203"
2025-03-24T01:30:29.819Z In(05) vmx DICT --- NONPERSISTENT
2025-03-24T01:30:29.819Z In(05) vmx DICT            vmx.stdio.keep = "TRUE"
2025-03-24T01:30:29.819Z In(05) vmx DICT             gui.available = "TRUE"
2025-03-24T01:30:29.819Z In(05) vmx DICT --- COMMAND LINE
2025-03-24T01:30:29.819Z In(05) vmx DICT            vmx.stdio.keep = "TRUE"
2025-03-24T01:30:29.819Z In(05) vmx DICT             gui.available = "TRUE"
2025-03-24T01:30:29.819Z In(05) vmx DICT --- RECORDING
2025-03-24T01:30:29.819Z In(05) vmx DICT            vmx.stdio.keep = "TRUE"
2025-03-24T01:30:29.819Z In(05) vmx DICT             gui.available = "TRUE"
2025-03-24T01:30:29.819Z In(05) vmx DICT --- CONFIGURATION C:\Users\<USER>\Desktop\Kali 2024 x64 Customized by zSecurity v1.2.vmwarevm\Kali 2024 x64 Customized by zSecurity v1.2.vmx 
2025-03-24T01:30:29.819Z In(05) vmx DICT            config.version = "8"
2025-03-24T01:30:29.819Z In(05) vmx DICT         virtualHW.version = "21"
2025-03-24T01:30:29.819Z In(05) vmx DICT        pciBridge0.present = "TRUE"
2025-03-24T01:30:29.819Z In(05) vmx DICT        pciBridge4.present = "TRUE"
2025-03-24T01:30:29.819Z In(05) vmx DICT     pciBridge4.virtualDev = "pcieRootPort"
2025-03-24T01:30:29.819Z In(05) vmx DICT      pciBridge4.functions = "8"
2025-03-24T01:30:29.819Z In(05) vmx DICT        pciBridge5.present = "TRUE"
2025-03-24T01:30:29.819Z In(05) vmx DICT     pciBridge5.virtualDev = "pcieRootPort"
2025-03-24T01:30:29.819Z In(05) vmx DICT      pciBridge5.functions = "8"
2025-03-24T01:30:29.819Z In(05) vmx DICT        pciBridge6.present = "TRUE"
2025-03-24T01:30:29.819Z In(05) vmx DICT     pciBridge6.virtualDev = "pcieRootPort"
2025-03-24T01:30:29.819Z In(05) vmx DICT      pciBridge6.functions = "8"
2025-03-24T01:30:29.819Z In(05) vmx DICT        pciBridge7.present = "TRUE"
2025-03-24T01:30:29.819Z In(05) vmx DICT     pciBridge7.virtualDev = "pcieRootPort"
2025-03-24T01:30:29.819Z In(05) vmx DICT      pciBridge7.functions = "8"
2025-03-24T01:30:29.819Z In(05) vmx DICT             vmci0.present = "TRUE"
2025-03-24T01:30:29.819Z In(05) vmx DICT             hpet0.present = "TRUE"
2025-03-24T01:30:29.819Z In(05) vmx DICT                     nvram = "Kali 2024 x64 Customized by zSecurity v1.2.nvram"
2025-03-24T01:30:29.819Z In(05) vmx DICT virtualHW.productCompatibility = "hosted"
2025-03-24T01:30:29.819Z In(05) vmx DICT        powerType.powerOff = "soft"
2025-03-24T01:30:29.819Z In(05) vmx DICT         powerType.powerOn = "soft"
2025-03-24T01:30:29.819Z In(05) vmx DICT         powerType.suspend = "soft"
2025-03-24T01:30:29.819Z In(05) vmx DICT           powerType.reset = "soft"
2025-03-24T01:30:29.819Z In(05) vmx DICT               displayName = "Kali 2024 x64 Customized by zSecurity v1.2"
2025-03-24T01:30:29.819Z In(05) vmx DICT usb.vbluetooth.startConnected = "TRUE"
2025-03-24T01:30:29.819Z In(05) vmx DICT                   guestOS = "debian12-64"
2025-03-24T01:30:29.819Z In(05) vmx DICT            tools.syncTime = "TRUE"
2025-03-24T01:30:29.819Z In(05) vmx DICT      tools.upgrade.policy = "upgradeAtPowerCycle"
2025-03-24T01:30:29.819Z In(05) vmx DICT          sound.autoDetect = "TRUE"
2025-03-24T01:30:29.819Z In(05) vmx DICT            sound.fileName = "-1"
2025-03-24T01:30:29.819Z In(05) vmx DICT             sound.present = "TRUE"
2025-03-24T01:30:29.819Z In(05) vmx DICT                   memsize = "8192"
2025-03-24T01:30:29.819Z In(05) vmx DICT          scsi0.virtualDev = "lsilogic"
2025-03-24T01:30:29.819Z In(05) vmx DICT             scsi0.present = "TRUE"
2025-03-24T01:30:29.819Z In(05) vmx DICT          scsi0:0.fileName = "disk-000001.vmdk"
2025-03-24T01:30:29.819Z In(05) vmx DICT         ide1:0.deviceType = "cdrom-raw"
2025-03-24T01:30:29.819Z In(05) vmx DICT           ide1:0.fileName = "auto detect"
2025-03-24T01:30:29.819Z In(05) vmx DICT            ide1:0.present = "TRUE"
2025-03-24T01:30:29.819Z In(05) vmx DICT               usb.present = "TRUE"
2025-03-24T01:30:29.819Z In(05) vmx DICT              ehci.present = "TRUE"
2025-03-24T01:30:29.819Z In(05) vmx DICT  ethernet0.connectionType = "nat"
2025-03-24T01:30:29.819Z In(05) vmx DICT     ethernet0.addressType = "generated"
2025-03-24T01:30:29.819Z In(05) vmx DICT ethernet0.linkStatePropagation.enable = "TRUE"
2025-03-24T01:30:29.819Z In(05) vmx DICT         ethernet0.present = "TRUE"
2025-03-24T01:30:29.819Z In(05) vmx DICT        extendedConfigFile = "Kali 2024 x64 Customized by zSecurity v1.2.vmxf"
2025-03-24T01:30:29.819Z In(05) vmx DICT                  numvcpus = "2"
2025-03-24T01:30:29.819Z In(05) vmx DICT gui.perVMWindowAutofitMode = "resize"
2025-03-24T01:30:29.819Z In(05) vmx DICT gui.perVMFullscreenAutofitMode = "resize"
2025-03-24T01:30:29.819Z In(05) vmx DICT           scsi0:0.present = "TRUE"
2025-03-24T01:30:29.819Z In(05) vmx DICT      numa.autosize.cookie = "20012"
2025-03-24T01:30:29.819Z In(05) vmx DICT numa.autosize.vcpu.maxPerVirtualNode = "2"
2025-03-24T01:30:29.819Z In(05) vmx DICT                 uuid.bios = "56 4d 40 f8 4f 80 56 23-17 f1 7f d0 7c 71 5a 8c"
2025-03-24T01:30:29.819Z In(05) vmx DICT             uuid.location = "56 4d 40 f8 4f 80 56 23-17 f1 7f d0 7c 71 5a 8c"
2025-03-24T01:30:29.819Z In(05) vmx DICT              scsi0:0.redo = ""
2025-03-24T01:30:29.819Z In(05) vmx DICT  pciBridge0.pciSlotNumber = "17"
2025-03-24T01:30:29.819Z In(05) vmx DICT  pciBridge4.pciSlotNumber = "21"
2025-03-24T01:30:29.819Z In(05) vmx DICT  pciBridge5.pciSlotNumber = "22"
2025-03-24T01:30:29.819Z In(05) vmx DICT  pciBridge6.pciSlotNumber = "23"
2025-03-24T01:30:29.819Z In(05) vmx DICT  pciBridge7.pciSlotNumber = "24"
2025-03-24T01:30:29.819Z In(05) vmx DICT       scsi0.pciSlotNumber = "16"
2025-03-24T01:30:29.819Z In(05) vmx DICT         usb.pciSlotNumber = "32"
2025-03-24T01:30:29.819Z In(05) vmx DICT   ethernet0.pciSlotNumber = "33"
2025-03-24T01:30:29.819Z In(05) vmx DICT       sound.pciSlotNumber = "34"
2025-03-24T01:30:29.819Z In(05) vmx DICT        ehci.pciSlotNumber = "35"
2025-03-24T01:30:29.819Z In(05) vmx DICT       vmci0.pciSlotNumber = "36"
2025-03-24T01:30:29.819Z In(05) vmx DICT             svga.vramSize = "268435456"
2025-03-24T01:30:29.819Z In(05) vmx DICT  vmotion.checkpointFBSize = "4194304"
2025-03-24T01:30:29.819Z In(05) vmx DICT vmotion.checkpointSVGAPrimarySize = "268435456"
2025-03-24T01:30:29.819Z In(05) vmx DICT   vmotion.svga.mobMaxSize = "268435456"
2025-03-24T01:30:29.819Z In(05) vmx DICT vmotion.svga.graphicsMemoryKB = "262144"
2025-03-24T01:30:29.819Z In(05) vmx DICT ethernet0.generatedAddress = "00:0c:29:71:5a:8c"
2025-03-24T01:30:29.819Z In(05) vmx DICT ethernet0.generatedAddressOffset = "0"
2025-03-24T01:30:29.819Z In(05) vmx DICT                  vmci0.id = "-586922912"
2025-03-24T01:30:29.819Z In(05) vmx DICT    monitor.phys_bits_used = "45"
2025-03-24T01:30:29.819Z In(05) vmx DICT             cleanShutdown = "TRUE"
2025-03-24T01:30:29.819Z In(05) vmx DICT              softPowerOff = "FALSE"
2025-03-24T01:30:29.819Z In(05) vmx DICT               usb:1.speed = "2"
2025-03-24T01:30:29.819Z In(05) vmx DICT             usb:1.present = "TRUE"
2025-03-24T01:30:29.819Z In(05) vmx DICT          usb:1.deviceType = "hub"
2025-03-24T01:30:29.819Z In(05) vmx DICT                usb:1.port = "1"
2025-03-24T01:30:29.819Z In(05) vmx DICT              usb:1.parent = "-1"
2025-03-24T01:30:29.819Z In(05) vmx DICT svga.guestBackedPrimaryAware = "TRUE"
2025-03-24T01:30:29.819Z In(05) vmx DICT     guestOS.detailed.data = "architecture='X86' bitness='64' distroAddlVersion='2024.3' distroName='Kali GNU/Linux' distroVersion='2024.3' familyName='Linux' kernelVersion='6.8.11-amd64' prettyName='Kali GNU/Linux Rolling'"
2025-03-24T01:30:29.819Z In(05) vmx DICT toolsInstallManager.updateCounter = "1"
2025-03-24T01:30:29.819Z In(05) vmx DICT isolation.tools.hgfs.disable = "TRUE"
2025-03-24T01:30:29.819Z In(05) vmx DICT         hgfs.mapRootShare = "TRUE"
2025-03-24T01:30:29.819Z In(05) vmx DICT        hgfs.linkRootShare = "TRUE"
2025-03-24T01:30:29.819Z In(05) vmx DICT     sharedFolder0.present = "TRUE"
2025-03-24T01:30:29.819Z In(05) vmx DICT     sharedFolder0.enabled = "TRUE"
2025-03-24T01:30:29.819Z In(05) vmx DICT  sharedFolder0.readAccess = "TRUE"
2025-03-24T01:30:29.819Z In(05) vmx DICT sharedFolder0.writeAccess = "TRUE"
2025-03-24T01:30:29.819Z In(05) vmx DICT    sharedFolder0.hostPath = "/Volumes/Data/Shared"
2025-03-24T01:30:29.819Z In(05) vmx DICT   sharedFolder0.guestName = "Shared"
2025-03-24T01:30:29.819Z In(05) vmx DICT  sharedFolder0.expiration = "never"
2025-03-24T01:30:29.819Z In(05) vmx DICT       sharedFolder.maxNum = "1"
2025-03-24T01:30:29.819Z In(05) vmx DICT     ide1:0.startConnected = "FALSE"
2025-03-24T01:30:29.819Z In(05) vmx DICT         ide1:0.autodetect = "TRUE"
2025-03-24T01:30:29.819Z In(05) vmx DICT           floppy0.present = "FALSE"
2025-03-24T01:30:29.819Z In(05) vmx DICT         vmxstats.filename = "Kali 2024 x64 Customized by zSecurity v1.2.scoreboard"
2025-03-24T01:30:29.819Z In(05) vmx DICT tools.capability.verifiedSamlToken = "TRUE"
2025-03-24T01:30:29.819Z In(05) vmx DICT   guestInfo.detailed.data = <not printed>
2025-03-24T01:30:29.819Z In(05) vmx DICT cpuid.coresPerSocket.cookie = "2"
2025-03-24T01:30:29.819Z In(05) vmx DICT      usb.generic.allowHID = "TRUE"
2025-03-24T01:30:29.819Z In(05) vmx DICT          usb_xhci.present = "TRUE"
2025-03-24T01:30:29.819Z In(05) vmx DICT   gui.lastPoweredViewMode = "fullscreen"
2025-03-24T01:30:29.819Z In(05) vmx DICT    usb_xhci.pciSlotNumber = "160"
2025-03-24T01:30:29.819Z In(05) vmx DICT        checkpoint.vmState = "Kali 2024 x64 Customized by zSecurity v1.2-f65b28b0.vmss"
2025-03-24T01:30:29.819Z In(05) vmx DICT      gui.stretchGuestMode = "fullfill"
2025-03-24T01:30:29.819Z In(05) vmx DICT       tools.remindInstall = "FALSE"
2025-03-24T01:30:29.819Z In(05) vmx DICT        usb_xhci:4.present = "TRUE"
2025-03-24T01:30:29.819Z In(05) vmx DICT     usb_xhci:4.deviceType = "hid"
2025-03-24T01:30:29.819Z In(05) vmx DICT           usb_xhci:4.port = "4"
2025-03-24T01:30:29.819Z In(05) vmx DICT         usb_xhci:4.parent = "-1"
2025-03-24T01:30:29.819Z In(05) vmx DICT          usb_xhci:6.speed = "2"
2025-03-24T01:30:29.819Z In(05) vmx DICT        usb_xhci:6.present = "TRUE"
2025-03-24T01:30:29.819Z In(05) vmx DICT     usb_xhci:6.deviceType = "hub"
2025-03-24T01:30:29.819Z In(05) vmx DICT           usb_xhci:6.port = "6"
2025-03-24T01:30:29.819Z In(05) vmx DICT         usb_xhci:6.parent = "-1"
2025-03-24T01:30:29.819Z In(05) vmx DICT          usb_xhci:7.speed = "4"
2025-03-24T01:30:29.819Z In(05) vmx DICT        usb_xhci:7.present = "TRUE"
2025-03-24T01:30:29.819Z In(05) vmx DICT     usb_xhci:7.deviceType = "hub"
2025-03-24T01:30:29.819Z In(05) vmx DICT           usb_xhci:7.port = "7"
2025-03-24T01:30:29.819Z In(05) vmx DICT         usb_xhci:7.parent = "-1"
2025-03-24T01:30:29.819Z In(05) vmx DICT --- USER DEFAULTS C:\Users\<USER>\AppData\Roaming\VMware\config.ini 
2025-03-24T01:30:29.819Z In(05) vmx DICT --- HOST DEFAULTS C:\ProgramData\VMware\VMware Workstation\config.ini 
2025-03-24T01:30:29.819Z In(05) vmx DICT         authd.client.port = "902"
2025-03-24T01:30:29.819Z In(05) vmx DICT           authd.proxy.nfc = "vmware-hostd:ha-nfc"
2025-03-24T01:30:29.819Z In(05) vmx DICT installerDefaults.autoSoftwareUpdateEnabled = "yes"
2025-03-24T01:30:29.819Z In(05) vmx DICT installerDefaults.autoSoftwareUpdateEnabled.epoch = "5203"
2025-03-24T01:30:29.819Z In(05) vmx DICT installerDefaults.componentDownloadEnabled = "yes"
2025-03-24T01:30:29.819Z In(05) vmx DICT installerDefaults.dataCollectionEnabled = "yes"
2025-03-24T01:30:29.819Z In(05) vmx DICT installerDefaults.dataCollectionEnabled.epoch = "5203"
2025-03-24T01:30:29.819Z In(05) vmx DICT --- SITE DEFAULTS C:\ProgramData\VMware\VMware Workstation\config.ini 
2025-03-24T01:30:29.819Z In(05) vmx DICT         authd.client.port = "902"
2025-03-24T01:30:29.819Z In(05) vmx DICT           authd.proxy.nfc = "vmware-hostd:ha-nfc"
2025-03-24T01:30:29.819Z In(05) vmx DICT installerDefaults.autoSoftwareUpdateEnabled = "yes"
2025-03-24T01:30:29.819Z In(05) vmx DICT installerDefaults.autoSoftwareUpdateEnabled.epoch = "5203"
2025-03-24T01:30:29.819Z In(05) vmx DICT installerDefaults.componentDownloadEnabled = "yes"
2025-03-24T01:30:29.819Z In(05) vmx DICT installerDefaults.dataCollectionEnabled = "yes"
2025-03-24T01:30:29.819Z In(05) vmx DICT installerDefaults.dataCollectionEnabled.epoch = "5203"
2025-03-24T01:30:29.819Z In(05) vmx DICT --- GLOBAL SETTINGS C:\ProgramData\VMware\VMware Workstation\settings.ini 
2025-03-24T01:30:29.819Z In(05) vmx DICT          printers.enabled = "FALSE"
2025-03-24T01:30:29.819Z In(05) vmx VMXSTATS: Registering 1 stats: vmx.diskLibDataVmdkOpenTime
2025-03-24T01:30:29.819Z In(05) vmx VMXSTATS: Registering 2 stats: vmx.diskLibDataVmdkOpenTime
2025-03-24T01:30:29.819Z In(05) vmx VMXSTATS: Registering 3 stats: vmx.diskLibDataVmdkGrowTime
2025-03-24T01:30:29.819Z In(05) vmx VMXSTATS: Registering 4 stats: vmx.diskLibDataVmdkGrowTime
2025-03-24T01:30:29.819Z In(05) vmx VMXSTATS: Registering 5 stats: vmx.diskLibDigestVmdkOpenTime
2025-03-24T01:30:29.819Z In(05) vmx VMXSTATS: Registering 6 stats: vmx.diskLibDigestVmdkOpenTime
2025-03-24T01:30:29.819Z In(05) vmx VMXSTATS: Registering 7 stats: vmx.diskLibDigestVmdkGrowTime
2025-03-24T01:30:29.819Z In(05) vmx VMXSTATS: Registering 8 stats: vmx.diskLibDigestVmdkGrowTime
2025-03-24T01:30:29.819Z In(05) vmx VMXSTATS: Registering 9 stats: vmx.diskLibDigestFileDataGrowTime
2025-03-24T01:30:29.819Z In(05) vmx VMXSTATS: Registering 10 stats: vmx.diskLibDigestFileDataGrowTime
2025-03-24T01:30:29.819Z In(05) vmx VMXSTATS: Registering 11 stats: vmx.digestLibOpenIntTime
2025-03-24T01:30:29.819Z In(05) vmx VMXSTATS: Registering 12 stats: vmx.digestLibOpenIntTime
2025-03-24T01:30:29.819Z In(05) vmx VMXSTATS: Registering 13 stats: vmx.diskLibDataVmdkCloseTime
2025-03-24T01:30:29.819Z In(05) vmx VMXSTATS: Registering 14 stats: vmx.diskLibDataVmdkCloseTime
2025-03-24T01:30:29.819Z In(05) vmx VMXSTATS: Registering 15 stats: vmx.diskLibDigestVmdkCloseTime
2025-03-24T01:30:29.819Z In(05) vmx VMXSTATS: Registering 16 stats: vmx.diskLibDigestVmdkCloseTime
2025-03-24T01:30:29.819Z In(05) vmx VMXSTATS: Registering 17 stats: vmx.diskLibVmdkCreateTime
2025-03-24T01:30:29.819Z In(05) vmx VMXSTATS: Registering 18 stats: vmx.diskLibVmdkCreateTime
2025-03-24T01:30:29.819Z In(05) vmx VMXSTATS: Registering 19 stats: vmx.diskLibChildVmdkCreateTime
2025-03-24T01:30:29.819Z In(05) vmx VMXSTATS: Registering 20 stats: vmx.diskLibChildVmdkCreateTime
2025-03-24T01:30:29.819Z In(05) vmx VMXSTATS: Registering 21 stats: vmx.snapshotCreateTime
2025-03-24T01:30:29.819Z In(05) vmx VMXSTATS: Registering 22 stats: vmx.snapshotCreateTime
2025-03-24T01:30:29.819Z In(05) vmx VMXSTATS: Registering 23 stats: vmx.snapshotCreateQuiescedTime
2025-03-24T01:30:29.819Z In(05) vmx VMXSTATS: Registering 24 stats: vmx.snapshotCreateQuiescedTime
2025-03-24T01:30:29.819Z In(05) vmx VMXSTATS: Registering 25 stats: vmx.snapshotCreateMemoryTime
2025-03-24T01:30:29.819Z In(05) vmx VMXSTATS: Registering 26 stats: vmx.snapshotCreateMemoryTime
2025-03-24T01:30:29.819Z In(05) vmx VMXSTATS: Registering 27 stats: vmx.snapshotDeleteTime
2025-03-24T01:30:29.819Z In(05) vmx VMXSTATS: Registering 28 stats: vmx.snapshotDeleteTime
2025-03-24T01:30:29.819Z In(05) vmx VMXSTATS: Registering 29 stats: vmx.snapshotConsolidateTime
2025-03-24T01:30:29.819Z In(05) vmx VMXSTATS: Registering 30 stats: vmx.snapshotConsolidateTime
2025-03-24T01:30:29.819Z In(05) vmx VMXSTATS: Registering 31 stats: vmx.checkpointStunTime
2025-03-24T01:30:29.819Z In(05) vmx VMXSTATS: Registering 32 stats: vmx.checkpointStunTime
2025-03-24T01:30:29.819Z In(05) vmx VMXSTATS: Registering 33 stats: vmx.setPolicyTime
2025-03-24T01:30:29.819Z In(05) vmx VMXSTATS: Registering 34 stats: vmx.setPolicyTime
2025-03-24T01:30:29.819Z In(05) vmx VMXSTATS: Registering 35 stats: vmx.filtlibApplyDiskConfigTime
2025-03-24T01:30:29.819Z In(05) vmx VMXSTATS: Registering 36 stats: vmx.filtlibApplyDiskConfigTime
2025-03-24T01:30:29.819Z In(05) vmx VMXSTATS: Registering 37 stats: vmx.diskLibGetInfoTime
2025-03-24T01:30:29.819Z In(05) vmx VMXSTATS: Registering 38 stats: vmx.diskLibGetInfoTime
2025-03-24T01:30:29.819Z In(05) vmx VMXSTATS: Registering 39 stats: vmx.diskLibDigestGetInfoTime
2025-03-24T01:30:29.819Z In(05) vmx VMXSTATS: Registering 40 stats: vmx.diskLibDigestGetInfoTime
2025-03-24T01:30:29.819Z In(05) vmx Powering on guestOS 'debian12-64' using the configuration for 'debian12-64'.
2025-03-24T01:30:29.821Z In(05) vmx ToolsISO: open of C:\Program Files (x86)\VMware\VMware Workstation\isoimages_manifest.txt.sig failed: Could not find the file
2025-03-24T01:30:29.821Z In(05) vmx ToolsISO: Unable to read signature file 'C:\Program Files (x86)\VMware\VMware Workstation\isoimages_manifest.txt.sig', ignoring.
2025-03-24T01:30:29.821Z In(05) vmx ToolsISO: Updated cached value for imageName to 'linux.iso'.
2025-03-24T01:30:29.821Z In(05) vmx ToolsISO: Selected Tools ISO 'linux.iso' for 'debian12-64' guest.
2025-03-24T01:30:29.823Z In(05) vmx DUMPER: Restoring checkpoint version 8.
2025-03-24T01:30:29.823Z In(05) vmx Checkpointed in VMware Workstation, 17.5.2, build-23775571, Windows Host
2025-03-24T01:30:29.823Z In(05) vmx Resuming virtual machine from C:\Users\<USER>\Desktop\Kali 2024 x64 Customized by zSecurity v1.2.vmwarevm\Kali 2024 x64 Customized by zSecurity v1.2-f65b28b0.vmss with 8192 MB of memory.
2025-03-24T01:30:29.823Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=1, newAppState=1873, success=1 additionalError=0
2025-03-24T01:30:29.823Z In(05) vmx DEVSWAP: GuestOS does not require LSI adapter swap.
2025-03-24T01:30:29.823Z In(05) vmx Monitor Mode: CPL0
2025-03-24T01:30:29.839Z In(05) vmx OvhdMem_PowerOn: initial admission: paged   674445 nonpaged     7729 anonymous    11823
2025-03-24T01:30:29.839Z In(05) vmx VMMEM: Initial Reservation: 2710MB (MainMem=8192MB)
2025-03-24T01:30:29.839Z In(05) vmx numa: Resuming from checkpoint using VPD = 2
2025-03-24T01:30:29.839Z In(05) vmx llc: maximum vcpus per LLC: 1
2025-03-24T01:30:29.839Z In(05) vmx llc: vLLC size: 2
2025-03-24T01:30:29.839Z In(05) vmx MemSched_PowerOn: balloon minGuestSize 209715 (80% of min required size 262144)
2025-03-24T01:30:29.839Z In(05) vmx MemSched: reserved mem (in MB) min 128 max 6200 recommended 6200
2025-03-24T01:30:29.839Z In(05) vmx MemSched: pg 674445 np 7729 anon 11823 mem 2097152
2025-03-24T01:30:29.839Z In(05) vmx MemSched: numvm 2 locked pages: num 10000 max 1570816
2025-03-24T01:30:29.839Z In(05) vmx MemSched: locked Page Limit: host 1689771 config 1587200
2025-03-24T01:30:29.839Z In(05) vmx MemSched: minmempct 50 minalloc 1096244 admitted 0
2025-03-24T01:30:29.839Z In(05) vmx Msg_Post: Error
2025-03-24T01:30:29.839Z In(05) vmx [msg.memsched.preNotEnoughMem] Not enough physical memory is available to power on this virtual machine with its configured settings.
2025-03-24T01:30:29.839Z In(05) vmx [msg.memsched.notEnoughMemNoVal2] To fix this problem, power off other virtual machines or adjust the additional memory settings to allow more virtual machine memory to be swapped.
2025-03-24T01:30:29.839Z In(05) vmx [msg.memsched.postNotEnoughMem] If you were able to power on this virtual machine on this host computer in the past, try rebooting the host computer. Rebooting may allow you to use slightly more host memory to run virtual machines.
2025-03-24T01:30:29.839Z In(05) vmx ----------------------------------------
2025-03-24T01:30:36.806Z In(05) vmx Module 'MemSched' power on failed.
2025-03-24T01:30:36.806Z In(05) vmx VMX_PowerOn: ModuleTable_PowerOn = 0
2025-03-24T01:30:36.809Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=1, newAppState=1875, success=1 additionalError=0
2025-03-24T01:30:36.809Z In(05) vmx VMXSTATS: Ready to cleanup and munmap 16A182A0000.
2025-03-24T01:30:36.811Z In(05) vmx 
2025-03-24T01:30:36.811Z In(05)+ vmx Power on failure messages: Not enough physical memory is available to power on this virtual machine with its configured settings.
2025-03-24T01:30:36.811Z In(05)+ vmx To fix this problem, power off other virtual machines or adjust the additional memory settings to allow more virtual machine memory to be swapped.
2025-03-24T01:30:36.811Z In(05)+ vmx If you were able to power on this virtual machine on this host computer in the past, try rebooting the host computer. Rebooting may allow you to use slightly more host memory to run virtual machines.
2025-03-24T01:30:36.811Z In(05)+ vmx Failed to start the virtual machine.
2025-03-24T01:30:36.811Z In(05)+ vmx 
2025-03-24T01:30:36.811Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=0, newAppState=1870, success=1 additionalError=0
2025-03-24T01:30:36.811Z In(05) vmx Transitioned vmx/execState/val to suspended
2025-03-24T01:30:36.811Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=0, newAppState=1870, success=0 additionalError=0
2025-03-24T01:30:36.813Z In(05) vmx Vix: [mainDispatch.c:4250]: Error VIX_E_FAIL in VMAutomation_ReportPowerOpFinished(): Unknown error
2025-03-24T01:30:36.815Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=0, newAppState=1870, success=1 additionalError=0
2025-03-24T01:30:36.815Z In(05) vmx Transitioned vmx/execState/val to suspended
2025-03-24T01:30:36.815Z In(05) vmx WQPoolFreePoll : pollIx = 3, signalHandle = 1128
2025-03-24T01:30:36.815Z In(05) vmx Vix: [mainDispatch.c:817]: VMAutomation_LateShutdown()
2025-03-24T01:30:36.815Z In(05) vmx Vix: [mainDispatch.c:772]: VMAutomationCloseListenerSocket. Closing listener socket.
2025-03-24T01:30:36.815Z In(05) vmx Flushing VMX VMDB connections
2025-03-24T01:30:36.821Z In(05) vmx VmdbDbRemoveCnx: Removing Cnx from Db for '/db/connection/#1/'
2025-03-24T01:30:36.821Z In(05) vmx VmdbCnxDisconnect: Disconnect: closed pipe for pub cnx '/db/connection/#1/' (0)
2025-03-24T01:30:36.821Z In(05) vmx VigorTransport_ServerDestroy: server destroyed.
2025-03-24T01:30:36.821Z In(05) vmx WQPoolFreePoll : pollIx = 2, signalHandle = 856
2025-03-24T01:30:36.821Z In(05) vmx WQPoolFreePoll : pollIx = 1, signalHandle = 848
2025-03-24T01:30:36.822Z In(05) vmx VMX exit (0).
2025-03-24T01:30:36.822Z In(05) vmx OBJLIB-LIB: ObjLib cleanup done.
2025-03-24T01:30:36.822Z In(05) vmx AIOMGR-S : stat o=1 r=2 w=0 i=0 br=16384 bw=0
