2024-10-09T17:13:07.308Z In(05) vmx Log for VMware Workstation pid=22476 version=17.5.2 build=build-23775571 option=Release
2024-10-09T17:13:07.308Z In(05) vmx The host is x86_64.
2024-10-09T17:13:07.308Z In(05) vmx Host codepage=windows-1255 encoding=windows-1255
2024-10-09T17:13:07.308Z In(05) vmx Host is Windows 11 Pro, 64-bit (Build 22631.4169)
2024-10-09T17:13:07.308Z In(05) vmx Host offset from UTC is -02:00.
2024-10-09T17:13:07.287Z In(05) vmx VTHREAD 8964 "vmx"
2024-10-09T17:13:07.290Z In(05) vmx LOCALE windows-1255 -> NULL User=40d System=40d
2024-10-09T17:13:07.290Z In(05) vmx Msg_SetLocaleEx: HostLocale=windows-1255 UserLocale=NULL
2024-10-09T17:13:07.296Z In(05) vmx DictionaryLoad: Cannot open file "C:\Users\<USER>\AppData\Roaming\VMware\config.ini": The system cannot find the file specified.
2024-10-09T17:13:07.296Z In(05) vmx Msg_Reset:
2024-10-09T17:13:07.296Z In(05) vmx [msg.dictionary.load.openFailed] Cannot open file "C:\Users\<USER>\AppData\Roaming\VMware\config.ini": The system cannot find the file specified.
2024-10-09T17:13:07.296Z In(05) vmx ----------------------------------------
2024-10-09T17:13:07.296Z In(05) vmx ConfigDB: Failed to load C:\Users\<USER>\AppData\Roaming\VMware\config.ini
2024-10-09T17:13:07.297Z In(05) vmx Win32U_GetFileAttributes: GetFileAttributesExW("C:\Users\<USER>\Desktop\Metasploitable2-Linux\Metasploitable.vmpl", ...) failed, error: 2
2024-10-09T17:13:07.297Z In(05) vmx OBJLIB-LIB: Objlib initialized.
2024-10-09T17:13:07.299Z In(05) vmx DictionaryLoad: Cannot open file "C:\Users\<USER>\AppData\Roaming\VMware\config.ini": The system cannot find the file specified.
2024-10-09T17:13:07.299Z In(05) vmx [msg.dictionary.load.openFailed] Cannot open file "C:\Users\<USER>\AppData\Roaming\VMware\config.ini": The system cannot find the file specified.
2024-10-09T17:13:07.299Z In(05) vmx PREF Optional preferences file not found at C:\Users\<USER>\AppData\Roaming\VMware\config.ini. Using default values.
2024-10-09T17:13:07.302Z In(05) vmx SSL Error: error:80000002:system library::No such file or directory
2024-10-09T17:13:07.302Z In(05) vmx SSL Error: error:10000080:BIO routines::no such file
2024-10-09T17:13:07.302Z In(05) vmx SSL Error: error:07000072:configuration file routines::no such file
2024-10-09T17:13:07.302Z Wa(03) vmx SSLConfigLoad: Failed to load OpenSSL config file.
2024-10-09T17:13:07.304Z In(05) vmx lib/ssl: OpenSSL using default provider
2024-10-09T17:13:07.304Z In(05) vmx lib/ssl: Client usage
2024-10-09T17:13:07.304Z In(05) vmx lib/ssl: protocol list tls1.2
2024-10-09T17:13:07.304Z In(05) vmx lib/ssl: protocol min 0x303 max 0x303
2024-10-09T17:13:07.304Z In(05) vmx lib/ssl: protocol list tls1.2 (openssl flags 0x36000000)
2024-10-09T17:13:07.304Z In(05) vmx lib/ssl: cipher list ECDHE+AESGCM:RSA+AESGCM:ECDHE+AES:RSA+AES
2024-10-09T17:13:07.304Z In(05) vmx lib/ssl: cipher suites TLS_AES_128_GCM_SHA256:TLS_AES_256_GCM_SHA384
2024-10-09T17:13:07.304Z In(05) vmx lib/ssl: curves list prime256v1:secp384r1:secp521r1
2024-10-09T17:13:07.305Z In(05) vmx lib/ssl: Server usage
2024-10-09T17:13:07.305Z In(05) vmx lib/ssl: protocol list tls1.2
2024-10-09T17:13:07.305Z In(05) vmx lib/ssl: protocol min 0x303 max 0x303
2024-10-09T17:13:07.305Z In(05) vmx lib/ssl: protocol list tls1.2 (openssl flags 0x36000000)
2024-10-09T17:13:07.305Z In(05) vmx lib/ssl: cipher list ECDHE+AESGCM:RSA+AESGCM:ECDHE+AES:RSA+AES
2024-10-09T17:13:07.305Z In(05) vmx lib/ssl: cipher suites TLS_AES_128_GCM_SHA256:TLS_AES_256_GCM_SHA384
2024-10-09T17:13:07.305Z In(05) vmx lib/ssl: curves list prime256v1:secp384r1:secp521r1
2024-10-09T17:13:07.312Z In(05) vmx Hostname=DESKTOP-Q1DBRSH
2024-10-09T17:13:07.317Z In(05) vmx IP=fe80::f300:f556:1811:9be%16
2024-10-09T17:13:07.317Z In(05) vmx IP=fe80::a29d:2a26:193c:b90f%7
2024-10-09T17:13:07.317Z In(05) vmx IP=fe80::c36:873b:2a5f:d50d%4
2024-10-09T17:13:07.317Z In(05) vmx IP=************
2024-10-09T17:13:07.317Z In(05) vmx IP=************
2024-10-09T17:13:07.317Z In(05) vmx IP=**************
2024-10-09T17:13:07.352Z In(05) vmx System uptime 2405361456106 us
2024-10-09T17:13:07.352Z In(05) vmx Command line: "C:\Program Files (x86)\VMware\VMware Workstation\x64\vmware-vmx.exe" "-T" "querytoken" "-s" "vmx.stdio.keep=TRUE" "-#" "product=1;name=VMware Workstation;version=17.5.2;buildnumber=23775571;licensename=VMware Workstation;licenseversion=17.0;" "-@" "pipe=\\.\pipe\vmx987138fe9396be4e;msgs=ui" "C:\Users\<USER>\Desktop\Metasploitable2-Linux\Metasploitable.vmx"
2024-10-09T17:13:07.352Z In(05) vmx Msg_SetLocaleEx: HostLocale=windows-1255 UserLocale=NULL
2024-10-09T17:13:07.383Z In(05) vmx WQPoolAllocPoll : pollIx = 1, signalHandle = 800
2024-10-09T17:13:07.383Z In(05) vmx WQPoolAllocPoll : pollIx = 2, signalHandle = 792
2024-10-09T17:13:07.384Z In(05) vmx VigorTransport listening on fd 880
2024-10-09T17:13:07.384Z In(05) vmx Vigor_Init 1
2024-10-09T17:13:07.384Z In(05) vmx Connecting 'ui' to pipe '\\.\pipe\vmx987138fe9396be4e' with user '(null)'
2024-10-09T17:13:07.385Z In(05) vmx VMXVmdb: Local connection timeout: 60000 ms.
2024-10-09T17:13:07.493Z In(05) vmx VmdbAddConnection: cnxPath=/db/connection/#1/, cnxIx=1
2024-10-09T17:13:07.495Z In(05) vmx Vix: [mainDispatch.c:488]: VMAutomation: Initializing VMAutomation.
2024-10-09T17:13:07.496Z In(05) vmx Vix: [mainDispatch.c:740]: VMAutomationOpenListenerSocket() listening
2024-10-09T17:13:07.520Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=0, newAppState=1870, success=1 additionalError=0
2024-10-09T17:13:07.520Z In(05) vmx Transitioned vmx/execState/val to poweredOff
2024-10-09T17:13:07.520Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=1, newAppState=1873, success=1 additionalError=0
2024-10-09T17:13:07.520Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=2, newAppState=1877, success=1 additionalError=0
2024-10-09T17:13:07.520Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=3, newAppState=1881, success=1 additionalError=0
2024-10-09T17:13:07.587Z In(05) vmx IOPL_VBSRunning: VBS is set to 0
2024-10-09T17:13:07.589Z In(05) vmx IOCTL_VMX86_SET_MEMORY_PARAMS already set
2024-10-09T17:13:07.589Z In(05) vmx FeatureCompat: No EVC masks.
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID vendor: GenuineIntel
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID family: 0x6 model: 0x7e stepping: 0x5
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID codename: Ice Lake-U/Y
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID name: Intel(R) Core(TM) i3-1005G1 CPU @ 1.20GHz
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 00000000, 0: 0x0000001b 0x756e6547 0x6c65746e 0x49656e69
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 00000001, 0: 0x000706e5 0x00100800 0x7ffafbbf 0xbfebfbff
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 00000002, 0: 0x00feff01 0x000000f0 0x00000000 0x00000000
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 00000003, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 00000004, 0: 0x1c004121 0x02c0003f 0x0000003f 0x00000000
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 00000004, 1: 0x1c004122 0x01c0003f 0x0000003f 0x00000000
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 00000004, 2: 0x1c004143 0x01c0003f 0x000003ff 0x00000000
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 00000004, 3: 0x1c03c163 0x03c0003f 0x00000fff 0x00000006
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 00000004, 4: 0x00000000 0x00000000 0x00000000 0x00000000
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 00000005, 0: 0x00000040 0x00000040 0x00000003 0x11121020
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 00000006, 0: 0x0017aff7 0x00000002 0x00000009 0x00000000
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 00000007, 0: 0x00000000 0xf2bf27ef 0x40405f4e 0xbc000410
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 00000008, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 00000009, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 0000000a, 0: 0x08300805 0x00000000 0x0000000f 0x00008604
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 0000000b, 0: 0x00000001 0x00000002 0x00000100 0x00000000
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 0000000b, 1: 0x00000004 0x00000004 0x00000201 0x00000000
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 0000000b, 2: 0x00000000 0x00000000 0x00000002 0x00000000
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 0000000c, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 0000000d, 0: 0x000002e7 0x00000a80 0x00000a88 0x00000000
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 0000000d, 1: 0x0000000f 0x00000a00 0x00002100 0x00000000
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 0000000d, 2: 0x00000100 0x00000240 0x00000000 0x00000000
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 0000000d, 3: 0x00000000 0x00000000 0x00000000 0x00000000
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 0000000d, 4: 0x00000000 0x00000000 0x00000000 0x00000000
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 0000000d, 5: 0x00000040 0x00000440 0x00000000 0x00000000
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 0000000d, 6: 0x00000200 0x00000480 0x00000000 0x00000000
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 0000000d, 7: 0x00000400 0x00000680 0x00000000 0x00000000
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 0000000d, 8: 0x00000080 0x00000000 0x00000001 0x00000000
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 0000000d, 9: 0x00000008 0x00000a80 0x00000000 0x00000000
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 0000000d, a: 0x00000000 0x00000000 0x00000000 0x00000000
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 0000000d, b: 0x00000000 0x00000000 0x00000000 0x00000000
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 0000000d, c: 0x00000000 0x00000000 0x00000000 0x00000000
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 0000000d, d: 0x00000008 0x00000000 0x00000001 0x00000000
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 0000000e, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 0000000f, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 0000000f, 1: 0x00000000 0x00000000 0x00000000 0x00000000
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 00000010, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 00000010, 1: 0x00000000 0x00000000 0x00000000 0x00000000
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 00000010, 2: 0x00000000 0x00000000 0x00000000 0x00000000
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 00000010, 3: 0x00000000 0x00000000 0x00000000 0x00000000
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 00000011, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 00000012, 0: 0x00000063 0x00000001 0x00000000 0x00002f1f
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 00000012, 1: 0x000000b6 0x00000000 0x000002e7 0x00000000
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 00000012, 2: 0x50180001 0x00000000 0x0bc00001 0x00000000
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 00000012, 3: 0x00000000 0x00000000 0x00000000 0x00000000
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 00000013, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 00000014, 0: 0x00000001 0x0000000f 0x00000007 0x00000000
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 00000014, 1: 0x02490002 0x003f1fff 0x00000000 0x00000000
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 00000015, 0: 0x00000002 0x0000003e 0x0249f000 0x00000000
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 00000016, 0: 0x000004b0 0x00000d48 0x00000064 0x00000000
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 00000017, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 00000018, 0: 0x00000007 0x00000000 0x00000000 0x00000000
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 00000018, 1: 0x00000000 0x00080007 0x00000001 0x00004122
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 00000018, 2: 0x00000000 0x0010000f 0x00000001 0x00004125
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 00000018, 3: 0x00000000 0x00040001 0x00000010 0x00004024
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 00000018, 4: 0x00000000 0x00040006 0x00000008 0x00004024
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 00000018, 5: 0x00000000 0x00080008 0x00000001 0x00004124
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 00000018, 6: 0x00000000 0x00080007 0x00000080 0x00004043
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 00000018, 7: 0x00000000 0x00080009 0x00000080 0x00004043
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 00000019, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 0000001a, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 0000001b, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 80000000, 0: 0x80000008 0x00000000 0x00000000 0x00000000
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 80000001, 0: 0x00000000 0x00000000 0x00000121 0x2c100800
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 80000002, 0: 0x65746e49 0x2952286c 0x726f4320 0x4d542865
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 80000003, 0: 0x33692029 0x3030312d 0x20314735 0x20555043
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 80000004, 0: 0x2e312040 0x48473032 0x0000007a 0x00000000
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 80000005, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 80000006, 0: 0x00000000 0x00000000 0x01006040 0x00000000
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 80000007, 0: 0x00000000 0x00000000 0x00000000 0x00000100
2024-10-09T17:13:07.594Z In(05) vmx hostCPUID level 80000008, 0: 0x00003027 0x00000000 0x00000000 0x00000000
2024-10-09T17:13:07.594Z In(05) vmx CPUID differences from hostCPUID.
2024-10-09T17:13:07.594Z In(05) vmx Physical APIC IDs: 0-3
2024-10-09T17:13:07.594Z In(05) vmx Physical X2APIC IDs: 0-3
2024-10-09T17:13:07.594Z In(05) vmx Common: MSR       0x3a =            0x60005
2024-10-09T17:13:07.594Z In(05) vmx Common: MSR      0x480 =   0xda050000000013
2024-10-09T17:13:07.594Z In(05) vmx Common: MSR      0x481 =       0xff00000016
2024-10-09T17:13:07.594Z In(05) vmx Common: MSR      0x482 = 0xfff9fffe0401e172
2024-10-09T17:13:07.594Z In(05) vmx Common: MSR      0x483 =  0x37fffff00036dff
2024-10-09T17:13:07.594Z In(05) vmx Common: MSR      0x484 =    0x6ffff000011ff
2024-10-09T17:13:07.594Z In(05) vmx Common: MSR      0x485 =         0x7004c1e7
2024-10-09T17:13:07.594Z In(05) vmx Common: MSR      0x486 =         0x80000021
2024-10-09T17:13:07.594Z In(05) vmx Common: MSR      0x487 =         0xffffffff
2024-10-09T17:13:07.594Z In(05) vmx Common: MSR      0x488 =             0x2000
2024-10-09T17:13:07.594Z In(05) vmx Common: MSR      0x489 =           0x772fff
2024-10-09T17:13:07.595Z In(05) vmx Common: MSR      0x48a =               0x2e
2024-10-09T17:13:07.595Z In(05) vmx Common: MSR      0x48b = 0x335fbfff00000000
2024-10-09T17:13:07.595Z In(05) vmx Common: MSR      0x48c =      0xf0106734141
2024-10-09T17:13:07.595Z In(05) vmx Common: MSR      0x48d =       0xff00000016
2024-10-09T17:13:07.595Z In(05) vmx Common: MSR      0x48e = 0xfff9fffe04006172
2024-10-09T17:13:07.595Z In(05) vmx Common: MSR      0x48f =  0x37fffff00036dfb
2024-10-09T17:13:07.595Z In(05) vmx Common: MSR      0x490 =    0x6ffff000011fb
2024-10-09T17:13:07.595Z In(05) vmx Common: MSR      0x491 =                0x1
2024-10-09T17:13:07.595Z In(05) vmx Common: MSR      0x492 =                  0
2024-10-09T17:13:07.595Z In(05) vmx Common: MSR 0xc0010114 =                  0
2024-10-09T17:13:07.595Z In(05) vmx Common: MSR       0xce =         0x80000000
2024-10-09T17:13:07.595Z In(05) vmx Common: MSR      0x10a =               0x2b
2024-10-09T17:13:07.595Z In(05) vmx Common: MSR      0x122 =                  0
2024-10-09T17:13:07.595Z In(05) vmx VMMon_GetkHzEstimate: Calculated 1190389 kHz
2024-10-09T17:13:07.595Z In(05) vmx TSC Hz estimates: vmmon 1190389000, remembered 0, osReported 1190000000. Using 1190389000 Hz.
2024-10-09T17:13:07.595Z In(05) vmx TSC first measured delta 151
2024-10-09T17:13:07.595Z In(05) vmx TSC min delta 105
2024-10-09T17:13:07.595Z In(05) vmx PTSC: RefClockToPTSC 0 @ 10000000Hz -> 0 @ 1190389000Hz
2024-10-09T17:13:07.595Z In(05) vmx PTSC: RefClockToPTSC ((x * 3994282675) >> 25) + -169106334140036
2024-10-09T17:13:07.595Z In(05) vmx PTSC: tscOffset -169219003023999
2024-10-09T17:13:07.595Z In(05) vmx PTSC: using TSC
2024-10-09T17:13:07.595Z In(05) vmx PTSC: hardware TSCs are synchronized.
2024-10-09T17:13:07.595Z In(05) vmx PTSC: hardware TSCs may have been adjusted by the host.
2024-10-09T17:13:07.595Z In(05) vmx PTSC: current PTSC=226568633185
2024-10-09T17:13:07.602Z In(05) vmx WQPoolAllocPoll : pollIx = 3, signalHandle = 1132
2024-10-09T17:13:07.645Z In(05) vmx ConfigCheck: No rules file found. Checks are disabled.
2024-10-09T17:13:07.645Z In(05) vmx changing directory to C:\Users\<USER>\Desktop\Metasploitable2-Linux\.
2024-10-09T17:13:07.645Z In(05) vmx Config file: C:\Users\<USER>\Desktop\Metasploitable2-Linux\Metasploitable.vmx
2024-10-09T17:13:07.645Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=1, newAppState=1873, success=1 additionalError=0
2024-10-09T17:13:07.645Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=2, newAppState=1878, success=1 additionalError=0
2024-10-09T17:13:07.668Z In(05) vmx LogRotation: Rotating to a new log file (keepOld 3) took 0.020826 seconds.
2024-10-09T17:13:07.681Z No(00) vmx LogVMXReplace: Successful switching from temporary to permanent log file took 0.034607 seconds.
2024-10-09T17:13:07.699Z No(00) vmx ConfigDB: Setting ide1:0.fileName = "auto detect"
2024-10-09T17:13:07.702Z Wa(03) vmx PowerOn
2024-10-09T17:13:07.702Z In(05) vmx VMX_PowerOn: VMX build 23775571, UI build 23775571
2024-10-09T17:13:07.702Z In(05) vmx HostWin32: WIN32 NUMA node 0, CPU mask 0x000000000000000f
2024-10-09T17:13:07.708Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=0, newAppState=1871, success=1 additionalError=0
2024-10-09T17:13:07.710Z In(05) vmx VMXSTATS: Successfully created stats file 'Metasploitable.scoreboard'
2024-10-09T17:13:07.711Z In(05) vmx VMXSTATS: Update Product Information: VMware Workstation	17.5.2	build-23775571	Release  TotalBlockSize: 56
2024-10-09T17:13:07.712Z In(05) vmx HOST Windows version 10.0, build 22631, platform 2, ""
2024-10-09T17:13:07.712Z In(05) vmx DICT --- GLOBAL SETTINGS C:\ProgramData\VMware\VMware Workstation\settings.ini
2024-10-09T17:13:07.712Z In(05) vmx DICT          printers.enabled = "FALSE"
2024-10-09T17:13:07.712Z In(05) vmx DICT --- NON PERSISTENT (null)
2024-10-09T17:13:07.712Z In(05) vmx DICT --- USER PREFERENCES C:\Users\<USER>\AppData\Roaming\VMware\preferences.ini
2024-10-09T17:13:07.712Z In(05) vmx DICT pref.keyboardAndMouse.vmHotKey.enabled = "FALSE"
2024-10-09T17:13:07.712Z In(05) vmx DICT pref.keyboardAndMouse.vmHotKey.count = "0"
2024-10-09T17:13:07.712Z In(05) vmx DICT pref.wspro.firstRunDismissedVersion = "17.5.2"
2024-10-09T17:13:07.712Z In(05) vmx DICT pref.updatesVersionIgnore.numItems = "1"
2024-10-09T17:13:07.712Z In(05) vmx DICT pref.updatesVersionIgnore0.key = <not printed>
2024-10-09T17:13:07.712Z In(05) vmx DICT pref.updatesVersionIgnore0.value = "bb61d294-c7fd-4b93-bdb3-48a9b5b74f44"
2024-10-09T17:13:07.712Z In(05) vmx DICT   pref.lastUpdateCheckSec = "1728491637"
2024-10-09T17:13:07.712Z In(05) vmx DICT pref.ws.session.window.count = "1"
2024-10-09T17:13:07.712Z In(05) vmx DICT pref.ws.session.window0.tab.count = "2"
2024-10-09T17:13:07.712Z In(05) vmx DICT pref.ws.session.window0.tab0.cnxType = "vmdb"
2024-10-09T17:13:07.712Z In(05) vmx DICT pref.ws.session.window0.tab1.cnxType = "vmdb"
2024-10-09T17:13:07.712Z In(05) vmx DICT pref.ws.session.window0.sidebar = "TRUE"
2024-10-09T17:13:07.712Z In(05) vmx DICT pref.ws.session.window0.sidebar.width = "200"
2024-10-09T17:13:07.712Z In(05) vmx DICT pref.ws.session.window0.statusBar = "TRUE"
2024-10-09T17:13:07.712Z In(05) vmx DICT pref.ws.session.window0.tabs = "TRUE"
2024-10-09T17:13:07.712Z In(05) vmx DICT pref.ws.session.window0.thumbnailBar = "FALSE"
2024-10-09T17:13:07.712Z In(05) vmx DICT pref.ws.session.window0.thumbnailBar.size = "100"
2024-10-09T17:13:07.712Z In(05) vmx DICT pref.ws.session.window0.thumbnailBar.view = "opened-vms"
2024-10-09T17:13:07.712Z In(05) vmx DICT pref.ws.session.window0.placement.left = "224"
2024-10-09T17:13:07.712Z In(05) vmx DICT pref.ws.session.window0.placement.top = "224"
2024-10-09T17:13:07.712Z In(05) vmx DICT pref.ws.session.window0.placement.right = "1152"
2024-10-09T17:13:07.712Z In(05) vmx DICT pref.ws.session.window0.placement.bottom = "839"
2024-10-09T17:13:07.712Z In(05) vmx DICT pref.ws.session.window0.maximized = "FALSE"
2024-10-09T17:13:07.712Z In(05) vmx DICT  pref.fullscreen.autohide = "TRUE"
2024-10-09T17:13:07.712Z In(05) vmx DICT pref.ws.session.window0.tab0.dest = ""
2024-10-09T17:13:07.712Z In(05) vmx DICT pref.ws.session.window0.tab0.file = ""
2024-10-09T17:13:07.712Z In(05) vmx DICT pref.ws.session.window0.tab0.type = "home"
2024-10-09T17:13:07.712Z In(05) vmx DICT pref.ws.session.window0.tab0.focused = "FALSE"
2024-10-09T17:13:07.712Z In(05) vmx DICT pref.ws.session.window0.tab1.dest = ""
2024-10-09T17:13:07.712Z In(05) vmx DICT pref.ws.session.window0.tab1.file = "C:\Users\<USER>\Desktop\Metasploitable2-Linux\Metasploitable.vmx"
2024-10-09T17:13:07.712Z In(05) vmx DICT pref.ws.session.window0.tab1.type = "vm"
2024-10-09T17:13:07.712Z In(05) vmx DICT pref.ws.session.window0.tab1.focused = "TRUE"
2024-10-09T17:13:07.712Z In(05) vmx DICT pref.ws.session.window0.tab2.cnxType = "vmdb"
2024-10-09T17:13:07.712Z In(05) vmx DICT --- USER DEFAULTS C:\Users\<USER>\AppData\Roaming\VMware\config.ini
2024-10-09T17:13:07.712Z In(05) vmx DICT --- HOST DEFAULTS C:\ProgramData\VMware\VMware Workstation\config.ini
2024-10-09T17:13:07.712Z In(05) vmx DICT         authd.client.port = "902"
2024-10-09T17:13:07.712Z In(05) vmx DICT           authd.proxy.nfc = "vmware-hostd:ha-nfc"
2024-10-09T17:13:07.712Z In(05) vmx DICT installerDefaults.autoSoftwareUpdateEnabled = "yes"
2024-10-09T17:13:07.712Z In(05) vmx DICT installerDefaults.autoSoftwareUpdateEnabled.epoch = "5203"
2024-10-09T17:13:07.712Z In(05) vmx DICT installerDefaults.componentDownloadEnabled = "yes"
2024-10-09T17:13:07.712Z In(05) vmx DICT installerDefaults.dataCollectionEnabled = "yes"
2024-10-09T17:13:07.712Z In(05) vmx DICT installerDefaults.dataCollectionEnabled.epoch = "5203"
2024-10-09T17:13:07.712Z In(05) vmx DICT --- SITE DEFAULTS C:\ProgramData\VMware\VMware Workstation\config.ini
2024-10-09T17:13:07.712Z In(05) vmx DICT         authd.client.port = "902"
2024-10-09T17:13:07.712Z In(05) vmx DICT           authd.proxy.nfc = "vmware-hostd:ha-nfc"
2024-10-09T17:13:07.712Z In(05) vmx DICT installerDefaults.autoSoftwareUpdateEnabled = "yes"
2024-10-09T17:13:07.712Z In(05) vmx DICT installerDefaults.autoSoftwareUpdateEnabled.epoch = "5203"
2024-10-09T17:13:07.712Z In(05) vmx DICT installerDefaults.componentDownloadEnabled = "yes"
2024-10-09T17:13:07.712Z In(05) vmx DICT installerDefaults.dataCollectionEnabled = "yes"
2024-10-09T17:13:07.712Z In(05) vmx DICT installerDefaults.dataCollectionEnabled.epoch = "5203"
2024-10-09T17:13:07.712Z In(05) vmx DICT --- NONPERSISTENT
2024-10-09T17:13:07.712Z In(05) vmx DICT            vmx.stdio.keep = "TRUE"
2024-10-09T17:13:07.712Z In(05) vmx DICT             gui.available = "TRUE"
2024-10-09T17:13:07.712Z In(05) vmx DICT --- COMMAND LINE
2024-10-09T17:13:07.712Z In(05) vmx DICT            vmx.stdio.keep = "TRUE"
2024-10-09T17:13:07.712Z In(05) vmx DICT             gui.available = "TRUE"
2024-10-09T17:13:07.712Z In(05) vmx DICT --- RECORDING
2024-10-09T17:13:07.712Z In(05) vmx DICT            vmx.stdio.keep = "TRUE"
2024-10-09T17:13:07.712Z In(05) vmx DICT             gui.available = "TRUE"
2024-10-09T17:13:07.712Z In(05) vmx DICT --- CONFIGURATION C:\Users\<USER>\Desktop\Metasploitable2-Linux\Metasploitable.vmx 
2024-10-09T17:13:07.712Z In(05) vmx DICT            config.version = "8"
2024-10-09T17:13:07.712Z In(05) vmx DICT         virtualHW.version = "7"
2024-10-09T17:13:07.712Z In(05) vmx DICT                  numvcpus = "1"
2024-10-09T17:13:07.712Z In(05) vmx DICT                  maxvcpus = "4"
2024-10-09T17:13:07.712Z In(05) vmx DICT             scsi0.present = "TRUE"
2024-10-09T17:13:07.712Z In(05) vmx DICT          scsi0.virtualDev = "lsilogic"
2024-10-09T17:13:07.712Z In(05) vmx DICT                   memsize = "512"
2024-10-09T17:13:07.712Z In(05) vmx DICT            ide1:0.present = "TRUE"
2024-10-09T17:13:07.712Z In(05) vmx DICT           ide1:0.fileName = "auto detect"
2024-10-09T17:13:07.712Z In(05) vmx DICT         ide1:0.deviceType = "cdrom-raw"
2024-10-09T17:13:07.712Z In(05) vmx DICT         ethernet0.present = "TRUE"
2024-10-09T17:13:07.712Z In(05) vmx DICT  ethernet0.connectionType = "nat"
2024-10-09T17:13:07.712Z In(05) vmx DICT   ethernet0.wakeOnPcktRcv = "FALSE"
2024-10-09T17:13:07.713Z In(05) vmx DICT     ethernet0.addressType = "generated"
2024-10-09T17:13:07.713Z In(05) vmx DICT               usb.present = "TRUE"
2024-10-09T17:13:07.713Z In(05) vmx DICT              ehci.present = "TRUE"
2024-10-09T17:13:07.713Z In(05) vmx DICT        pciBridge0.present = "TRUE"
2024-10-09T17:13:07.713Z In(05) vmx DICT        pciBridge4.present = "TRUE"
2024-10-09T17:13:07.713Z In(05) vmx DICT     pciBridge4.virtualDev = "pcieRootPort"
2024-10-09T17:13:07.713Z In(05) vmx DICT      pciBridge4.functions = "8"
2024-10-09T17:13:07.713Z In(05) vmx DICT        pciBridge5.present = "TRUE"
2024-10-09T17:13:07.713Z In(05) vmx DICT     pciBridge5.virtualDev = "pcieRootPort"
2024-10-09T17:13:07.713Z In(05) vmx DICT      pciBridge5.functions = "8"
2024-10-09T17:13:07.713Z In(05) vmx DICT        pciBridge6.present = "TRUE"
2024-10-09T17:13:07.713Z In(05) vmx DICT     pciBridge6.virtualDev = "pcieRootPort"
2024-10-09T17:13:07.713Z In(05) vmx DICT      pciBridge6.functions = "8"
2024-10-09T17:13:07.713Z In(05) vmx DICT        pciBridge7.present = "TRUE"
2024-10-09T17:13:07.713Z In(05) vmx DICT     pciBridge7.virtualDev = "pcieRootPort"
2024-10-09T17:13:07.713Z In(05) vmx DICT      pciBridge7.functions = "8"
2024-10-09T17:13:07.713Z In(05) vmx DICT             vmci0.present = "TRUE"
2024-10-09T17:13:07.713Z In(05) vmx DICT    roamingVM.exitBehavior = "go"
2024-10-09T17:13:07.713Z In(05) vmx DICT               displayName = "Metasploitable2-Linux"
2024-10-09T17:13:07.713Z In(05) vmx DICT                   guestOS = "ubuntu"
2024-10-09T17:13:07.713Z In(05) vmx DICT                     nvram = "Metasploitable.nvram"
2024-10-09T17:13:07.713Z In(05) vmx DICT virtualHW.productCompatibility = "hosted"
2024-10-09T17:13:07.713Z In(05) vmx DICT        extendedConfigFile = "Metasploitable.vmxf"
2024-10-09T17:13:07.713Z In(05) vmx DICT ethernet0.generatedAddress = "00:0c:29:1a:02:47"
2024-10-09T17:13:07.713Z In(05) vmx DICT            tools.syncTime = "FALSE"
2024-10-09T17:13:07.713Z In(05) vmx DICT             uuid.location = "56 4d a7 b6 49 eb 2d eb-7b 81 20 17 1a 1a 02 47"
2024-10-09T17:13:07.713Z In(05) vmx DICT                 uuid.bios = "56 4d a7 b6 49 eb 2d eb-7b 81 20 17 1a 1a 02 47"
2024-10-09T17:13:07.713Z In(05) vmx DICT             cleanShutdown = "TRUE"
2024-10-09T17:13:07.713Z In(05) vmx DICT          replay.supported = "FALSE"
2024-10-09T17:13:07.713Z In(05) vmx DICT           replay.filename = ""
2024-10-09T17:13:07.713Z In(05) vmx DICT  pciBridge0.pciSlotNumber = "17"
2024-10-09T17:13:07.713Z In(05) vmx DICT  pciBridge4.pciSlotNumber = "21"
2024-10-09T17:13:07.713Z In(05) vmx DICT  pciBridge5.pciSlotNumber = "22"
2024-10-09T17:13:07.713Z In(05) vmx DICT  pciBridge6.pciSlotNumber = "23"
2024-10-09T17:13:07.713Z In(05) vmx DICT  pciBridge7.pciSlotNumber = "24"
2024-10-09T17:13:07.713Z In(05) vmx DICT       scsi0.pciSlotNumber = "16"
2024-10-09T17:13:07.713Z In(05) vmx DICT         usb.pciSlotNumber = "32"
2024-10-09T17:13:07.713Z In(05) vmx DICT   ethernet0.pciSlotNumber = "33"
2024-10-09T17:13:07.713Z In(05) vmx DICT        ehci.pciSlotNumber = "35"
2024-10-09T17:13:07.713Z In(05) vmx DICT       vmci0.pciSlotNumber = "36"
2024-10-09T17:13:07.713Z In(05) vmx DICT  vmotion.checkpointFBSize = "134217728"
2024-10-09T17:13:07.713Z In(05) vmx DICT ethernet0.generatedAddressOffset = "0"
2024-10-09T17:13:07.713Z In(05) vmx DICT                  vmci0.id = "363079114"
2024-10-09T17:13:07.713Z In(05) vmx DICT       tools.remindInstall = "TRUE"
2024-10-09T17:13:07.713Z In(05) vmx DICT        checkpoint.vmState = ""
2024-10-09T17:13:07.713Z In(05) vmx DICT                annotation = <not printed>
2024-10-09T17:13:07.713Z In(05) vmx DICT         ide1:0.autodetect = "TRUE"
2024-10-09T17:13:07.713Z In(05) vmx DICT     ide1:0.startConnected = "FALSE"
2024-10-09T17:13:07.713Z In(05) vmx DICT           scsi0:0.present = "TRUE"
2024-10-09T17:13:07.713Z In(05) vmx DICT          scsi0:0.fileName = "Metasploitable.vmdk"
2024-10-09T17:13:07.713Z In(05) vmx DICT              scsi0:0.mode = "persistent"
2024-10-09T17:13:07.713Z In(05) vmx DICT              scsi0:0.redo = ""
2024-10-09T17:13:07.713Z In(05) vmx DICT   ethernet1.pciSlotNumber = "34"
2024-10-09T17:13:07.713Z In(05) vmx DICT         ethernet1.present = "TRUE"
2024-10-09T17:13:07.713Z In(05) vmx DICT  ethernet1.connectionType = "hostonly"
2024-10-09T17:13:07.713Z In(05) vmx DICT   ethernet1.wakeOnPcktRcv = "FALSE"
2024-10-09T17:13:07.713Z In(05) vmx DICT     ethernet1.addressType = "generated"
2024-10-09T17:13:07.713Z In(05) vmx DICT ethernet1.generatedAddress = "00:0c:29:1a:02:51"
2024-10-09T17:13:07.713Z In(05) vmx DICT ethernet1.generatedAddressOffset = "10"
2024-10-09T17:13:07.713Z In(05) vmx DICT             sound.present = "FALSE"
2024-10-09T17:13:07.713Z In(05) vmx DICT           scsi0:1.present = "FALSE"
2024-10-09T17:13:07.713Z In(05) vmx DICT           floppy0.present = "FALSE"
2024-10-09T17:13:07.713Z In(05) vmx DICT         vmxstats.filename = "Metasploitable.scoreboard"
2024-10-09T17:13:07.713Z In(05) vmx DICT      numa.autosize.cookie = "10001"
2024-10-09T17:13:07.713Z In(05) vmx DICT numa.autosize.vcpu.maxPerVirtualNode = "1"
2024-10-09T17:13:07.713Z In(05) vmx DICT             svga.vramSize = "134217728"
2024-10-09T17:13:07.713Z In(05) vmx DICT    monitor.phys_bits_used = "40"
2024-10-09T17:13:07.713Z In(05) vmx DICT              softPowerOff = "FALSE"
2024-10-09T17:13:07.713Z In(05) vmx DICT --- USER DEFAULTS C:\Users\<USER>\AppData\Roaming\VMware\config.ini 
2024-10-09T17:13:07.713Z In(05) vmx DICT --- HOST DEFAULTS C:\ProgramData\VMware\VMware Workstation\config.ini 
2024-10-09T17:13:07.713Z In(05) vmx DICT         authd.client.port = "902"
2024-10-09T17:13:07.713Z In(05) vmx DICT           authd.proxy.nfc = "vmware-hostd:ha-nfc"
2024-10-09T17:13:07.713Z In(05) vmx DICT installerDefaults.autoSoftwareUpdateEnabled = "yes"
2024-10-09T17:13:07.713Z In(05) vmx DICT installerDefaults.autoSoftwareUpdateEnabled.epoch = "5203"
2024-10-09T17:13:07.713Z In(05) vmx DICT installerDefaults.componentDownloadEnabled = "yes"
2024-10-09T17:13:07.713Z In(05) vmx DICT installerDefaults.dataCollectionEnabled = "yes"
2024-10-09T17:13:07.713Z In(05) vmx DICT installerDefaults.dataCollectionEnabled.epoch = "5203"
2024-10-09T17:13:07.713Z In(05) vmx DICT --- SITE DEFAULTS C:\ProgramData\VMware\VMware Workstation\config.ini 
2024-10-09T17:13:07.713Z In(05) vmx DICT         authd.client.port = "902"
2024-10-09T17:13:07.713Z In(05) vmx DICT           authd.proxy.nfc = "vmware-hostd:ha-nfc"
2024-10-09T17:13:07.713Z In(05) vmx DICT installerDefaults.autoSoftwareUpdateEnabled = "yes"
2024-10-09T17:13:07.713Z In(05) vmx DICT installerDefaults.autoSoftwareUpdateEnabled.epoch = "5203"
2024-10-09T17:13:07.713Z In(05) vmx DICT installerDefaults.componentDownloadEnabled = "yes"
2024-10-09T17:13:07.713Z In(05) vmx DICT installerDefaults.dataCollectionEnabled = "yes"
2024-10-09T17:13:07.713Z In(05) vmx DICT installerDefaults.dataCollectionEnabled.epoch = "5203"
2024-10-09T17:13:07.713Z In(05) vmx DICT --- GLOBAL SETTINGS C:\ProgramData\VMware\VMware Workstation\settings.ini 
2024-10-09T17:13:07.713Z In(05) vmx DICT          printers.enabled = "FALSE"
2024-10-09T17:13:07.713Z In(05) vmx VMXSTATS: Registering 1 stats: vmx.diskLibDataVmdkOpenTime
2024-10-09T17:13:07.713Z In(05) vmx VMXSTATS: Registering 2 stats: vmx.diskLibDataVmdkOpenTime
2024-10-09T17:13:07.713Z In(05) vmx VMXSTATS: Registering 3 stats: vmx.diskLibDataVmdkGrowTime
2024-10-09T17:13:07.713Z In(05) vmx VMXSTATS: Registering 4 stats: vmx.diskLibDataVmdkGrowTime
2024-10-09T17:13:07.713Z In(05) vmx VMXSTATS: Registering 5 stats: vmx.diskLibDigestVmdkOpenTime
2024-10-09T17:13:07.713Z In(05) vmx VMXSTATS: Registering 6 stats: vmx.diskLibDigestVmdkOpenTime
2024-10-09T17:13:07.713Z In(05) vmx VMXSTATS: Registering 7 stats: vmx.diskLibDigestVmdkGrowTime
2024-10-09T17:13:07.713Z In(05) vmx VMXSTATS: Registering 8 stats: vmx.diskLibDigestVmdkGrowTime
2024-10-09T17:13:07.713Z In(05) vmx VMXSTATS: Registering 9 stats: vmx.diskLibDigestFileDataGrowTime
2024-10-09T17:13:07.713Z In(05) vmx VMXSTATS: Registering 10 stats: vmx.diskLibDigestFileDataGrowTime
2024-10-09T17:13:07.713Z In(05) vmx VMXSTATS: Registering 11 stats: vmx.digestLibOpenIntTime
2024-10-09T17:13:07.713Z In(05) vmx VMXSTATS: Registering 12 stats: vmx.digestLibOpenIntTime
2024-10-09T17:13:07.713Z In(05) vmx VMXSTATS: Registering 13 stats: vmx.diskLibDataVmdkCloseTime
2024-10-09T17:13:07.713Z In(05) vmx VMXSTATS: Registering 14 stats: vmx.diskLibDataVmdkCloseTime
2024-10-09T17:13:07.713Z In(05) vmx VMXSTATS: Registering 15 stats: vmx.diskLibDigestVmdkCloseTime
2024-10-09T17:13:07.713Z In(05) vmx VMXSTATS: Registering 16 stats: vmx.diskLibDigestVmdkCloseTime
2024-10-09T17:13:07.713Z In(05) vmx VMXSTATS: Registering 17 stats: vmx.diskLibVmdkCreateTime
2024-10-09T17:13:07.713Z In(05) vmx VMXSTATS: Registering 18 stats: vmx.diskLibVmdkCreateTime
2024-10-09T17:13:07.713Z In(05) vmx VMXSTATS: Registering 19 stats: vmx.diskLibChildVmdkCreateTime
2024-10-09T17:13:07.713Z In(05) vmx VMXSTATS: Registering 20 stats: vmx.diskLibChildVmdkCreateTime
2024-10-09T17:13:07.713Z In(05) vmx VMXSTATS: Registering 21 stats: vmx.snapshotCreateTime
2024-10-09T17:13:07.713Z In(05) vmx VMXSTATS: Registering 22 stats: vmx.snapshotCreateTime
2024-10-09T17:13:07.713Z In(05) vmx VMXSTATS: Registering 23 stats: vmx.snapshotCreateQuiescedTime
2024-10-09T17:13:07.714Z In(05) vmx VMXSTATS: Registering 24 stats: vmx.snapshotCreateQuiescedTime
2024-10-09T17:13:07.714Z In(05) vmx VMXSTATS: Registering 25 stats: vmx.snapshotCreateMemoryTime
2024-10-09T17:13:07.714Z In(05) vmx VMXSTATS: Registering 26 stats: vmx.snapshotCreateMemoryTime
2024-10-09T17:13:07.714Z In(05) vmx VMXSTATS: Registering 27 stats: vmx.snapshotDeleteTime
2024-10-09T17:13:07.714Z In(05) vmx VMXSTATS: Registering 28 stats: vmx.snapshotDeleteTime
2024-10-09T17:13:07.714Z In(05) vmx VMXSTATS: Registering 29 stats: vmx.snapshotConsolidateTime
2024-10-09T17:13:07.714Z In(05) vmx VMXSTATS: Registering 30 stats: vmx.snapshotConsolidateTime
2024-10-09T17:13:07.714Z In(05) vmx VMXSTATS: Registering 31 stats: vmx.checkpointStunTime
2024-10-09T17:13:07.714Z In(05) vmx VMXSTATS: Registering 32 stats: vmx.checkpointStunTime
2024-10-09T17:13:07.714Z In(05) vmx VMXSTATS: Registering 33 stats: vmx.setPolicyTime
2024-10-09T17:13:07.714Z In(05) vmx VMXSTATS: Registering 34 stats: vmx.setPolicyTime
2024-10-09T17:13:07.714Z In(05) vmx VMXSTATS: Registering 35 stats: vmx.filtlibApplyDiskConfigTime
2024-10-09T17:13:07.715Z In(05) vmx VMXSTATS: Registering 36 stats: vmx.filtlibApplyDiskConfigTime
2024-10-09T17:13:07.715Z In(05) vmx VMXSTATS: Registering 37 stats: vmx.diskLibGetInfoTime
2024-10-09T17:13:07.715Z In(05) vmx VMXSTATS: Registering 38 stats: vmx.diskLibGetInfoTime
2024-10-09T17:13:07.715Z In(05) vmx VMXSTATS: Registering 39 stats: vmx.diskLibDigestGetInfoTime
2024-10-09T17:13:07.715Z In(05) vmx VMXSTATS: Registering 40 stats: vmx.diskLibDigestGetInfoTime
2024-10-09T17:13:07.715Z In(05) vmx Powering on guestOS 'ubuntu' using the configuration for 'ubuntu'.
2024-10-09T17:13:07.715Z In(05) vmx ToolsISO: open of C:\Program Files (x86)\VMware\VMware Workstation\isoimages_manifest.txt.sig failed: Could not find the file
2024-10-09T17:13:07.715Z In(05) vmx ToolsISO: Unable to read signature file 'C:\Program Files (x86)\VMware\VMware Workstation\isoimages_manifest.txt.sig', ignoring.
2024-10-09T17:13:07.716Z In(05) vmx ToolsISO: Updated cached value for imageName to 'linux.iso'.
2024-10-09T17:13:07.716Z In(05) vmx ToolsISO: Selected Tools ISO 'linux.iso' for 'ubuntu' guest.
2024-10-09T17:13:07.716Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=1, newAppState=1873, success=1 additionalError=0
2024-10-09T17:13:07.716Z In(05) vmx DEVSWAP: GuestOS does not require LSI adapter swap.
2024-10-09T17:13:07.718Z In(05) vmx Monitor Mode: CPL0
2024-10-09T17:13:07.731Z In(05) vmx OvhdMem_PowerOn: initial admission: paged   440354 nonpaged    35592 anonymous     4760
2024-10-09T17:13:07.731Z In(05) vmx VMMEM: Initial Reservation: 1877MB (MainMem=512MB)
2024-10-09T17:13:07.732Z In(05) vmx llc: maximum vcpus per LLC: 1
2024-10-09T17:13:07.732Z In(05) vmx llc: vLLC size: 1
2024-10-09T17:13:07.733Z In(05) vmx MemSched_PowerOn: balloon minGuestSize 52428 (80% of min required size 65536)
2024-10-09T17:13:07.733Z In(05) vmx MemSched: reserved mem (in MB) min 128 max 6200 recommended 6200
2024-10-09T17:13:07.733Z In(05) vmx MemSched: pg 440354 np 35592 anon 4760 mem 131072
2024-10-09T17:13:07.784Z In(05) vmx MemSched: numvm 2 locked pages: num 384940 max 1570816
2024-10-09T17:13:07.784Z In(05) vmx MemSched: locked Page Limit: host 1647497 config 1587200
2024-10-09T17:13:07.784Z In(05) vmx MemSched: minmempct 50 minalloc 1064331 admitted 1
2024-10-09T17:13:07.785Z In(05) PowerNotifyThread VTHREAD 21236 "PowerNotifyThread"
2024-10-09T17:13:07.785Z In(05) PowerNotifyThread PowerNotify thread is alive.
2024-10-09T17:13:07.785Z In(05) vmx VMXSTATS: Registering 41 stats: vmx.logBytesDropped
2024-10-09T17:13:07.785Z In(05) vmx VMXSTATS: Registering 42 stats: vmx.logMsgsDropped
2024-10-09T17:13:07.785Z In(05) vmx VMXSTATS: Registering 43 stats: vmx.logBytesLogged
2024-10-09T17:13:07.785Z In(05) vmx VMXSTATS: Registering 44 stats: vmx.logWriteMinMaxTime
2024-10-09T17:13:07.785Z In(05) vmx VMXSTATS: Registering 45 stats: vmx.logWriteAvgTime
2024-10-09T17:13:07.785Z In(05) vmx LICENSE: Running unlicensed VMX (VMware Workstation)
2024-10-09T17:13:07.785Z In(05) vthread-11728 VTHREAD 11728 "vthread-11728"
2024-10-09T17:13:07.786Z In(05) vmx Win32U_GetFileAttributes: GetFileAttributesExW("C:\Users\<USER>\Desktop\Metasploitable2-Linux\Metasploitable.vmpl", ...) failed, error: 2
2024-10-09T17:13:07.786Z In(05) vmx PolicyVMXFindPolicyKey: policy file does not exist.
2024-10-09T17:13:07.786Z In(05) vmx Win32U_GetFileAttributes: GetFileAttributesExW("C:\Users\<USER>\Desktop\Metasploitable2-Linux\Metasploitable.vmpl", ...) failed, error: 2
2024-10-09T17:13:07.786Z In(05) vmx PolicyVMXFindPolicyKey: policy file does not exist.
2024-10-09T17:13:07.787Z In(05) vmx Host PA size: 39 bits. Guest PA size: 40 bits.
2024-10-09T17:13:07.787Z In(05) vmx ToolsISO: Refreshing imageName for 'ubuntu' (refreshCount=1, lastCount=1).
2024-10-09T17:13:07.787Z In(05) vmx ToolsISO: open of C:\Program Files (x86)\VMware\VMware Workstation\isoimages_manifest.txt.sig failed: Could not find the file
2024-10-09T17:13:07.787Z In(05) vmx ToolsISO: Unable to read signature file 'C:\Program Files (x86)\VMware\VMware Workstation\isoimages_manifest.txt.sig', ignoring.
2024-10-09T17:13:07.788Z In(05) vmx ToolsISO: Updated cached value for imageName to 'linux.iso'.
2024-10-09T17:13:07.788Z In(05) vmx ToolsISO: Selected Tools ISO 'linux.iso' for 'ubuntu' guest.
2024-10-09T17:13:07.789Z In(05) deviceThread VTHREAD 19252 "deviceThread"
2024-10-09T17:13:07.789Z In(05) deviceThread Device thread is alive
2024-10-09T17:13:07.789Z In(05) vmx Host VT-x Capabilities:
2024-10-09T17:13:07.789Z In(05) vmx Basic VMX Information (0x00da050000000013)
2024-10-09T17:13:07.789Z In(05) vmx   VMCS revision ID                          19
2024-10-09T17:13:07.789Z In(05) vmx   VMCS region length                      1280
2024-10-09T17:13:07.789Z In(05) vmx   VMX physical-address width           natural
2024-10-09T17:13:07.789Z In(05) vmx   SMM dual-monitor mode                    yes
2024-10-09T17:13:07.789Z In(05) vmx   VMCS memory type                          WB
2024-10-09T17:13:07.789Z In(05) vmx   Advanced INS/OUTS info                   yes
2024-10-09T17:13:07.789Z In(05) vmx   True VMX MSRs                            yes
2024-10-09T17:13:07.789Z In(05) vmx   Exception Injection ignores error code    no
2024-10-09T17:13:07.789Z In(05) vmx True Pin-Based VM-Execution Controls (0x000000ff00000016)
2024-10-09T17:13:07.789Z In(05) vmx   External-interrupt exiting               {0,1}
2024-10-09T17:13:07.789Z In(05) vmx   NMI exiting                              {0,1}
2024-10-09T17:13:07.789Z In(05) vmx   Virtual NMIs                             {0,1}
2024-10-09T17:13:07.789Z In(05) vmx   Activate VMX-preemption timer            {0,1}
2024-10-09T17:13:07.790Z In(05) vmx   Process posted interrupts                {0,1}
2024-10-09T17:13:07.790Z In(05) vmx True Primary Processor-Based VM-Execution Controls (0xfff9fffe04006172)
2024-10-09T17:13:07.790Z In(05) vmx   Interrupt-window exiting                 {0,1}
2024-10-09T17:13:07.790Z In(05) vmx   Use TSC offsetting                       {0,1}
2024-10-09T17:13:07.790Z In(05) vmx   HLT exiting                              {0,1}
2024-10-09T17:13:07.790Z In(05) vmx   INVLPG exiting                           {0,1}
2024-10-09T17:13:07.790Z In(05) vmx   MWAIT exiting                            {0,1}
2024-10-09T17:13:07.790Z In(05) vmx   RDPMC exiting                            {0,1}
2024-10-09T17:13:07.790Z In(05) vmx   RDTSC exiting                            {0,1}
2024-10-09T17:13:07.790Z In(05) vmx   CR3-load exiting                         {0,1}
2024-10-09T17:13:07.790Z In(05) vmx   CR3-store exiting                        {0,1}
2024-10-09T17:13:07.790Z In(05) vmx   Activate tertiary controls               { 0 }
2024-10-09T17:13:07.790Z In(05) vmx   CR8-load exiting                         {0,1}
2024-10-09T17:13:07.790Z In(05) vmx   CR8-store exiting                        {0,1}
2024-10-09T17:13:07.790Z In(05) vmx   Use TPR shadow                           {0,1}
2024-10-09T17:13:07.790Z In(05) vmx   NMI-window exiting                       {0,1}
2024-10-09T17:13:07.790Z In(05) vmx   MOV-DR exiting                           {0,1}
2024-10-09T17:13:07.790Z In(05) vmx   Unconditional I/O exiting                {0,1}
2024-10-09T17:13:07.790Z In(05) vmx   Use I/O bitmaps                          {0,1}
2024-10-09T17:13:07.790Z In(05) vmx   Monitor trap flag                        {0,1}
2024-10-09T17:13:07.790Z In(05) vmx   Use MSR bitmaps                          {0,1}
2024-10-09T17:13:07.790Z In(05) vmx   MONITOR exiting                          {0,1}
2024-10-09T17:13:07.790Z In(05) vmx   PAUSE exiting                            {0,1}
2024-10-09T17:13:07.790Z In(05) vmx   Activate secondary controls              {0,1}
2024-10-09T17:13:07.790Z In(05) vmx Secondary Processor-Based VM-Execution Controls (0x335fbfff00000000)
2024-10-09T17:13:07.790Z In(05) vmx   Virtualize APIC accesses                 {0,1}
2024-10-09T17:13:07.790Z In(05) vmx   Enable EPT                               {0,1}
2024-10-09T17:13:07.790Z In(05) vmx   Descriptor-table exiting                 {0,1}
2024-10-09T17:13:07.790Z In(05) vmx   Enable RDTSCP                            {0,1}
2024-10-09T17:13:07.790Z In(05) vmx   Virtualize x2APIC mode                   {0,1}
2024-10-09T17:13:07.790Z In(05) vmx   Enable VPID                              {0,1}
2024-10-09T17:13:07.790Z In(05) vmx   WBINVD exiting                           {0,1}
2024-10-09T17:13:07.790Z In(05) vmx   Unrestricted guest                       {0,1}
2024-10-09T17:13:07.790Z In(05) vmx   APIC-register virtualization             {0,1}
2024-10-09T17:13:07.790Z In(05) vmx   Virtual-interrupt delivery               {0,1}
2024-10-09T17:13:07.790Z In(05) vmx   PAUSE-loop exiting                       {0,1}
2024-10-09T17:13:07.790Z In(05) vmx   RDRAND exiting                           {0,1}
2024-10-09T17:13:07.790Z In(05) vmx   Enable INVPCID                           {0,1}
2024-10-09T17:13:07.790Z In(05) vmx   Enable VM Functions                      {0,1}
2024-10-09T17:13:07.790Z In(05) vmx   Use VMCS shadowing                       { 0 }
2024-10-09T17:13:07.790Z In(05) vmx   ENCLS exiting                            {0,1}
2024-10-09T17:13:07.790Z In(05) vmx   RDSEED exiting                           {0,1}
2024-10-09T17:13:07.790Z In(05) vmx   Enable PML                               {0,1}
2024-10-09T17:13:07.790Z In(05) vmx   EPT-violation #VE                        {0,1}
2024-10-09T17:13:07.790Z In(05) vmx   Conceal VMX from PT                      {0,1}
2024-10-09T17:13:07.790Z In(05) vmx   Enable XSAVES/XRSTORS                    {0,1}
2024-10-09T17:13:07.790Z In(05) vmx   PASID translation                        { 0 }
2024-10-09T17:13:07.790Z In(05) vmx   Mode-based execute control for EPT       {0,1}
2024-10-09T17:13:07.790Z In(05) vmx   Sub-page write permissions for EPT       { 0 }
2024-10-09T17:13:07.790Z In(05) vmx   PT uses guest physical addresses         {0,1}
2024-10-09T17:13:07.790Z In(05) vmx   Use TSC scaling                          {0,1}
2024-10-09T17:13:07.790Z In(05) vmx   Enable UMWAIT and TPAUSE                 { 0 }
2024-10-09T17:13:07.790Z In(05) vmx   Enable ENCLV in VMX non-root mode        {0,1}
2024-10-09T17:13:07.790Z In(05) vmx   Enable EPC Virtualization Extensions     {0,1}
2024-10-09T17:13:07.790Z In(05) vmx   Bus lock exiting                         { 0 }
2024-10-09T17:13:07.790Z In(05) vmx   Notification VM exits                    { 0 }
2024-10-09T17:13:07.790Z In(05) vmx Tertiary Processor-Based VM-Execution Controls (0x0000000000000000)
2024-10-09T17:13:07.790Z In(05) vmx   LOADIWKEY exiting                          no
2024-10-09T17:13:07.790Z In(05) vmx   Enable HLAT                                no
2024-10-09T17:13:07.790Z In(05) vmx   Enable Paging-Write                        no
2024-10-09T17:13:07.790Z In(05) vmx   Enable Guest Paging Verification           no
2024-10-09T17:13:07.790Z In(05) vmx   Enable IPI Virtualization                  no
2024-10-09T17:13:07.790Z In(05) vmx   Enable Virtual MSR_SPEC_CTRL               no
2024-10-09T17:13:07.790Z In(05) vmx True VM-Exit Controls (0x037fffff00036dfb)
2024-10-09T17:13:07.790Z In(05) vmx   Save debug controls                      {0,1}
2024-10-09T17:13:07.790Z In(05) vmx   Host address-space size                  {0,1}
2024-10-09T17:13:07.790Z In(05) vmx   Load IA32_PERF_GLOBAL_CTRL               {0,1}
2024-10-09T17:13:07.790Z In(05) vmx   Acknowledge interrupt on exit            {0,1}
2024-10-09T17:13:07.790Z In(05) vmx   Save IA32_PAT                            {0,1}
2024-10-09T17:13:07.790Z In(05) vmx   Load IA32_PAT                            {0,1}
2024-10-09T17:13:07.790Z In(05) vmx   Save IA32_EFER                           {0,1}
2024-10-09T17:13:07.790Z In(05) vmx   Load IA32_EFER                           {0,1}
2024-10-09T17:13:07.790Z In(05) vmx   Save VMX-preemption timer                {0,1}
2024-10-09T17:13:07.790Z In(05) vmx   Clear IA32_BNDCFGS                       { 0 }
2024-10-09T17:13:07.790Z In(05) vmx   Conceal VMX from processor trace         {0,1}
2024-10-09T17:13:07.790Z In(05) vmx   Clear IA32_RTIT MSR                      {0,1}
2024-10-09T17:13:07.790Z In(05) vmx   Clear IA32_LBR_CTL MSR                   { 0 }
2024-10-09T17:13:07.790Z In(05) vmx   Clear user-interrupt notification vector { 0 }
2024-10-09T17:13:07.790Z In(05) vmx   Load CET state                           { 0 }
2024-10-09T17:13:07.790Z In(05) vmx   Load PKRS                                { 0 }
2024-10-09T17:13:07.790Z In(05) vmx True VM-Entry Controls (0x0006ffff000011fb)
2024-10-09T17:13:07.790Z In(05) vmx   Load debug controls                      {0,1}
2024-10-09T17:13:07.790Z In(05) vmx   IA-32e mode guest                        {0,1}
2024-10-09T17:13:07.790Z In(05) vmx   Entry to SMM                             {0,1}
2024-10-09T17:13:07.790Z In(05) vmx   Deactivate dual-monitor mode             {0,1}
2024-10-09T17:13:07.790Z In(05) vmx   Load IA32_PERF_GLOBAL_CTRL               {0,1}
2024-10-09T17:13:07.790Z In(05) vmx   Load IA32_PAT                            {0,1}
2024-10-09T17:13:07.790Z In(05) vmx   Load IA32_EFER                           {0,1}
2024-10-09T17:13:07.790Z In(05) vmx   Load IA32_BNDCFGS                        { 0 }
2024-10-09T17:13:07.790Z In(05) vmx   Conceal VMX from processor trace         {0,1}
2024-10-09T17:13:07.790Z In(05) vmx   Load IA32_RTIT MSR                       {0,1}
2024-10-09T17:13:07.790Z In(05) vmx   Load user-interrupt notification vector  { 0 }
2024-10-09T17:13:07.790Z In(05) vmx   Load CET state                           { 0 }
2024-10-09T17:13:07.790Z In(05) vmx   Load IA32_LBR_CTL MSR                    { 0 }
2024-10-09T17:13:07.790Z In(05) vmx   Load PKRS                                { 0 }
2024-10-09T17:13:07.790Z In(05) vmx VPID and EPT Capabilities (0x00000f0106734141)
2024-10-09T17:13:07.790Z In(05) vmx   R=0/W=0/X=1                               yes
2024-10-09T17:13:07.790Z In(05) vmx   Page-walk length 3                        yes
2024-10-09T17:13:07.790Z In(05) vmx   EPT memory type WB                        yes
2024-10-09T17:13:07.790Z In(05) vmx   2MB super-page                            yes
2024-10-09T17:13:07.790Z In(05) vmx   1GB super-page                            yes
2024-10-09T17:13:07.790Z In(05) vmx   INVEPT support                            yes
2024-10-09T17:13:07.790Z In(05) vmx   Access & Dirty Bits                       yes
2024-10-09T17:13:07.790Z In(05) vmx   Advanced VM exit information for EPT violations   yes
2024-10-09T17:13:07.790Z In(05) vmx   Supervisor shadow-stack control            no
2024-10-09T17:13:07.790Z In(05) vmx   Type 1 INVEPT                             yes
2024-10-09T17:13:07.790Z In(05) vmx   Type 2 INVEPT                             yes
2024-10-09T17:13:07.790Z In(05) vmx   INVVPID support                           yes
2024-10-09T17:13:07.790Z In(05) vmx   Type 0 INVVPID                            yes
2024-10-09T17:13:07.790Z In(05) vmx   Type 1 INVVPID                            yes
2024-10-09T17:13:07.790Z In(05) vmx   Type 2 INVVPID                            yes
2024-10-09T17:13:07.790Z In(05) vmx   Type 3 INVVPID                            yes
2024-10-09T17:13:07.790Z In(05) vmx Miscellaneous VMX Data (0x000000007004c1e7)
2024-10-09T17:13:07.790Z In(05) vmx   TSC to preemption timer ratio      7
2024-10-09T17:13:07.790Z In(05) vmx   VM-Exit saves EFER.LMA           yes
2024-10-09T17:13:07.790Z In(05) vmx   Activity State HLT               yes
2024-10-09T17:13:07.790Z In(05) vmx   Activity State shutdown          yes
2024-10-09T17:13:07.790Z In(05) vmx   Activity State wait-for-SIPI     yes
2024-10-09T17:13:07.790Z In(05) vmx   Processor trace in VMX           yes
2024-10-09T17:13:07.790Z In(05) vmx   RDMSR SMBASE MSR in SMM          yes
2024-10-09T17:13:07.790Z In(05) vmx   CR3 targets supported              4
2024-10-09T17:13:07.790Z In(05) vmx   Maximum MSR list size            512
2024-10-09T17:13:07.790Z In(05) vmx   VMXOFF holdoff of SMIs           yes
2024-10-09T17:13:07.790Z In(05) vmx   Allow all VMWRITEs               yes
2024-10-09T17:13:07.790Z In(05) vmx   Allow zero instruction length    yes
2024-10-09T17:13:07.790Z In(05) vmx   MSEG revision ID                   0
2024-10-09T17:13:07.790Z In(05) vmx VMX-Fixed Bits in CR0 (0x0000000080000021/0x00000000ffffffff)
2024-10-09T17:13:07.790Z In(05) vmx   Fixed to 0        0xffffffff00000000
2024-10-09T17:13:07.790Z In(05) vmx   Fixed to 1        0x0000000080000021
2024-10-09T17:13:07.790Z In(05) vmx   Variable          0x000000007fffffde
2024-10-09T17:13:07.790Z In(05) vmx VMX-Fixed Bits in CR4 (0x0000000000002000/0x0000000000772fff)
2024-10-09T17:13:07.790Z In(05) vmx   Fixed to 0        0xffffffffff88d000
2024-10-09T17:13:07.790Z In(05) vmx   Fixed to 1        0x0000000000002000
2024-10-09T17:13:07.790Z In(05) vmx   Variable          0x0000000000770fff
2024-10-09T17:13:07.790Z In(05) vmx VMCS Enumeration (0x000000000000002e)
2024-10-09T17:13:07.790Z In(05) vmx   Highest index                   0x17
2024-10-09T17:13:07.790Z In(05) vmx VM Functions (0x0000000000000001)
2024-10-09T17:13:07.790Z In(05) vmx   Function  0 (EPTP-switching) supported.
2024-10-09T17:13:07.790Z In(05) vmx Monitor_PowerOn: HostedVSMP skew tracking is disabled
2024-10-09T17:13:07.790Z In(05) vmx vmm-modules: [vmm.vmm, vmce-none.vmm, viommu-none.vmm, vprobe-none.vmm, hv-vt.vmm, gphys-ept.vmm, callstack-none.vmm, !tdxSharedVMData=0x0, !vmSamples=0x0, !theIOSpace=0x40, !ttGPPerVcpu=0x6e00, {UseUnwind}=0x0, numVCPUsAsAddr=0x1, {SharedAreaReservations}=0x6e40, {rodataSize}=0x209c0, {textAddr}=0xfffffffffc000000, {textSize}=0x8e379, <MonSrcFile>]
2024-10-09T17:13:07.790Z In(05) vmx vmm-vcpus:   1
2024-10-09T17:13:07.812Z In(05) vmx KHZEstimate 1190389
2024-10-09T17:13:07.812Z In(05) vmx MHZEstimate 1190
2024-10-09T17:13:07.812Z In(05) vmx NumVCPUs 1
2024-10-09T17:13:07.813Z In(05) vmx AIOGNRC: numThreads=18 ide=0, scsi=1, passthru=1
2024-10-09T17:13:07.813Z In(05) vmx WORKER: Creating new group with maxThreads=18 (18)
2024-10-09T17:13:07.815Z In(05) vmx WORKER: Creating new group with maxThreads=1 (19)
2024-10-09T17:13:07.815Z In(05) vmx MainMem: CPT Host WZ=0 PF=512 D=0
2024-10-09T17:13:07.815Z In(05) vmx MainMem: CPT PLS=1 PLR=1 BS=1 BlkP=32 Mult=4 W=50
2024-10-09T17:13:07.818Z In(05) vmx MainMem: Opened paging file, 'C:\Users\<USER>\Desktop\Metasploitable2-Linux\564da7b6-49eb-2deb-7b81-20171a1a0247.vmem'.
2024-10-09T17:13:07.818Z In(05) vmx MStat: Creating Stat vm.uptime
2024-10-09T17:13:07.818Z In(05) vmx MStat: Creating Stat vm.suspendTime
2024-10-09T17:13:07.818Z In(05) vmx MStat: Creating Stat vm.powerOnTimeStamp
2024-10-09T17:13:07.818Z In(05) aioCompletion VTHREAD 17512 "aioCompletion"
2024-10-09T17:13:07.818Z In(05) vmx VMXAIOMGR: Using: simple=Compl
2024-10-09T17:13:07.823Z In(05) vmx WORKER: Creating new group with maxThreads=1 (20)
2024-10-09T17:13:07.828Z In(05) vmx WORKER: Creating new group with maxThreads=1 (21)
2024-10-09T17:13:07.828Z In(05) vmx WORKER: Creating new group with maxThreads=14 (35)
2024-10-09T17:13:07.829Z In(05) vmx FeatureCompat: No VM masks.
2024-10-09T17:13:07.829Z In(05) vmx TimeTracker host to guest rate conversion 226847372807 @ 1190389000Hz -> 0 @ 1190389000Hz
2024-10-09T17:13:07.829Z In(05) vmx TimeTracker host to guest rate conversion ((x * 2147483648) >> 31) + -226847372807
2024-10-09T17:13:07.830Z In(05) vmx TSC scaling enabled.
2024-10-09T17:13:07.830Z In(05) vmx TSC offsetting enabled.
2024-10-09T17:13:07.830Z In(05) vmx timeTracker.globalProgressMaxAllowanceMS: 2000
2024-10-09T17:13:07.830Z In(05) vmx timeTracker.globalProgressToAllowanceNS: 1000
2024-10-09T17:13:07.830Z In(05) vmx MKS PowerOn
2024-10-09T17:13:07.836Z In(05) mks VTHREAD 1480 "mks"
2024-10-09T17:13:07.836Z In(05) mks MKS thread is alive
2024-10-09T17:13:07.837Z In(05) svga VTHREAD 12004 "svga"
2024-10-09T17:13:07.837Z In(05) mks MKS: SSE2=1, SSSE3=1, SSE4_1=1
2024-10-09T17:13:07.839Z In(05) svga SVGA thread is alive
2024-10-09T17:13:07.840Z In(05) mouse VTHREAD 4912 "mouse"
2024-10-09T17:13:07.840Z In(05) mks MKS-HookKeyboard: RegQueryValueEx(LowLevelHooksTimeout) failed: The system cannot find the file specified (2)
2024-10-09T17:13:07.840Z In(05) kbh VTHREAD 16000 "kbh"
2024-10-09T17:13:07.842Z In(05) mks MKS Win32: Registering top level window (0x3099c) to receive session change notification.
2024-10-09T17:13:07.844Z In(05) mks Current Display Settings:
2024-10-09T17:13:07.844Z In(05) mks    Display: 0 size: 1920x1080  position: (0, 0) name: \\.\DISPLAY1  
2024-10-09T17:13:07.844Z In(05) mks MKS Win32: MIL: 0x4000
2024-10-09T17:13:07.844Z In(05) mks MKS-RenderMain: PowerOn allowed MKSBasicOps 
2024-10-09T17:13:07.844Z In(05) mks MKS-RenderMain: ISB enabled by config
2024-10-09T17:13:07.844Z In(05) mks MKS-RenderMain: Collecting RenderOps caps from MKSBasicOps
2024-10-09T17:13:07.844Z In(05) mks MKS-RenderMain: Starting MKSBasicOps
2024-10-09T17:13:07.844Z In(05) mks MKS-RenderMain: Started MKSBasicOps
2024-10-09T17:13:07.844Z In(05) mks MKS-RenderMain: Found Full Renderer: MKSBasicOps
2024-10-09T17:13:07.844Z In(05) mks MKS-RenderMain: maxTextureSize=32768
2024-10-09T17:13:07.845Z In(05) mks KHBKL: Unable to parse keystring at: ''
2024-10-09T17:13:07.845Z In(05) mks MKSRemoteMgr: Set default display name: Metasploitable2-Linux
2024-10-09T17:13:07.845Z In(05) mks MKSRemoteMgr: Loading VNC Configuration from VM config file
2024-10-09T17:13:07.845Z In(05) mks MKSRemoteMgr: Using default VNC keymap table "us"
2024-10-09T17:13:07.845Z In(05) vmx VLANCE: send cluster threshold is 80, size = 2 recalcInterval is 20000 us
2024-10-09T17:13:07.845Z In(05) vmx VMXNET: send cluster threshold is 80, size = 2 recalcInterval is 20000 ticks, dontClusterSize is 128
2024-10-09T17:13:07.846Z In(05) vmx Chipset version: 0x13
2024-10-09T17:13:07.848Z In(05) vmx SOUNDLIB: Creating the Wave sound backend.
2024-10-09T17:13:07.849Z No(00) vmx ConfigDB: Setting pciBridge4.pciSlotNumber = "-1"
2024-10-09T17:13:07.849Z No(00) vmx ConfigDB: Setting pciBridge5.pciSlotNumber = "-1"
2024-10-09T17:13:07.849Z No(00) vmx ConfigDB: Setting pciBridge6.pciSlotNumber = "-1"
2024-10-09T17:13:07.849Z No(00) vmx ConfigDB: Setting pciBridge7.pciSlotNumber = "-1"
2024-10-09T17:13:07.849Z No(00) vmx ConfigDB: Setting pciBridge4.pciSlotNumber = "21"
2024-10-09T17:13:07.849Z No(00) vmx ConfigDB: Setting pciBridge5.pciSlotNumber = "22"
2024-10-09T17:13:07.849Z No(00) vmx ConfigDB: Setting pciBridge6.pciSlotNumber = "23"
2024-10-09T17:13:07.849Z No(00) vmx ConfigDB: Setting pciBridge7.pciSlotNumber = "24"
2024-10-09T17:13:07.855Z In(05) vmx VMXSTATS: Registering 46 stats: vmx.configWriteMinMaxTime
2024-10-09T17:13:07.855Z In(05) vmx VMXSTATS: Registering 47 stats: vmx.configWriteAvgTime
2024-10-09T17:13:07.865Z In(05) vmx MigrateBusMemPrealloc: BusMem preallocation begins.
2024-10-09T17:13:07.865Z In(05) vmx MigrateBusMemPrealloc: BusMem preallocation completes.
2024-10-09T17:13:07.865Z No(00) vmx ConfigDB: Setting scsi0:0.redo = ""
2024-10-09T17:13:07.865Z In(05) vmx DISK: OPEN scsi0:0 'C:\Users\<USER>\Desktop\Metasploitable2-Linux\Metasploitable.vmdk' persistent R[]
2024-10-09T17:13:07.871Z In(05) vmx DiskGetGeometry: Reading of disk partition table
2024-10-09T17:13:07.871Z In(05) vmx DISK: Disk 'C:\Users\<USER>\Desktop\Metasploitable2-Linux\Metasploitable.vmdk' has UUID '60 00 c2 96 27 a0 3b 6b-16 4c 0b 3d 4a 26 47 38'
2024-10-09T17:13:07.871Z In(05) vmx DISK: OPEN 'C:\Users\<USER>\Desktop\Metasploitable2-Linux\Metasploitable.vmdk' Geo (1044/255/63) BIOS Geo (1044/255/63)
2024-10-09T17:13:07.874Z In(05) vmx DISKUTILWIN32: DiskUtilW32IsATASSDDevice: Failed to send ATA IDENTIFY command to the target device.
2024-10-09T17:13:07.875Z In(05) vmx DISKUTILWIN32: DiskUtilW32IsScsiSSDDevice: Reported rotation rate = 1
2024-10-09T17:13:07.875Z In(05) vmx DISK: Opening disks took 10 ms.
2024-10-09T17:13:07.875Z In(05) vmx USBArbLib: USBArbLib initialized successfully, retryIntervalStart(5), retryIntervalMax(120), arbSocketName(\\.\pipe\vmware-usbarbpipe), useLocking(yes), tryUpgrading(no).
2024-10-09T17:13:07.875Z In(05) vmx UsbEnum: Initializing UsbEnum library, disableLocking(no), allowBootableHid(yes).
2024-10-09T17:13:07.875Z In(05) vmx USB: Initializing 'Virtual Hub' backend
2024-10-09T17:13:07.875Z In(05) vmx USB: Initializing 'Generic' backend
2024-10-09T17:13:07.875Z Wa(03) vmx USBArbLib: OUT SET_AUTO_CONNECT: Not connected to arbitrator, autoconnect(0) for client 'Metasploitable2-Linux', connectState(1).
2024-10-09T17:13:07.875Z In(05) vmx USB: Initializing 'Virtual HID' backend
2024-10-09T17:13:07.876Z In(05) sensorThread VTHREAD 19532 "sensorThread"
2024-10-09T17:13:07.876Z In(05) vmx USB: Initializing 'Remote Device' backend
2024-10-09T17:13:07.876Z In(05) vmx RemoteUSBVMX: Retrieved hostId [e4 a0 5e f8 ba 74 21 20-09 27 21 58 15 00 00 00].
2024-10-09T17:13:07.876Z In(05) vmx RemoteUSBVMX: Protocol version min:15 current:19
2024-10-09T17:13:07.876Z In(05) vmx RemoteUSBVMX: no delay setting is TRUE.
2024-10-09T17:13:07.876Z In(05) vmx USB: Initializing 'Virtual Mass Storage' backend
2024-10-09T17:13:07.876Z In(05) vmx USB: Initializing 'Virtual RNG' backend
2024-10-09T17:13:07.876Z In(05) vmx USB: Initializing 'Virtual CCID' backend
2024-10-09T17:13:07.877Z In(05) vmx USB-CCID: Could not establish context: SCARD_E_NO_SERVICE(0x8010001d).
2024-10-09T17:13:07.878Z In(05) vmx USB-CCID: Could not establish context: SCARD_E_NO_SERVICE(0x8010001d).
2024-10-09T17:13:07.880Z In(05) usbCCIDEnumCards VTHREAD 5796 "usbCCIDEnumCards"
2024-10-09T17:13:07.881Z In(05) vmx USB: Initializing 'Virtual Bluetooth' backend
2024-10-09T17:13:07.881Z In(05) vmx USB: Initializing 'Virtual Audio' backend
2024-10-09T17:13:07.881Z In(05) vmx USB: Initializing 'Virtual Video' backend
2024-10-09T17:13:07.881Z In(05) usbCCIDEnumCards USB-CCID: Card enum thread created.
2024-10-09T17:13:07.888Z Wa(03) vmx USBGW: IOCTL_STORAGE_QUERY_PROPERTY failed. Error(0x0): The operation completed successfully.
2024-10-09T17:13:07.888Z In(05) vmx USBGW: Skipping disk backing for path(C:\Users\<USER>\Desktop\Metasploitable2-Linux\Metasploitable.vmx).
2024-10-09T17:13:07.889Z Wa(03) vmx USBGW: IOCTL_STORAGE_QUERY_PROPERTY failed. Error(0x0): The operation completed successfully.
2024-10-09T17:13:07.889Z In(05) vmx USBGW: Skipping disk backing for path(C:\Users\<USER>\Desktop\Metasploitable2-Linux\Metasploitable.vmdk).
2024-10-09T17:13:07.889Z Wa(03) vmx USBGW: IOCTL_STORAGE_QUERY_PROPERTY failed. Error(0x0): The operation completed successfully.
2024-10-09T17:13:07.889Z In(05) vmx USBGW: Skipping disk backing for path(C:\Users\<USER>\Desktop\Metasploitable2-Linux\Metasploitable.vmsd).
2024-10-09T17:13:07.890Z Wa(03) vmx USBGW: IOCTL_STORAGE_QUERY_PROPERTY failed. Error(0x0): The operation completed successfully.
2024-10-09T17:13:07.890Z In(05) vmx USBGW: Skipping disk backing for path(C:\Users\<USER>\Desktop\Metasploitable2-Linux\Metasploitable.nvram).
2024-10-09T17:13:07.891Z In(05) usbCCIDEnumCards USB-CCID: Could not establish context: SCARD_E_NO_SERVICE(0x8010001d).
2024-10-09T17:13:07.892Z In(05) vmx SCSI DEVICE (ide1:0): Computed value of ide1:0.useBounceBuffers: default
2024-10-09T17:13:07.892Z In(05) vmx DISKUTIL: ide1:0 : capacity=0 logical sector size=2048
2024-10-09T17:13:07.892Z In(05) vmx DISKUTIL: ide1:0 : geometry=0/0/0
2024-10-09T17:13:07.892Z In(05) vmx SCSI: scsi0: intr coalescing: on period=50msec cifTh=4 iopsTh=2000 hlt=0
2024-10-09T17:13:07.892Z In(05) vmx SCSI0: UNTAGGED commands will be converted to ORDER tags.
2024-10-09T17:13:07.892Z In(05) vmx SCSI DEVICE (scsi0:0): Computed value of scsi0:0.useBounceBuffers: default
2024-10-09T17:13:07.892Z In(05) vmx DISKUTIL: scsi0:0 : capacity=16777216 logical sector size=512
2024-10-09T17:13:07.892Z In(05) vmx DISKUTIL: scsi0:0 : geometry=1044/255/63
2024-10-09T17:13:07.892Z In(05) vmx SVGA-GFB: Config settings: autodetect=1, numDisplays=1, maxWidth=2560, maxHeight=1600
2024-10-09T17:13:07.892Z In(05) vmx SVGA-GFB: Desired maximum display topology: wh(6688, 5016)
2024-10-09T17:13:07.892Z In(05) vmx SVGA-GFB: Autodetected target gfbSize = 268435456
2024-10-09T17:13:07.892Z In(05) vmx SVGA-GFB: Using Initial       gfbSize = 134217728
2024-10-09T17:13:07.892Z In(05) vmx SVGA-GFB: Max wh(6688, 5016), number of displays: 10
2024-10-09T17:13:07.892Z In(05) vmx SVGA-GFB: Allocated gfbSize=134217728
2024-10-09T17:13:07.893Z No(00) vmx ConfigDB: Setting vmotion.checkpointFBSize = "134217728"
2024-10-09T17:13:07.893Z In(05) vmx SVGA: SVGA DeviceLabel: svga2
2024-10-09T17:13:07.893Z In(05) vmx SVGA: surfaceMemoryLimitKB=786432
2024-10-09T17:13:07.893Z In(05) vmx SVGA: FIFO capabilities 0x0000007f
2024-10-09T17:13:07.893Z In(05) vmx SVGAFeature renderer (before clamping) svga.supports3D bool 0
2024-10-09T17:13:07.893Z In(05) vmx SVGAFeature renderer (before clamping) svga.baseCapsLevel num 11
2024-10-09T17:13:07.893Z In(05) vmx SVGAFeature renderer (before clamping) svga.maxPointSize num 0
2024-10-09T17:13:07.893Z In(05) vmx SVGAFeature renderer (before clamping) svga.maxTextureSize num 32768
2024-10-09T17:13:07.893Z In(05) vmx SVGAFeature renderer (before clamping) svga.maxVolumeExtent num 0
2024-10-09T17:13:07.893Z In(05) vmx SVGAFeature renderer (before clamping) svga.maxTextureAnisotropy num 0
2024-10-09T17:13:07.893Z In(05) vmx SVGAFeature renderer (before clamping) svga.lineStipple bool 0
2024-10-09T17:13:07.893Z In(05) vmx SVGAFeature renderer (before clamping) svga.dxMaxConstantBuffers num 0
2024-10-09T17:13:07.893Z In(05) vmx SVGAFeature renderer (before clamping) svga.dxProvokingVertex bool 0
2024-10-09T17:13:07.893Z In(05) vmx SVGAFeature renderer (before clamping) svga.sm41 bool 0
2024-10-09T17:13:07.893Z In(05) vmx SVGAFeature renderer (before clamping) svga.multisample2x bool 0
2024-10-09T17:13:07.893Z In(05) vmx SVGAFeature renderer (before clamping) svga.multisample4x bool 0
2024-10-09T17:13:07.893Z In(05) vmx SVGAFeature renderer (before clamping) svga.msFullQuality bool 0
2024-10-09T17:13:07.893Z In(05) vmx SVGAFeature renderer (before clamping) svga.logicOps bool 0
2024-10-09T17:13:07.893Z In(05) vmx SVGAFeature renderer (before clamping) svga.bc67 num 0
2024-10-09T17:13:07.893Z In(05) vmx SVGAFeature renderer (before clamping) svga.sm5 bool 0
2024-10-09T17:13:07.893Z In(05) vmx SVGAFeature renderer (before clamping) svga.multisample8x bool 0
2024-10-09T17:13:07.893Z In(05) vmx SVGAFeature renderer (before clamping) svga.logicBlendOps bool 0
2024-10-09T17:13:07.893Z In(05) vmx SVGAFeature renderer (before clamping) svga.maxForcedSampleCount num 0
2024-10-09T17:13:07.893Z In(05) vmx SVGAFeature renderer (before clamping) svga.gl43 bool 0
2024-10-09T17:13:07.893Z In(05) vmx SVGAFeature renderer (after  clamping) svga.supports3D bool 0
2024-10-09T17:13:07.893Z In(05) vmx SVGAFeature renderer (after  clamping) svga.baseCapsLevel num 0
2024-10-09T17:13:07.893Z In(05) vmx SVGAFeature renderer (after  clamping) svga.maxPointSize num 0
2024-10-09T17:13:07.893Z In(05) vmx SVGAFeature renderer (after  clamping) svga.maxTextureSize num 0
2024-10-09T17:13:07.893Z In(05) vmx SVGAFeature renderer (after  clamping) svga.maxVolumeExtent num 0
2024-10-09T17:13:07.893Z In(05) vmx SVGAFeature renderer (after  clamping) svga.maxTextureAnisotropy num 0
2024-10-09T17:13:07.893Z In(05) vmx SVGAFeature renderer (after  clamping) svga.lineStipple bool 0
2024-10-09T17:13:07.893Z In(05) vmx SVGAFeature renderer (after  clamping) svga.dxMaxConstantBuffers num 0
2024-10-09T17:13:07.893Z In(05) vmx SVGAFeature renderer (after  clamping) svga.dxProvokingVertex bool 0
2024-10-09T17:13:07.893Z In(05) vmx SVGAFeature renderer (after  clamping) svga.sm41 bool 0
2024-10-09T17:13:07.893Z In(05) vmx SVGAFeature renderer (after  clamping) svga.multisample2x bool 0
2024-10-09T17:13:07.893Z In(05) vmx SVGAFeature renderer (after  clamping) svga.multisample4x bool 0
2024-10-09T17:13:07.893Z In(05) vmx SVGAFeature renderer (after  clamping) svga.msFullQuality bool 0
2024-10-09T17:13:07.893Z In(05) vmx SVGAFeature renderer (after  clamping) svga.logicOps bool 0
2024-10-09T17:13:07.893Z In(05) vmx SVGAFeature renderer (after  clamping) svga.bc67 num 0
2024-10-09T17:13:07.893Z In(05) vmx SVGAFeature renderer (after  clamping) svga.sm5 bool 0
2024-10-09T17:13:07.893Z In(05) vmx SVGAFeature renderer (after  clamping) svga.multisample8x bool 0
2024-10-09T17:13:07.893Z In(05) vmx SVGAFeature renderer (after  clamping) svga.logicBlendOps bool 0
2024-10-09T17:13:07.893Z In(05) vmx SVGAFeature renderer (after  clamping) svga.maxForcedSampleCount num 0
2024-10-09T17:13:07.894Z In(05) vmx SVGAFeature renderer (after  clamping) svga.gl43 bool 0
2024-10-09T17:13:07.894Z In(05) vmx SVGA3dClamp: Renderer Provides     BC67Level:     0 (    0,     0)
2024-10-09T17:13:07.894Z In(05) vmx SVGA3dClamp: Renderer Provides BaseCapsLevel:     0 (    0,     0)
2024-10-09T17:13:07.894Z In(05) vmx SVGA3dClamp: Renderer Provides    ClampLevel:     0 (    0,     0)
2024-10-09T17:13:07.894Z In(05) vmx SVGA3dCaps: host, at power on (3d disabled)
2024-10-09T17:13:07.894Z In(05) vmx SVGA3dClamp:     Host Provides     BC67Level:     0 (    0,     0)
2024-10-09T17:13:07.894Z In(05) vmx SVGA3dClamp:     Host Provides BaseCapsLevel:     0 (    0,     0)
2024-10-09T17:13:07.894Z In(05) vmx SVGA3dClamp:     Host Provides    ClampLevel:     0 (    0,     0)
2024-10-09T17:13:07.894Z In(05) vmx SVGA3dCaps: Disabling 3d support
2024-10-09T17:13:07.894Z In(05) vmx SVGA3dCaps: guest, compatibility level: 8
2024-10-09T17:13:07.894Z In(05) vmx SVGA3dClamp:    Guest Requires     BC67Level:     0 (    0,     0)
2024-10-09T17:13:07.894Z In(05) vmx SVGA3dClamp:    Guest Requires BaseCapsLevel:     0 (    0,     0)
2024-10-09T17:13:07.894Z In(05) vmx SVGA3dClamp:    Guest Requires    ClampLevel:     0 (    0,     0)
2024-10-09T17:13:07.894Z In(05) vmx USB: Initializing 'UHCI' host controller.
2024-10-09T17:13:07.895Z In(05) vmx Ethernet0 MAC Address: 00:0c:29:1a:02:47
2024-10-09T17:13:07.896Z In(05) vmx Ethernet1 MAC Address: 00:0c:29:1a:02:51
2024-10-09T17:13:07.897Z In(05) vmx USB: Initializing 'EHCI' host controller.
2024-10-09T17:13:07.898Z No(00) vmx ConfigDB: Setting vmci0.id = "363079114"
2024-10-09T17:13:07.907Z In(05) vmx WORKER: Creating new group with maxThreads=1 (36)
2024-10-09T17:13:07.907Z In(05) vmx DISKUTIL: (null) : max toolsVersion = 0, type = 0
2024-10-09T17:13:07.907Z In(05) vmx DISKUTIL: Offline toolsVersion = 0, type = 0
2024-10-09T17:13:07.907Z In(05) vmx TOOLS setting legacy tools version to '0' type 0, manifest status is 9
2024-10-09T17:13:07.908Z In(05) vmx ToolsISO: Refreshing imageName for 'ubuntu' (refreshCount=1, lastCount=1).
2024-10-09T17:13:07.908Z In(05) vmx ToolsISO: open of C:\Program Files (x86)\VMware\VMware Workstation\isoimages_manifest.txt.sig failed: Could not find the file
2024-10-09T17:13:07.908Z In(05) vmx ToolsISO: Unable to read signature file 'C:\Program Files (x86)\VMware\VMware Workstation\isoimages_manifest.txt.sig', ignoring.
2024-10-09T17:13:07.909Z In(05) vmx ToolsISO: Updated cached value for imageName to 'linux.iso'.
2024-10-09T17:13:07.909Z In(05) vmx ToolsISO: Selected Tools ISO 'linux.iso' for 'ubuntu' guest.
2024-10-09T17:13:07.909Z In(05) vmx TOOLS updated cached value for isoImageExists to 1.
2024-10-09T17:13:07.909Z In(05) vmx VMXVmdb_SetToolsVersionStatus: status value set to 'noTools', 'noTools', install possible
2024-10-09T17:13:07.910Z In(05) vmx ToolsISO: Refreshing imageName for 'ubuntu' (refreshCount=1, lastCount=1).
2024-10-09T17:13:07.910Z In(05) vmx ToolsISO: open of C:\Program Files (x86)\VMware\VMware Workstation\isoimages_manifest.txt.sig failed: Could not find the file
2024-10-09T17:13:07.910Z In(05) vmx ToolsISO: Unable to read signature file 'C:\Program Files (x86)\VMware\VMware Workstation\isoimages_manifest.txt.sig', ignoring.
2024-10-09T17:13:07.911Z In(05) vmx ToolsISO: Updated cached value for imageName to 'linux.iso'.
2024-10-09T17:13:07.911Z In(05) vmx ToolsISO: Selected Tools ISO 'linux.iso' for 'ubuntu' guest.
2024-10-09T17:13:07.911Z In(05) vmx TOOLS updated cached value for isoImageExists to 1.
2024-10-09T17:13:07.911Z In(05) vmx VMXVmdb_SetToolsVersionStatus: status value set to 'noTools', 'noTools', install possible
2024-10-09T17:13:07.911Z In(05) vmx Tools: sending 'OS_PowerOn' (state = 3) state change request
2024-10-09T17:13:07.911Z In(05) vmx Tools: Delaying state change request to state 3.
2024-10-09T17:13:07.911Z In(05) vmx ToolsISO: Refreshing imageName for 'ubuntu' (refreshCount=1, lastCount=1).
2024-10-09T17:13:07.911Z In(05) vmx ToolsISO: open of C:\Program Files (x86)\VMware\VMware Workstation\isoimages_manifest.txt.sig failed: Could not find the file
2024-10-09T17:13:07.911Z In(05) vmx ToolsISO: Unable to read signature file 'C:\Program Files (x86)\VMware\VMware Workstation\isoimages_manifest.txt.sig', ignoring.
2024-10-09T17:13:07.913Z In(05) vmx ToolsISO: Updated cached value for imageName to 'linux.iso'.
2024-10-09T17:13:07.913Z In(05) vmx ToolsISO: Selected Tools ISO 'linux.iso' for 'ubuntu' guest.
2024-10-09T17:13:07.913Z In(05) vmx MsgHint: msg.tools.toolsReminder
2024-10-09T17:13:07.913Z In(05)+ vmx Install the VMware Tools package inside this virtual machine. After the guest operating system starts, select VM > Install VMware Tools… and follow the instructions.
2024-10-09T17:13:07.913Z In(05)+ vmx ---------------------------------------
2024-10-09T17:13:07.936Z In(05) vmx TOOLS INSTALL initializing state to IDLE on power on.
2024-10-09T17:13:07.936Z In(05) vmx TOOLS INSTALL updating Rpc handlers registration.
2024-10-09T17:13:07.936Z In(05) vmx TOOLS INSTALL register RPC: upgrader.setGuestFileRoot
2024-10-09T17:13:07.936Z In(05) vmx TOOLS INSTALL register RPC: toolinstall.is_image_inserted
2024-10-09T17:13:07.936Z In(05) vmx TOOLS INSTALL register RPC: toolinstall.installerActive
2024-10-09T17:13:07.936Z In(05) vmx TOOLS INSTALL register RPC: guest.upgrader_send_cmd_line_args
2024-10-09T17:13:07.936Z In(05) vmx P9FS_PowerOn: 9PFS server is not enabled.
2024-10-09T17:13:07.936Z In(05) vmx HgfsServerManagerVigorInit: Initialize: dev api
2024-10-09T17:13:07.937Z In(05) vmx MKSVMX: Copy/paste enabled = 1
2024-10-09T17:13:07.937Z In(05) vmx DEPLOYPKG: No pending deploy package name set
2024-10-09T17:13:07.937Z In(05) vmx DEPLOYPKG: ToolsDeployPkgPublishState: state=0, code=0, message=(null)
2024-10-09T17:13:07.938Z In(05) vmx MonPmc: ctrBase 0x4c1 selBase 0x186/1 PGC 1/1 SMM 1 drain 0 AMD 0
2024-10-09T17:13:07.938Z In(05)+ vmx MonPmc:   gen counters num: 8 width 48 write width 48
2024-10-09T17:13:07.938Z In(05)+ vmx MonPmc:   fix counters num: 4 width 48; version 5
2024-10-09T17:13:07.938Z In(05)+ vmx MonPmc:   unavailable counters: 0xf000000ff
2024-10-09T17:13:07.948Z No(00) vmx ConfigDB: Setting monitor.phys_bits_used = "40"
2024-10-09T17:13:07.948Z In(05) vmx Full guest CPUID with differences from hostCPUID highlighted.
2024-10-09T17:13:07.948Z In(05) vmx guest vs. host CPUID guest vendor: GenuineIntel
2024-10-09T17:13:07.948Z In(05) vmx guest vs. host CPUID guest family: 0x6 model: 0x7e stepping: 0x5
2024-10-09T17:13:07.948Z In(05) vmx guest vs. host CPUID guest codename: Ice Lake-U/Y
2024-10-09T17:13:07.948Z In(05) vmx guest vs. host CPUID guest name: Intel(R) Core(TM) i3-1005G1 CPU @ 1.20GHz
2024-10-09T17:13:07.948Z In(05) vmx guest vs. host CPUID       level eaxIn, ecxIn:        eax        ebx        ecx        edx
2024-10-09T17:13:07.948Z In(05) vmx guest vs. host CPUID guest level 00000000,  0: 0x0000001b 0x756e6547 0x6c65746e 0x49656e69
2024-10-09T17:13:07.948Z In(05) vmx guest vs. host CPUID guest level 00000001,  0: 0x000706e5 0x00010800 0x82d82203 0x0f8bfbff
2024-10-09T17:13:07.948Z In(05) vmx guest vs. host CPUID *host level 00000001,  0: 0x000706e5 0x00100800 0x7ffafbbf 0xbfebfbff
2024-10-09T17:13:07.948Z In(05) vmx guest vs. host CPUID guest level 00000002,  0: 0x00feff01 0x000000f0 0x00000000 0x00000000
2024-10-09T17:13:07.948Z In(05) vmx guest vs. host CPUID guest level 00000004,  0: 0x00000121 0x02c0003f 0x0000003f 0x00000000
2024-10-09T17:13:07.948Z In(05) vmx guest vs. host CPUID *host level 00000004,  0: 0x1c004121 0x02c0003f 0x0000003f 0x00000000
2024-10-09T17:13:07.948Z In(05) vmx guest vs. host CPUID guest level 00000004,  1: 0x00000122 0x01c0003f 0x0000003f 0x00000000
2024-10-09T17:13:07.948Z In(05) vmx guest vs. host CPUID *host level 00000004,  1: 0x1c004122 0x01c0003f 0x0000003f 0x00000000
2024-10-09T17:13:07.948Z In(05) vmx guest vs. host CPUID guest level 00000004,  2: 0x00000143 0x01c0003f 0x000003ff 0x00000000
2024-10-09T17:13:07.948Z In(05) vmx guest vs. host CPUID *host level 00000004,  2: 0x1c004143 0x01c0003f 0x000003ff 0x00000000
2024-10-09T17:13:07.948Z In(05) vmx guest vs. host CPUID guest level 00000004,  3: 0x00000163 0x03c0003f 0x00000fff 0x00000006
2024-10-09T17:13:07.948Z In(05) vmx guest vs. host CPUID *host level 00000004,  3: 0x1c03c163 0x03c0003f 0x00000fff 0x00000006
2024-10-09T17:13:07.948Z In(05) vmx guest vs. host CPUID guest level 00000006,  0: 0x00000004 0x00000000 0x00000000 0x00000000
2024-10-09T17:13:07.948Z In(05) vmx guest vs. host CPUID *host level 00000006,  0: 0x0017aff7 0x00000002 0x00000009 0x00000000
2024-10-09T17:13:07.948Z In(05) vmx guest vs. host CPUID guest level 00000007,  0: 0x00000000 0x00000040 0x00000000 0x00000000
2024-10-09T17:13:07.948Z In(05) vmx guest vs. host CPUID *host level 00000007,  0: 0x00000000 0xf2bf27ef 0x40405f4e 0xbc000410
2024-10-09T17:13:07.948Z In(05) vmx guest vs. host CPUID guest level 0000000a,  0: 0x08300801 0x000000ff 0x0000000f 0x00008000
2024-10-09T17:13:07.948Z In(05) vmx guest vs. host CPUID *host level 0000000a,  0: 0x08300805 0x00000000 0x0000000f 0x00008604
2024-10-09T17:13:07.948Z In(05) vmx guest vs. host CPUID guest level 0000000d,  0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-10-09T17:13:07.948Z In(05) vmx guest vs. host CPUID *host level 0000000d,  0: 0x000002e7 0x00000a80 0x00000a88 0x00000000
2024-10-09T17:13:07.948Z In(05) vmx guest vs. host CPUID guest level 0000000d,  1: 0x00000000 0x00000000 0x00000000 0x00000000
2024-10-09T17:13:07.948Z In(05) vmx guest vs. host CPUID *host level 0000000d,  1: 0x0000000f 0x00000a00 0x00002100 0x00000000
2024-10-09T17:13:07.948Z In(05) vmx guest vs. host CPUID guest level 40000000,  0: 0x40000010 0x61774d56 0x4d566572 0x65726177
2024-10-09T17:13:07.948Z In(05) vmx guest vs. host CPUID guest level 40000010,  0: 0x001229f5 0x000101d0 0x00000000 0x00000000
2024-10-09T17:13:07.948Z In(05) vmx guest vs. host CPUID guest level 80000000,  0: 0x80000008 0x00000000 0x00000000 0x00000000
2024-10-09T17:13:07.948Z In(05) vmx guest vs. host CPUID guest level 80000001,  0: 0x00000000 0x00000000 0x00000101 0x28100800
2024-10-09T17:13:07.948Z In(05) vmx guest vs. host CPUID *host level 80000001,  0: 0x00000000 0x00000000 0x00000121 0x2c100800
2024-10-09T17:13:07.948Z In(05) vmx guest vs. host CPUID guest level 80000002,  0: 0x65746e49 0x2952286c 0x726f4320 0x4d542865
2024-10-09T17:13:07.948Z In(05) vmx guest vs. host CPUID guest level 80000003,  0: 0x33692029 0x3030312d 0x20314735 0x20555043
2024-10-09T17:13:07.948Z In(05) vmx guest vs. host CPUID guest level 80000004,  0: 0x2e312040 0x48473032 0x0000007a 0x00000000
2024-10-09T17:13:07.948Z In(05) vmx guest vs. host CPUID guest level 80000006,  0: 0x00000000 0x00000000 0x01006040 0x00000000
2024-10-09T17:13:07.948Z In(05) vmx guest vs. host CPUID guest level 80000007,  0: 0x00000000 0x00000000 0x00000000 0x00000100
2024-10-09T17:13:07.948Z In(05) vmx guest vs. host CPUID guest level 80000008,  0: 0x00003028 0x00000000 0x00000000 0x00000000
2024-10-09T17:13:07.948Z In(05) vmx guest vs. host CPUID *host level 80000008,  0: 0x00003027 0x00000000 0x00000000 0x00000000
2024-10-09T17:13:07.948Z In(05) vmx Minimum ucode level: 0x000000a6
2024-10-09T17:13:07.948Z In(05) vmx VPMC: events will use hybrid freeze.
2024-10-09T17:13:07.948Z In(05) vmx VPMC: gen counters: num 8 mask 0xffffffffffff
2024-10-09T17:13:07.948Z In(05) vmx VPMC: fix counters: num 0 mask 0; version 1
2024-10-09T17:13:07.948Z In(05) vmx VPMC: hardware counters: 0
2024-10-09T17:13:07.948Z In(05) vmx Guest MSR IA32_ARCH_CAPABILITIES 0x10a = 0x4
2024-10-09T17:13:07.948Z In(05) vmx SVGA-PCI: BAR gfbSize=134217728, fifoSize=8388608
2024-10-09T17:13:07.948Z In(05) vmx SVGA: SVGA_REG_MEMORY_SIZE=939524096
2024-10-09T17:13:07.948Z In(05) vmx SVGA: SVGA_REG_VRAM_SIZE=134217728
2024-10-09T17:13:07.948Z In(05) vmx SVGA: Final Device caps : 0x001f83e2
2024-10-09T17:13:07.948Z In(05) vmx SVGA: Final Device caps2: 0x00000000
2024-10-09T17:13:07.948Z In(05) vmx BusMemSampleSetUpStats: touched: initPct 75 pages 98304 : dirtied: initPct 75 pages 98304
2024-10-09T17:13:07.948Z In(05) vmx MemSched: caller 1 numvm 2 locked pages: num 385733 max 1570816
2024-10-09T17:13:07.948Z In(05) vmx MemSched: locked Page Limit: host 1646014 config 1587200
2024-10-09T17:13:07.948Z In(05) vmx MemSched: minmempct 50  timestamp 142250
2024-10-09T17:13:07.948Z In(05) vmx MemSched: VM 0 min 1064331 max 2112907 shares 2097152 paged 674445 nonpaged 6294 anonymous 9461 locked 384940 touchedPct 75 dirtiedPct 75 timestamp 142249 vmResponsive is 1
2024-10-09T17:13:07.948Z In(05) vmx MemSched: VM 1 min 105888 max 171424 shares 131072 paged 440354 nonpaged 35592 anonymous 4760 locked 793 touchedPct 75 dirtiedPct 75 timestamp 142250 vmResponsive is 1
2024-10-09T17:13:07.948Z In(05) vmx MemSched: locked 793 target 105888 balloon 0 0 0 swapped 0 0 allocd 0 512 state 0 100
2024-10-09T17:13:07.948Z In(05) vmx MemSched: states: 0 1 : 1 0 : 2 0 : 3 0
2024-10-09T17:13:07.948Z In(05) vmx MemSched: Balloon enabled 1 guestType 0 maxSize 0
2024-10-09T17:13:07.948Z In(05) vmx PStrIntern expansion: nBkts=256
2024-10-09T17:13:07.949Z In(05) vmx FeatureCompat: Capabilities:
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.sse3 = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.pclmulqdq = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.mwait = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.vmx = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.ssse3 = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.fma = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.cmpxchg16b = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.pcid = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.sse41 = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.sse42 = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.movbe = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.popcnt = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.aes = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.xsave = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.avx = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.f16c = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.rdrand = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.ds = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.ss = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.fsgsbase = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.bmi1 = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.avx2 = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.smep = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.bmi2 = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.enfstrg = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.invpcid = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.avx512f = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.avx512dq = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.rdseed = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.adx = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.smap = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.avx512ifma = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.clflushopt = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.avx512cd = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.sha = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.avx512bw = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.avx512vl = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.avx512vbmi = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.umip = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.pku = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.avx512vbmi2 = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.gfni = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.vaes = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.vpclmulqdq = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.avx512vnni = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.avx512bitalg = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.avx512vpopcntdq = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.rdpid = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.fast_short_repmov = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.mdclear = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.stibp = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.fcmd = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.ssbd = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.xcr0_master_sse = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.xcr0_master_ymm_h = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.xcr0_master_bndregs = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.xcr0_master_bndcsr = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.xcr0_master_opmask = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.xcr0_master_zmm_h = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.xcr0_master_hi16_zmm = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.xcr0_master_pkru = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.xsaveopt = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.xsavec = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.xgetbv_ecx1 = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.xsaves = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.lahf64 = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.abm = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.3dnprefetch = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.nx = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.pdpe1gb = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.rdtscp = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.lm = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.intel = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.ibrs = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: cpuid.ibpb = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: hv.capable = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: vt.realmode = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: vt.mbx = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: misc.cpuidfaulting = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: vt.advexitinfo = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: vt.eptad = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: vt.ple = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: vt.zeroinstlen = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: misc.rdcl_no = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: misc.ibrs_all = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: misc.rsba_no = 1
2024-10-09T17:13:07.949Z In(05) vmx Capability Found: misc.mds_no = 1
2024-10-09T17:13:07.949Z In(05) vmx FeatureCompat: Requirements:
2024-10-09T17:13:07.949Z In(05) vmx VM Features Required: cpuid.sse3 - Bool:Min:1
2024-10-09T17:13:07.949Z In(05) vmx VM Features Required: cpuid.pclmulqdq - Bool:Min:1
2024-10-09T17:13:07.949Z In(05) vmx VM Features Required: cpuid.ssse3 - Bool:Min:1
2024-10-09T17:13:07.949Z In(05) vmx VM Features Required: cpuid.cmpxchg16b - Bool:Min:1
2024-10-09T17:13:07.949Z In(05) vmx VM Features Required: cpuid.sse41 - Bool:Min:1
2024-10-09T17:13:07.949Z In(05) vmx VM Features Required: cpuid.sse42 - Bool:Min:1
2024-10-09T17:13:07.949Z In(05) vmx VM Features Required: cpuid.movbe - Bool:Min:1
2024-10-09T17:13:07.949Z In(05) vmx VM Features Required: cpuid.popcnt - Bool:Min:1
2024-10-09T17:13:07.949Z In(05) vmx VM Features Required: cpuid.aes - Bool:Min:1
2024-10-09T17:13:07.949Z In(05) vmx VM Features Required: cpuid.ss - Bool:Min:1
2024-10-09T17:13:07.949Z In(05) vmx VM Features Required: cpuid.lahf64 - Bool:Min:1
2024-10-09T17:13:07.949Z In(05) vmx VM Features Required: cpuid.3dnprefetch - Bool:Min:1
2024-10-09T17:13:07.949Z In(05) vmx VM Features Required: cpuid.nx - Bool:Min:1
2024-10-09T17:13:07.949Z In(05) vmx VM Features Required: cpuid.rdtscp - Bool:Min:1
2024-10-09T17:13:07.949Z In(05) vmx VM Features Required: cpuid.lm - Bool:Min:1
2024-10-09T17:13:07.949Z In(05) vmx VM Features Required: cpuid.intel - Bool:Min:1
2024-10-09T17:13:07.950Z In(05) vmx TOOLS received request in VMX to set option 'enableDnD' -> '1'
2024-10-09T17:13:07.952Z In(05) vmx 
2024-10-09T17:13:07.952Z In(05)+ vmx OvhdMem: Static (Power On) Overheads
2024-10-09T17:13:07.952Z In(05) vmx                                                       reserved      |          used
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem excluded                                  cur    max    avg |    cur    max    avg
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdUser_MainMem                    :  131072 131072      - |      0      0      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdUser_VmxText                    :    7680   7680      - |      0      0      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdUser_VmxTextLibs                :   17408  17408      - |      0      0      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem Total excluded                      :  156160 156160      - |      -      -      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem Actual maximum                      :         156160        |             -
2024-10-09T17:13:07.952Z In(05)+ vmx 
2024-10-09T17:13:07.952Z In(05) vmx                                                       reserved      |          used
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem paged                                     cur    max    avg |    cur    max    avg
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdUser_STATS_vmm                  :       2      2      - |      0      0      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdUser_STATS_device               :       1      1      - |      0      0      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdUser_DiskLibMemUsed             :    3075   3075      - |      0      0      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdUser_SvgaSDirtyCache            :      96     96      - |      0      0      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdUser_SvgaCursor                 :      10     10      - |     10     10      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdUser_VmxGlobals                 :   10240  10240      - |      0      0      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdUser_VmxGlobalsLibs             :    3584   3584      - |      0      0      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdUser_VmxHeap                    :    8704   8704      - |      0      0      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdUser_VmxMks                     :      33     33      - |      0      0      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdUser_VmxMksRenderOps            :     678    678      - |    492    492      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdUser_VmxMks3d                   :  229376 229376      - |      0      0      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdUser_VmxMksScreenTemp           :   37122  37122      - |      0      0      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdUser_VmxMksVnc                  :   38236  38236      - |      0      0      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdUser_VmxMksScreen               :   65539  65539      - |      0      0      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdUser_VmxMksSVGAVO               :    4096   4096      - |      0      0      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdUser_VmxMksSwbCursor            :    4096   4096      - |      0      0      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdUser_VmxPhysMemErrPages         :      10     10      - |      0      0      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdUser_VmxSLEntryBuf              :     128    128      - |      0      0      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdUser_VmxThreads                 :   35328  35328      - |      0      0      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem Total paged                         :  440354 440354      - |    502    502      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem Actual maximum                      :         440354        |        440354
2024-10-09T17:13:07.952Z In(05)+ vmx 
2024-10-09T17:13:07.952Z In(05) vmx                                                       reserved      |          used
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem nonpaged                                  cur    max    avg |    cur    max    avg
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdUser_SharedArea                 :     137    137      - |    122    122      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdUser_BusMemTraceBitmap          :      10     10      - |      0      0      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdUser_PFrame                     :     498   1536      - |    498    498      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdUser_VIDE_KSEG                  :      16     16      - |     16     16      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdUser_VGA                        :      64     64      - |     64     64      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdUser_BalloonMPN                 :       1      1      - |      1      1      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdUser_P2MUpdateBuffer            :       3      3      - |      0      0      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdUser_ServicesMPN                :       3      3      - |      2      2      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdUser_LocalApic                  :       1      1      - |      1      1      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdUser_VBIOS                      :       8      8      - |      0      0      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdUser_VnicGuest                  :      32     32      - |      0      0      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdUser_LSIBIOS                    :       4      4      - |      0      0      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdUser_LSIRings                   :       4      4      - |      0      0      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdUser_SAS1068BIOS                :       4      4      - |      0      0      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdUser_SBIOS                      :      16     16      - |      0      0      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdUser_FlashRam                   :     128    128      - |      0      0      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdUser_SVGAFB                     :   32768  32768      - |      0      0      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdUser_SVGAMEM                    :     512    512      - |      0      0      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdUser_HDAudioReg                 :       3      3      - |      0      0      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdUser_EHCIRegister               :       1      1      - |      1      1      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdUser_XhciRegister               :       1      1      - |      0      0      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdUser_HyperV                     :       2      2      - |      0      0      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdUser_ExtCfg                     :       4      4      - |      0      0      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdUser_vhvCachedVMCS              :       1      1      - |      0      0      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdUser_vhvNestedAPIC              :       1      1      - |      0      0      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdUser_LBR                        :       1      1      - |      0      0      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdUser_MonWired                   :      35     35      - |     35     35      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdUser_MonNuma                    :     225    225      - |      0      0      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdUser_NVDC                       :       1      1      - |      0      0      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdUser_PCIeMMIOArea               :      70     70      - |     34     34      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem Total nonpaged                      :   34554  35592      - |    774    774      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem Actual maximum                      :          34554        |         35592
2024-10-09T17:13:07.952Z In(05)+ vmx 
2024-10-09T17:13:07.952Z In(05) vmx                                                       reserved      |          used
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem anonymous                                 cur    max    avg |    cur    max    avg
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdMon_Alloc                       :      98     98      - |      0      0      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdMon_BusMemFrame                 :     225    282      - |      0      0      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdMon_BusMem2MInfo                :       2      2      - |      0      0      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdMon_BusMem1GInfo                :       1      1      - |      0      0      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdMon_BusMemZapListMPN            :       1      1      - |      0      0      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdMon_BusMemPreval                :       4      4      - |      0      0      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdMon_MonAS                       :       1      1      - |      0      0      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdMon_GuestMem                    :      40     40      - |      0      0      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdMon_TC                          :     513    513      - |      0      0      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdMon_BusMemMonAS                 :       4      4      - |      0      0      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdMon_PlatformMonAS               :       6      6      - |      0      0      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdMon_HVNuma                      :       2      2      - |      0      0      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdMon_HV                          :       1      1      - |      0      0      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdMon_HVMSRBitmap                 :       1      1      - |      0      0      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdMon_VHVGuestMSRBitmap           :       1      1      - |      0      0      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdMon_VHV                         :       3      3      - |      0      0      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdMon_Numa                        :      24     24      - |      0      0      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdMon_NumaTextRodata              :     198    198      - |      0      0      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdMon_NumaDataBss                 :      27     27      - |      0      0      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdMon_BaseWired                   :      29     29      - |      0      0      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdMon_Bootstrap                   :    2303   2303      - |      0      0      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdMon_GPhysTraced                 :     264    264      - |      0      0      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdMon_GPhysHWMMU                  :     580    580      - |      0      0      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdMon_GPhysNoTrace                :     264    264      - |      0      0      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdMon_PhysMemGart                 :     104    104      - |      0      0      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdMon_PhysMemErr                  :       6      6      - |      0      0      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem OvhdMon_VProbe                      :       1      1      - |      0      0      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem Total anonymous                     :    4703   4760      - |      0      0      -
2024-10-09T17:13:07.952Z In(05) vmx OvhdMem Actual maximum                      :           4703        |          4760
2024-10-09T17:13:07.952Z In(05)+ vmx 
2024-10-09T17:13:07.952Z In(05) vmx VMMEM: Precise Reservation: 1873MB (MainMem=512MB)
2024-10-09T17:13:07.952Z In(05) vmx VMXSTATS: Registering 48 stats: vmx.overheadMemSize
2024-10-09T17:13:07.953Z In(05) vmx Vix: [mainDispatch.c:1058]: VMAutomation_PowerOn. Powering on.
2024-10-09T17:13:07.953Z In(05) vmx VMX_PowerOn: ModuleTable_PowerOn = 1
2024-10-09T17:13:07.953Z No(00) vmx ConfigDB: Setting cleanShutdown = "FALSE"
2024-10-09T17:13:07.953Z No(00) vmx ConfigDB: Setting softPowerOff = "FALSE"
2024-10-09T17:13:07.957Z In(05) vcpu-0 VTHREAD 19592 "vcpu-0"
2024-10-09T17:13:07.961Z In(05) vcpu-0 MonTimer APIC:0/0 vec: 0
2024-10-09T17:13:07.961Z In(05) vcpu-0 APIC: version = 0x15, max LVT = 6, LDR = 0x8000000, DFR = 0xffffffff
2024-10-09T17:13:07.961Z In(05) vcpu-0 Active HV capabilities
2024-10-09T17:13:07.961Z In(05) vcpu-0    Virtual interrupt delivery
2024-10-09T17:13:07.961Z In(05) vcpu-0    XAPIC MMIO virtualization
2024-10-09T17:13:07.961Z In(05) vcpu-0    Full decode
2024-10-09T17:13:07.961Z In(05) vcpu-0    Nested paging A/D bits
2024-10-09T17:13:07.961Z In(05) vcpu-0    Real-address mode
2024-10-09T17:13:07.961Z In(05) vcpu-0    Skip debug state
2024-10-09T17:13:07.961Z In(05) vcpu-0    X2APIC virtualization
2024-10-09T17:13:07.961Z In(05) vcpu-0    TPR MMIO virtualization
2024-10-09T17:13:07.961Z In(05) vcpu-0    Page-modification logging
2024-10-09T17:13:07.961Z In(05) vcpu-0    ENCLS exiting
2024-10-09T17:13:07.961Z In(05) vcpu-0    PAUSE-loop exiting
2024-10-09T17:13:07.961Z In(05) vcpu-0    TSC scaling
2024-10-09T17:13:07.961Z In(05) vcpu-0    Advanced exit information for EPT violations
2024-10-09T17:13:07.961Z In(05) vcpu-0    Mode-based execute control for nested paging
2024-10-09T17:13:07.961Z In(05) vcpu-0    EPT-violation virtualization exception
2024-10-09T17:13:07.961Z In(05) vcpu-0    Event injection with instruction length zero
2024-10-09T17:13:07.962Z In(05) vcpu-0 TSC scaling ratio: 0001_000000000000 (mult=2147483648, shift=31)
2024-10-09T17:13:07.962Z In(05) vcpu-0 CPU reset: hard (mode Emulation)
2024-10-09T17:13:07.964Z In(05) vcpu-0 GuestRpc: Successfully created RPCI listening socket.
2024-10-09T17:13:07.964Z In(05) vcpu-0 GuestRpc: Using vsocket for TCLO messaging is disabled.
2024-10-09T17:13:07.964Z In(05) vcpu-0 memoryHotplug: Node 0: Present: 511 MB (100 %) Size:511 MB (100 %)
2024-10-09T17:13:07.964Z In(05) vcpu-0 PIIX4: PM Resuming from suspend type 0x0, chipset.onlineStandby 0
2024-10-09T17:13:07.965Z In(05) vcpu-0 VNET: MACVNetConnectToNetwork 'Ethernet1' notify available.
2024-10-09T17:13:07.966Z In(05) vcpu-0 VNET: 'ethernet0' enable link state propagation, lsp.state = 5
2024-10-09T17:13:07.967Z In(05) vcpu-0 VNET: MACVNetConnectToNetwork 'ethernet0' lsp.state = 4
2024-10-09T17:13:07.967Z In(05) vcpu-0 VNET: MACVNetConnectToNetwork 'Ethernet0' notify available.
2024-10-09T17:13:07.968Z In(05) vcpu-0 HGFSPublish: publishing 0 shares
2024-10-09T17:13:07.970Z In(05) vcpu-0 Win32U_GetFileAttributes: GetFileAttributesExW("C:\Users\<USER>\Desktop\Metasploitable2-Linux\Metasploitable.vmpl", ...) failed, error: 2
2024-10-09T17:13:07.970Z In(05) vcpu-0 PolicyVMXFindPolicyKey: policy file does not exist.
2024-10-09T17:13:07.973Z In(05) vcpu-0 DEVSWAP: GuestOS does not require LSI adapter swap.
2024-10-09T17:13:07.973Z In(05) vcpu-0 VMXSTATS: Registering 49 stats: vmx.vigor.opsTotal
2024-10-09T17:13:07.973Z In(05) vcpu-0 VMXSTATS: Registering 50 stats: vmx.vigor.opsPerS
2024-10-09T17:13:07.973Z In(05) vcpu-0 VMXSTATS: Registering 51 stats: vmx.vigor.queriesPerS
2024-10-09T17:13:07.973Z In(05) vcpu-0 VMXSTATS: Registering 52 stats: vmx.poll.itersPerS
2024-10-09T17:13:07.973Z In(05) vcpu-0 VMXSTATS: Registering 53 stats: vmx.userRpc.opsPerS
2024-10-09T17:13:07.973Z In(05) vcpu-0 VMXSTATS: Registering 54 stats: vmx.metrics.lastUpdate
2024-10-09T17:13:07.973Z No(00) vcpu-0 Metrics lastUpdate (s): 2405452
2024-10-09T17:13:07.973Z In(05) vcpu-0 Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=0, newAppState=1872, success=1 additionalError=0
2024-10-09T17:13:07.973Z In(05) vcpu-0 Vix: [mainDispatch.c:4129]: VMAutomationReportPowerStateChange: Reporting power state change (opcode=0, err=0).
2024-10-09T17:13:07.973Z In(05) vcpu-0 Vix: [mainDispatch.c:4129]: VMAutomationReportPowerStateChange: Reporting power state change (opcode=2, err=0).
2024-10-09T17:13:07.973Z In(05) vcpu-0 Transitioned vmx/execState/val to poweredOn
2024-10-09T17:13:07.973Z In(05) vcpu-0 Tools: Adding Tools inactivity timer.
2024-10-09T17:13:07.973Z In(05) vcpu-0 Intel VT: FlexPriority enabled, VPID enabled.
2024-10-09T17:13:07.975Z In(05) vmx USB: New set of 3 USB devices.
2024-10-09T17:13:07.975Z In(05) vmx USB: Found device [name:Bison\ Integrated\ Camera vid:5986 pid:1135 path:1/0/4 speed:high family:video instanceId:USB\\VID_5986&PID_1135\\************ serialnum:************ arbRuntimeKey:2 version:5]
2024-10-09T17:13:07.975Z In(05) vmx USB: Found device [name:Intel(R)\ Wireless\ Bluetooth(R) vid:8087 pid:0026 path:1/0/9 speed:full family:wireless,bluetooth instanceId:USB\\VID_8087&PID_0026\\5&2FFBEC60&0&10 arbRuntimeKey:3 version:5]
2024-10-09T17:13:07.975Z In(05) vmx USB: Found device [name:Virtual\ Bluetooth\ Adapter vid:0e0f pid:0008 speed:full family:wireless,bluetooth deviceType:virtual-bluetooth info:0000001 version:5]
2024-10-09T17:13:07.976Z In(05) vmx VNET: MACVNetLinkStateEventHandler: event, up:1, adapter:0
2024-10-09T17:13:07.976Z In(05) vmx VNET: MACVNetLinkStateEventHandler: 'ethernet0' state from 4 to 6.
2024-10-09T17:13:08.036Z In(05) mks MKSControlMgr: connected
2024-10-09T17:13:08.046Z In(05) svga SWBScreen: Screen 0 Defined: xywh(0, 0, 640, 480) flags=0x3
2024-10-09T17:13:08.050Z In(05) mks MKS-VMDB: VMDB requested a screenshot
2024-10-09T17:13:08.053Z In(05) svga MKSScreenShotMgr: Taking a screenshot
2024-10-09T17:13:08.079Z In(05) mks SWBWindow: Number of MKSWindows changed: 0 rendering MKSWindow(s) of total 1.
2024-10-09T17:13:08.079Z In(05) mks SWBWindow: Window 1 Defined: src screenId=-1, src xywh(0, 0, 640, 480) dest xywh(0, 0, 640, 480) pixelScale=1, flags=0x10
2024-10-09T17:13:08.079Z In(05) mks GDI-Backend: successfully started by HWinMux to do window composition.
2024-10-09T17:13:08.082Z In(05) mks MKS-HWinMux: Started GDI presentation backend.
2024-10-09T17:13:08.097Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 2.
2024-10-09T17:13:08.097Z In(05) mks SWBWindow: Window 0 Defined: src screenId=-1, src xywh(0, 0, 640, 480) dest xywh(0, 0, 640, 480) pixelScale=1, flags=0xD
2024-10-09T17:13:08.242Z In(05) vcpu-0 SVGA: Registering MemSpace at 0xf0000000(0x0) and 0xfe000000(0x0)
2024-10-09T17:13:08.242Z In(05) vcpu-0 SVGA: FIFO is already mapped
2024-10-09T17:13:08.247Z In(05) vcpu-0 SVGA: Unregistering MemSpace at 0xf0000000(0xf0000000) and 0xfe000000(0xfe000000)
2024-10-09T17:13:08.247Z In(05) vcpu-0 SVGA: FIFO is already mapped
2024-10-09T17:13:08.399Z In(05) vcpu-0 SVGA: Registering MemSpace at 0xf0000000(0xf0000000) and 0xfe000000(0xfe000000)
2024-10-09T17:13:08.399Z In(05) vcpu-0 SVGA: FIFO is already mapped
2024-10-09T17:13:08.404Z In(05) vcpu-0 SVGA: Unregistering MemSpace at 0xf0000000(0xf0000000) and 0xfe000000(0xfe000000)
2024-10-09T17:13:08.404Z In(05) vcpu-0 SVGA: FIFO is already mapped
2024-10-09T17:13:08.404Z In(05) vcpu-0 SVGA: Registering IOSpace at 0x1070
2024-10-09T17:13:08.404Z In(05) vcpu-0 SVGA: Registering MemSpace at 0xf0000000(0xf0000000) and 0xfe000000(0xfe000000)
2024-10-09T17:13:08.404Z In(05) vcpu-0 SVGA: FIFO is already mapped
2024-10-09T17:13:08.407Z In(05) vmx VNET: MACVNetLinkStateTimerHandler: 'ethernet0' state from 6 to 1.
2024-10-09T17:13:08.438Z In(05) vcpu-0 SVGA: FIFO is already mapped
2024-10-09T17:13:08.455Z In(05) vcpu-0 SVGA: FIFO is already mapped
2024-10-09T17:13:08.582Z In(05) vcpu-0 DISKUTIL: scsi0:0 : geometry=1044/255/63
2024-10-09T17:13:08.582Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=16777216 logical sector size=512
2024-10-09T17:13:08.802Z In(05) vcpu-0 BIOS-UUID is 56 4d a7 b6 49 eb 2d eb-7b 81 20 17 1a 1a 02 47
2024-10-09T17:13:08.879Z In(05) vmx USB: New set of 3 USB devices.
2024-10-09T17:13:08.879Z In(05) vmx USB: Found device [name:Bison\ Integrated\ Camera vid:5986 pid:1135 path:1/0/4 speed:high family:video instanceId:USB\\VID_5986&PID_1135\\************ serialnum:************ arbRuntimeKey:2 version:5]
2024-10-09T17:13:08.879Z In(05) vmx USB: Found device [name:Intel(R)\ Wireless\ Bluetooth(R) vid:8087 pid:0026 path:1/0/9 speed:full family:wireless,bluetooth instanceId:USB\\VID_8087&PID_0026\\5&2FFBEC60&0&10 arbRuntimeKey:3 version:5]
2024-10-09T17:13:08.879Z In(05) vmx USB: Found device [name:Virtual\ Bluetooth\ Adapter vid:0e0f pid:0008 speed:full family:wireless,bluetooth deviceType:virtual-bluetooth info:0000001 version:5]
2024-10-09T17:13:08.889Z In(05) svga SWBScreen: Screen 0 Resized: xywh(0, 0, 720, 400) flags=0x3
2024-10-09T17:13:18.979Z In(05) vcpu-0 SVGA: Unregistering IOSpace at 0x1070
2024-10-09T17:13:18.979Z In(05) vcpu-0 SVGA: Registering IOSpace at 0xfffffff0
2024-10-09T17:13:18.979Z In(05) vcpu-0 SVGA: Unregistering IOSpace at 0xfffffff0
2024-10-09T17:13:18.979Z In(05) vcpu-0 SVGA: Registering IOSpace at 0x1070
2024-10-09T17:13:18.979Z In(05) vcpu-0 SVGA: Unregistering MemSpace at 0xf0000000(0xf0000000) and 0xfe000000(0xfe000000)
2024-10-09T17:13:18.979Z In(05) vcpu-0 SVGA: FIFO is already mapped
2024-10-09T17:13:18.979Z In(05) vcpu-0 SVGA: Registering MemSpace at 0xf0000000(0xf0000000) and 0xfe000000(0xfe000000)
2024-10-09T17:13:18.979Z In(05) vcpu-0 SVGA: FIFO is already mapped
2024-10-09T17:13:18.979Z In(05) vcpu-0 SVGA: Unregistering MemSpace at 0xf0000000(0xf0000000) and 0xfe000000(0xfe000000)
2024-10-09T17:13:18.979Z In(05) vcpu-0 SVGA: FIFO is already mapped
2024-10-09T17:13:18.979Z In(05) vcpu-0 SVGA: Registering MemSpace at 0xf0000000(0xf0000000) and 0xfe000000(0xfe000000)
2024-10-09T17:13:18.979Z In(05) vcpu-0 SVGA: FIFO is already mapped
2024-10-09T17:13:21.526Z In(05) vmx VNET: MACVNetLinkStateTimerHandler: 'ethernet0' state from 1 to 5.
2024-10-09T17:13:24.619Z In(05) vcpu-0 SCSI0: RESET BUS
2024-10-09T17:13:24.670Z In(05) mks MKS-VMDB: VMDB requested a screenshot
2024-10-09T17:13:24.670Z In(05) svga MKSScreenShotMgr: Taking a screenshot
2024-10-09T17:13:24.694Z In(05) mks SWBWindow: Number of MKSWindows changed: 0 rendering MKSWindow(s) of total 2.
2024-10-09T17:13:24.702Z In(05) mks SWBWindow: Window #0 validation failed: no valid host window or host surface.
2024-10-09T17:13:24.703Z In(05) mks SWBVmdb: Destroy SWB Window Id #0 because an invalid MKSWindow definition is received from UI over VMDB.
2024-10-09T17:13:24.703Z In(05) mks SWBWindow: Window 0 Destroyed: src screenId=-1, src xywh(0, 0, 720, 400) dest xywh(0, 0, 720, 400) pixelScale=1, flags=0x7
2024-10-09T17:13:24.703Z In(05) mks SWBWindow: Number of MKSWindows changed: 0 rendering MKSWindow(s) of total 1.
2024-10-09T17:13:24.703Z In(05) mks SWBWindow: Window #1 validation failed: no valid host window or host surface.
2024-10-09T17:13:24.703Z In(05) mks SWBVmdb: Destroy SWB Window Id #1 because an invalid MKSWindow definition is received from UI over VMDB.
2024-10-09T17:13:24.703Z In(05) mks SWBWindow: Window 1 Destroyed: src screenId=-1, src xywh(0, 0, 720, 400) dest xywh(0, 0, 720, 400) pixelScale=1, flags=0x10
2024-10-09T17:13:24.703Z In(05) mks GDI-Backend: stopped by HWinMux to do window composition.
2024-10-09T17:13:24.703Z In(05) mks SWBWindow: Number of MKSWindows changed: 0 rendering MKSWindow(s) of total 0.
2024-10-09T17:13:25.431Z In(05) vcpu-0 SCSI0: RESET BUS
2024-10-09T17:13:25.487Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=16777216 logical sector size=512
2024-10-09T17:13:25.487Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-10-09T17:13:25.488Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=16777216 logical sector size=512
2024-10-09T17:13:25.488Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-10-09T17:13:26.110Z In(05) vcpu-0 DDB: "longContentID" = "01f51d245caf5a9f7aa37c6340cf6be1" (was "fc199de5e9db8bcaacb8b54bc4c5ed51")
2024-10-09T17:13:30.665Z In(05) vcpu-0 VLANCE: Returning 0x0 for LANCE_EXTINT IN
2024-10-09T17:13:30.665Z In(05) vcpu-0 VLANCE: Ignoring LANCE_EXTINT OUT of 0x1
2024-10-09T17:13:30.946Z In(05) vcpu-0 VLANCE: IN on LANCE_MODE while not stopped: 0x73
2024-10-09T17:13:30.946Z In(05) vcpu-0 VLANCE: OUT on LANCE_MODE while not stopped: 0x73, word: 0x0
2024-10-09T17:13:30.946Z In(05) vcpu-0 VLANCE: OUT on LANCE_LADRF0 while not stopped: 0x73, word: 0x0
2024-10-09T17:13:30.946Z In(05) vcpu-0 VLANCE: OUT on LANCE_LADRF1 while not stopped: 0x73, word: 0x0
2024-10-09T17:13:30.946Z In(05) vcpu-0 VLANCE: OUT on LANCE_LADRF2 while not stopped: 0x73, word: 0x0
2024-10-09T17:13:30.946Z In(05) vcpu-0 VLANCE: OUT on LANCE_LADRF3 while not stopped: 0x73, word: 0x0
2024-10-09T17:16:08.080Z In(05) vmx GuestRpcSendTimedOut: message to toolbox timed out.
2024-10-09T17:16:08.080Z In(05) vmx Vix: [guestCommands.c:1943]: Error VIX_E_TOOLS_NOT_RUNNING in VMAutomationTranslateGuestRpcError(): VMware Tools are not running in the guest
2024-10-09T17:20:41.882Z In(05) vmx SOCKET 4 (2436) recv error 10054: An existing connection was forcibly closed by the remote host
2024-10-09T17:20:41.882Z In(05) vmx Vix: [mainDispatch.c:2813]: VMAutomation: Connection Error (1) on connection 0.
2024-10-09T17:20:41.882Z Wa(03) mks SOCKET 5 (-1) AsyncNamedPipeRecvCallback: failed to get overlapped result: 109.
2024-10-09T17:20:41.882Z In(05) mks MKSControlMgr: MKSControl Remote Disconnect: socket closed.
2024-10-09T17:20:41.882Z Wa(03) mks MKSResponse: Error: (1084)
2024-10-09T17:20:41.882Z In(05) mks MKSControlMgr: MKSResponse error
2024-10-09T17:20:41.882Z In(05) mks MKSControlMgr: Dropping MKSControl error due to prior unresolved error.
2024-10-09T17:20:41.882Z Wa(03) mks MKSControlMgr: MKSControl Error: Disconnecting from UI
2024-10-09T17:20:41.883Z In(05) vmx VmdbPipeStreamsOvlError Couldn't read: (109) The pipe has been ended.
2024-10-09T17:20:41.883Z In(05) vmx VmdbCnxDisconnect: Disconnect: closed pipe for pub cnx '/db/connection/#1/' (-32)
2024-10-09T17:20:41.883Z In(05) mks MKSControlMgr: disconnected
2024-10-09T17:20:41.884Z In(05) vmx VmdbDbRemoveCnx: Removing Cnx from Db for '/db/connection/#1/'
2024-10-09T17:20:46.315Z In(05) PowerNotifyThread Suspending VM from WM_ENDSESSION: Metasploitable2-Linux
2024-10-09T17:20:46.316Z In(05) vmx SUSPEND: Start suspend (flags=0)
2024-10-09T17:20:46.318Z In(05) svga MKSScreenShotMgr: Taking a screenshot
2024-10-09T17:20:46.323Z In(05) vcpu-0 Closing all the disks of the VM.
2024-10-09T17:20:46.323Z In(05) vcpu-0 Closing disk 'scsi0:0'
2024-10-09T17:20:46.326Z In(05) vcpu-0 Progress -1% (msg.checkpoint.saveStatus)
2024-10-09T17:20:46.326Z In(05) vcpu-0 Checkpointed in VMware Workstation, 17.5.2, build-23775571, Windows Host
2024-10-09T17:20:46.328Z In(05) vcpu-0 Win32U_GetFileAttributes: GetFileAttributesExW("C:\Users\<USER>\Desktop\Metasploitable2-Linux\Metasploitable-8f9ae34c.vmem", ...) failed, error: 2
2024-10-09T17:20:46.330Z In(05) vcpu-0 MainMem: Keep paging file 'C:\Users\<USER>\Desktop\Metasploitable2-Linux\Metasploitable-8f9ae34c.vmem' as memory image.
2024-10-09T17:20:46.330Z In(05) vcpu-0 Progress (no connection) 0% (none)
2024-10-09T17:20:46.331Z In(05) vcpu-0 Progress (no connection) 1% (none)
2024-10-09T17:20:46.332Z In(05) vcpu-0 Progress (no connection) 2% (none)
2024-10-09T17:20:46.332Z In(05) vcpu-0 Progress (no connection) 3% (none)
2024-10-09T17:20:46.333Z In(05) vcpu-0 Progress (no connection) 4% (none)
2024-10-09T17:20:46.333Z In(05) vcpu-0 Progress (no connection) 5% (none)
2024-10-09T17:20:46.333Z In(05) vcpu-0 Progress (no connection) 6% (none)
2024-10-09T17:20:46.333Z In(05) vcpu-0 Progress (no connection) 7% (none)
2024-10-09T17:20:46.333Z In(05) vcpu-0 Progress (no connection) 8% (none)
2024-10-09T17:20:46.333Z In(05) vcpu-0 Progress (no connection) 9% (none)
2024-10-09T17:20:46.333Z In(05) vcpu-0 Progress (no connection) 10% (none)
2024-10-09T17:20:46.333Z In(05) vcpu-0 Progress (no connection) 11% (none)
2024-10-09T17:20:46.334Z In(05) vcpu-0 Progress (no connection) 12% (none)
2024-10-09T17:20:46.334Z In(05) vcpu-0 Progress (no connection) 13% (none)
2024-10-09T17:20:46.334Z In(05) vcpu-0 Progress (no connection) 14% (none)
2024-10-09T17:20:46.334Z In(05) vcpu-0 Progress (no connection) 15% (none)
2024-10-09T17:20:46.334Z In(05) vcpu-0 Progress (no connection) 16% (none)
2024-10-09T17:20:46.334Z In(05) vcpu-0 Progress (no connection) 17% (none)
2024-10-09T17:20:46.334Z In(05) vcpu-0 Progress (no connection) 18% (none)
2024-10-09T17:20:46.334Z In(05) vcpu-0 Progress (no connection) 19% (none)
2024-10-09T17:20:46.334Z In(05) vcpu-0 Progress (no connection) 20% (none)
2024-10-09T17:20:46.334Z In(05) vcpu-0 Progress (no connection) 21% (none)
2024-10-09T17:20:46.334Z In(05) vcpu-0 Progress (no connection) 22% (none)
2024-10-09T17:20:46.334Z In(05) vcpu-0 Progress (no connection) 23% (none)
2024-10-09T17:20:46.334Z In(05) vcpu-0 Progress (no connection) 24% (none)
2024-10-09T17:20:46.334Z In(05) vcpu-0 Progress (no connection) 25% (none)
2024-10-09T17:20:46.334Z In(05) vcpu-0 Progress (no connection) 26% (none)
2024-10-09T17:20:46.334Z In(05) vcpu-0 Progress (no connection) 27% (none)
2024-10-09T17:20:46.334Z In(05) vcpu-0 Progress (no connection) 28% (none)
2024-10-09T17:20:46.334Z In(05) vcpu-0 Progress (no connection) 29% (none)
2024-10-09T17:20:46.334Z In(05) vcpu-0 Progress (no connection) 30% (none)
2024-10-09T17:20:46.334Z In(05) vcpu-0 Progress (no connection) 31% (none)
2024-10-09T17:20:46.334Z In(05) vcpu-0 Progress (no connection) 32% (none)
2024-10-09T17:20:46.334Z In(05) vcpu-0 Progress (no connection) 33% (none)
2024-10-09T17:20:46.334Z In(05) vcpu-0 Progress (no connection) 34% (none)
2024-10-09T17:20:46.334Z In(05) vcpu-0 Progress (no connection) 35% (none)
2024-10-09T17:20:46.334Z In(05) vcpu-0 Progress (no connection) 36% (none)
2024-10-09T17:20:46.334Z In(05) vcpu-0 Progress (no connection) 37% (none)
2024-10-09T17:20:46.334Z In(05) vcpu-0 Progress (no connection) 38% (none)
2024-10-09T17:20:46.334Z In(05) vcpu-0 Progress (no connection) 39% (none)
2024-10-09T17:20:46.334Z In(05) vcpu-0 Progress (no connection) 40% (none)
2024-10-09T17:20:46.334Z In(05) vcpu-0 Progress (no connection) 41% (none)
2024-10-09T17:20:46.335Z In(05) vcpu-0 Progress (no connection) 42% (none)
2024-10-09T17:20:46.336Z In(05) vcpu-0 Progress (no connection) 43% (none)
2024-10-09T17:20:46.337Z In(05) vcpu-0 Progress (no connection) 44% (none)
2024-10-09T17:20:46.338Z In(05) vcpu-0 Progress (no connection) 45% (none)
2024-10-09T17:20:46.339Z In(05) vcpu-0 Progress (no connection) 46% (none)
2024-10-09T17:20:46.340Z In(05) vcpu-0 Progress (no connection) 47% (none)
2024-10-09T17:20:46.341Z In(05) vcpu-0 Progress (no connection) 48% (none)
2024-10-09T17:20:46.341Z In(05) vcpu-0 Progress (no connection) 49% (none)
2024-10-09T17:20:46.342Z In(05) vcpu-0 Progress (no connection) 50% (none)
2024-10-09T17:20:46.343Z In(05) vcpu-0 Progress (no connection) 51% (none)
2024-10-09T17:20:46.344Z In(05) vcpu-0 Progress (no connection) 52% (none)
2024-10-09T17:20:46.345Z In(05) vcpu-0 Progress (no connection) 53% (none)
2024-10-09T17:20:46.345Z In(05) vcpu-0 Progress (no connection) 54% (none)
2024-10-09T17:20:46.346Z In(05) vcpu-0 Progress (no connection) 55% (none)
2024-10-09T17:20:46.347Z In(05) vcpu-0 Progress (no connection) 56% (none)
2024-10-09T17:20:46.348Z In(05) vcpu-0 Progress (no connection) 57% (none)
2024-10-09T17:20:46.348Z In(05) vcpu-0 Progress (no connection) 58% (none)
2024-10-09T17:20:46.349Z In(05) vcpu-0 Progress (no connection) 59% (none)
2024-10-09T17:20:46.349Z In(05) vcpu-0 Progress (no connection) 60% (none)
2024-10-09T17:20:46.350Z In(05) vcpu-0 Progress (no connection) 61% (none)
2024-10-09T17:20:46.350Z In(05) vcpu-0 Progress (no connection) 62% (none)
2024-10-09T17:20:46.350Z In(05) vcpu-0 Progress (no connection) 63% (none)
2024-10-09T17:20:46.350Z In(05) vcpu-0 Progress (no connection) 64% (none)
2024-10-09T17:20:46.350Z In(05) vcpu-0 Progress (no connection) 65% (none)
2024-10-09T17:20:46.351Z In(05) vcpu-0 Progress (no connection) 66% (none)
2024-10-09T17:20:46.351Z In(05) vcpu-0 Progress (no connection) 67% (none)
2024-10-09T17:20:46.351Z In(05) vcpu-0 Progress (no connection) 68% (none)
2024-10-09T17:20:46.352Z In(05) vcpu-0 Progress (no connection) 69% (none)
2024-10-09T17:20:46.352Z In(05) vcpu-0 Progress (no connection) 70% (none)
2024-10-09T17:20:46.353Z In(05) vcpu-0 Progress (no connection) 71% (none)
2024-10-09T17:20:46.353Z In(05) vcpu-0 Progress (no connection) 72% (none)
2024-10-09T17:20:46.353Z In(05) vcpu-0 Progress (no connection) 73% (none)
2024-10-09T17:20:46.354Z In(05) vcpu-0 Progress (no connection) 74% (none)
2024-10-09T17:20:46.354Z In(05) vcpu-0 Progress (no connection) 75% (none)
2024-10-09T17:20:46.355Z In(05) vcpu-0 Progress (no connection) 76% (none)
2024-10-09T17:20:46.355Z In(05) vcpu-0 Progress (no connection) 77% (none)
2024-10-09T17:20:46.356Z In(05) vcpu-0 Progress (no connection) 78% (none)
2024-10-09T17:20:46.357Z In(05) vcpu-0 Progress (no connection) 79% (none)
2024-10-09T17:20:46.357Z In(05) vcpu-0 Progress (no connection) 80% (none)
2024-10-09T17:20:46.357Z In(05) vcpu-0 Progress (no connection) 81% (none)
2024-10-09T17:20:46.358Z In(05) vcpu-0 Progress (no connection) 82% (none)
2024-10-09T17:20:46.358Z In(05) vcpu-0 Progress (no connection) 83% (none)
2024-10-09T17:20:46.358Z In(05) vcpu-0 Progress (no connection) 84% (none)
2024-10-09T17:20:46.358Z In(05) vcpu-0 Progress (no connection) 85% (none)
2024-10-09T17:20:46.359Z In(05) vcpu-0 Progress (no connection) 86% (none)
2024-10-09T17:20:46.359Z In(05) vcpu-0 Progress (no connection) 87% (none)
2024-10-09T17:20:46.360Z In(05) vcpu-0 Progress (no connection) 88% (none)
2024-10-09T17:20:46.360Z In(05) vcpu-0 Progress (no connection) 89% (none)
2024-10-09T17:20:46.361Z In(05) vcpu-0 Progress (no connection) 90% (none)
2024-10-09T17:20:46.361Z In(05) vcpu-0 Progress (no connection) 91% (none)
2024-10-09T17:20:46.362Z In(05) vcpu-0 Progress (no connection) 92% (none)
2024-10-09T17:20:46.363Z In(05) vcpu-0 Progress (no connection) 93% (none)
2024-10-09T17:20:46.363Z In(05) vcpu-0 Progress (no connection) 94% (none)
2024-10-09T17:20:46.364Z In(05) vcpu-0 Progress (no connection) 95% (none)
2024-10-09T17:20:46.365Z In(05) vcpu-0 Progress (no connection) 96% (none)
2024-10-09T17:20:46.366Z In(05) vcpu-0 Progress (no connection) 97% (none)
2024-10-09T17:20:46.366Z In(05) vcpu-0 Progress (no connection) 98% (none)
2024-10-09T17:20:46.367Z In(05) vcpu-0 Progress (no connection) 99% (none)
2024-10-09T17:20:46.368Z In(05) vcpu-0 Progress (no connection) 100% (none)
2024-10-09T17:20:46.373Z In(05) vcpu-0 SVGA: Guest reported SVGA driver: (0, 0, 0, 0)
2024-10-09T17:20:46.373Z In(05) vcpu-0 SVGA-ScreenMgr: No SVGA screens defined at checkpoint save.
2024-10-09T17:20:46.373Z In(05) vcpu-0 Tools: [AppStatus] Last heartbeat value 0 (never received)
2024-10-09T17:20:46.373Z In(05) vcpu-0 TOOLS: appName=toolbox, oldStatus=0, status=0, guestInitiated=0.
2024-10-09T17:20:46.377Z In(05) vcpu-0 DEPLOYPKG: ToolsDeployPkgCptSave: state=0 err=0 (null msg)
2024-10-09T17:20:46.377Z In(05) vcpu-0 Progress 101% (none)
2024-10-09T17:20:46.381Z No(00) vcpu-0 ConfigDB: Setting checkpoint.vmState = "Metasploitable-8f9ae34c.vmss"
2024-10-09T17:20:46.400Z In(05) vcpu-0 Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=1, newAppState=1875, success=1 additionalError=0
2024-10-09T17:20:46.400Z In(05) vcpu-0 SUSPEND: Completed suspend: 'Operation completed successfully' (0)
2024-10-09T17:20:46.400Z In(05) vmx Stopping VCPU threads...
2024-10-09T17:20:46.400Z In(05) vmx MKSThread: Requesting MKS exit
2024-10-09T17:20:46.400Z In(05) vmx Stopping MKS/SVGA threads
2024-10-09T17:20:46.400Z In(05) svga SVGA thread is exiting the main loop
2024-10-09T17:20:46.400Z In(05) mks SWBScreen: Screen 0 Destroyed: xywh(0, 0, 720, 400) flags=0x3
2024-10-09T17:20:46.400Z In(05) vmx MKS/SVGA threads are stopped
2024-10-09T17:20:46.401Z In(05) vmx 
2024-10-09T17:20:46.401Z In(05)+ vmx OvhdMem: Final (Power Off) Overheads
2024-10-09T17:20:46.401Z In(05) vmx                                                       reserved      |          used
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem excluded                                  cur    max    avg |    cur    max    avg
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdUser_MainMem                    :  131072 131072      - |  73926  73926      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdUser_VmxText                    :    7680   7680      - |      0      0      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdUser_VmxTextLibs                :   17408  17408      - |      0      0      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem Total excluded                      :  156160 156160      - |      -      -      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem Actual maximum                      :         156160        |             -
2024-10-09T17:20:46.401Z In(05)+ vmx 
2024-10-09T17:20:46.401Z In(05) vmx                                                       reserved      |          used
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem paged                                     cur    max    avg |    cur    max    avg
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdUser_STATS_vmm                  :       2      2      - |      0      0      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdUser_STATS_device               :       1      1      - |      0      0      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdUser_DiskLibMemUsed             :    3075   3075      - |      0      0      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdUser_SvgaSDirtyCache            :      96     96      - |      0      0      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdUser_SvgaCursor                 :      10     10      - |     10     10      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdUser_VmxGlobals                 :   10240  10240      - |      0      0      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdUser_VmxGlobalsLibs             :    3584   3584      - |      0      0      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdUser_VmxHeap                    :    8704   8704      - |      0      0      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdUser_VmxMks                     :      33     33      - |      1      1      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdUser_VmxMksRenderOps            :     678    678      - |    492    492      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdUser_VmxMks3d                   :  229376 229376      - |      0      0      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdUser_VmxMksScreenTemp           :   37122  37122      - |      0      0      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdUser_VmxMksVnc                  :   38236  38236      - |      0      0      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdUser_VmxMksScreen               :   65539  65539      - |      0    376      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdUser_VmxMksSVGAVO               :    4096   4096      - |      0      0      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdUser_VmxMksSwbCursor            :    4096   4096      - |      0      0      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdUser_VmxPhysMemErrPages         :      10     10      - |      0      0      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdUser_VmxSLEntryBuf              :     128    128      - |      0      0      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdUser_VmxThreads                 :   35328  35328      - |      0      0      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem Total paged                         :  440354 440354      - |    503    879      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem Actual maximum                      :         440354        |        440354
2024-10-09T17:20:46.401Z In(05)+ vmx 
2024-10-09T17:20:46.401Z In(05) vmx                                                       reserved      |          used
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem nonpaged                                  cur    max    avg |    cur    max    avg
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdUser_SharedArea                 :     137    137      - |    122    122      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdUser_BusMemTraceBitmap          :      10     10      - |      0      0      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdUser_PFrame                     :     498   1536      - |    498    498      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdUser_VIDE_KSEG                  :      16     16      - |     16     16      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdUser_VGA                        :      64     64      - |     64     64      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdUser_BalloonMPN                 :       1      1      - |      1      1      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdUser_P2MUpdateBuffer            :       3      3      - |      0      0      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdUser_ServicesMPN                :       3      3      - |      2      2      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdUser_LocalApic                  :       1      1      - |      1      1      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdUser_VBIOS                      :       8      8      - |      8      8      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdUser_VnicGuest                  :      32     32      - |     32     32      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdUser_LSIBIOS                    :       4      4      - |      2      2      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdUser_LSIRings                   :       4      4      - |      0      0      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdUser_SAS1068BIOS                :       4      4      - |      0      0      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdUser_SBIOS                      :      16     16      - |      0      0      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdUser_FlashRam                   :     128    128      - |     57     57      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdUser_SVGAFB                     :   32768  32768      - |      0      0      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdUser_SVGAMEM                    :     512    512      - |      1      1      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdUser_HDAudioReg                 :       3      3      - |      0      0      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdUser_EHCIRegister               :       1      1      - |      1      1      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdUser_XhciRegister               :       1      1      - |      0      0      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdUser_HyperV                     :       2      2      - |      0      0      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdUser_ExtCfg                     :       4      4      - |      2      2      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdUser_vhvCachedVMCS              :       1      1      - |      0      0      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdUser_vhvNestedAPIC              :       1      1      - |      0      0      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdUser_LBR                        :       1      1      - |      0      0      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdUser_MonWired                   :      35     35      - |     35     35      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdUser_MonNuma                    :     225    225      - |      0      0      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdUser_NVDC                       :       1      1      - |      0      0      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdUser_PCIeMMIOArea               :      70     70      - |     34     34      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem Total nonpaged                      :   34554  35592      - |    876    876      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem Actual maximum                      :          34554        |         35592
2024-10-09T17:20:46.401Z In(05)+ vmx 
2024-10-09T17:20:46.401Z In(05) vmx                                                       reserved      |          used
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem anonymous                                 cur    max    avg |    cur    max    avg
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdMon_Alloc                       :      98     98      - |     25     77      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdMon_BusMemFrame                 :     225    282      - |    225    225      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdMon_BusMem2MInfo                :       2      2      - |      2      2      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdMon_BusMem1GInfo                :       1      1      - |      1      1      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdMon_BusMemZapListMPN            :       1      1      - |      1      1      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdMon_BusMemPreval                :       4      4      - |      0      0      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdMon_MonAS                       :       1      1      - |      1      1      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdMon_GuestMem                    :      40     40      - |     30     30      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdMon_TC                          :     513    513      - |    477    477      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdMon_BusMemMonAS                 :       4      4      - |      4      4      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdMon_PlatformMonAS               :       6      6      - |      2      2      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdMon_HVNuma                      :       2      2      - |      0      0      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdMon_HV                          :       1      1      - |      1      1      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdMon_HVMSRBitmap                 :       1      1      - |      0      0      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdMon_VHVGuestMSRBitmap           :       1      1      - |      0      0      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdMon_VHV                         :       3      3      - |      0      0      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdMon_Numa                        :      24     24      - |     14     24      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdMon_NumaTextRodata              :     198    374      - |    176    352      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdMon_NumaDataBss                 :      27     27      - |     26     26      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdMon_NumaLargeData               :       0    512      - |      0      0      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdMon_BaseWired                   :      28     29      - |     23     23      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdMon_Bootstrap                   :       0   2303      - |      0    330      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdMon_GPhysTraced                 :     264    264      - |     88     88      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdMon_GPhysHWMMU                  :     580    580      - |    299    299      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdMon_GPhysNoTrace                :     264    264      - |     69     69      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdMon_PhysMemGart                 :     104    104      - |     96     96      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdMon_PhysMemErr                  :       6      6      - |      0      0      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem OvhdMon_VProbe                      :       1      1      - |      0      0      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem Total anonymous                     :    2399   5448      - |   1560   2128      -
2024-10-09T17:20:46.401Z In(05) vmx OvhdMem Actual maximum                      :           2399        |          4760
2024-10-09T17:20:46.401Z In(05)+ vmx 
2024-10-09T17:20:46.402Z In(05) vmx VMMEM: Maximum Reservation: 1880MB (MainMem=512MB)
2024-10-09T17:20:46.402Z In(05) vmx MemSched: BALLOON HIST [0, 131072]: 459 459 0 0 0 0 0 0 0 0 0 0
2024-10-09T17:20:46.402Z In(05) vmx MemSched: BALLOON P50 1 P70 1 P90 1 MIN 0 MAX 0
2024-10-09T17:20:46.402Z In(05) vmx MemSched: SWAP HIST [0, 131072]: 459 459 0 0 0 0 0 0 0 0 0 0
2024-10-09T17:20:46.402Z In(05) vmx MemSched: SWAP P50 1 P70 1 P90 1 MIN 0 MAX 0
2024-10-09T17:20:46.402Z In(05) vmx MemSched: LOCK HIST [0, 131072]: 0 11 23 4 5 2 414 0 0 0 0 0
2024-10-09T17:20:46.402Z In(05) vmx MemSched: LOCK P50 60 P70 60 P90 60 MIN 793 MAX 76363
2024-10-09T17:20:46.402Z In(05) vmx MemSched: LOCK_TARGET HIST [0, 131072]: 0 0 0 0 0 0 0 35 94 89 241 120
2024-10-09T17:20:46.402Z In(05) vmx MemSched: LOCK_TARGET P50 100 P70 100 P90 100 MIN 85932 MAX 135229
2024-10-09T17:20:46.402Z In(05) vmx MemSched: ACTIVE_PCT HIST [0, 100]: 0 0 0 0 87 0 60 60 252 0 0 0
2024-10-09T17:20:46.402Z In(05) vmx MemSched: ACTIVE_PCT P50 80 P70 80 P90 80 MIN 31 MAX 75
2024-10-09T17:20:46.402Z In(05) vmx MemSched: NUM_VMS HIST [0, 10]: 0 0 0 459 0 0 0 0 0 0 0 0
2024-10-09T17:20:46.402Z In(05) vmx MemSched: NUM_VMS P50 30 P70 30 P90 30 MIN 2 MAX 2
2024-10-09T17:20:46.402Z In(05) vmx MemSched: HOSTLOCK HIST [0, 1570816]: 0 0 0 166 293 0 0 0 0 0 0 0
2024-10-09T17:20:46.402Z In(05) vmx MemSched: HOSTLOCK P50 40 P70 40 P90 40 MIN 385733 MAX 472102
2024-10-09T17:20:46.402Z In(05) vmx TOOLS received request in VMX to set option 'enableDnD' -> '0'
2024-10-09T17:20:46.402Z In(05) vmx TOOLS received request in VMX to set option 'copypaste' -> '0'
2024-10-09T17:20:46.402Z In(05) vmx HgfsServerManagerVigorExit: Destroy:
2024-10-09T17:20:46.402Z In(05) vmx ToolsISO: Refreshing imageName for 'ubuntu' (refreshCount=1, lastCount=1).
2024-10-09T17:20:46.403Z In(05) vmx ToolsISO: open of C:\Program Files (x86)\VMware\VMware Workstation\isoimages_manifest.txt.sig failed: Could not find the file
2024-10-09T17:20:46.403Z In(05) vmx ToolsISO: Unable to read signature file 'C:\Program Files (x86)\VMware\VMware Workstation\isoimages_manifest.txt.sig', ignoring.
2024-10-09T17:20:46.404Z In(05) vmx ToolsISO: Updated cached value for imageName to 'linux.iso'.
2024-10-09T17:20:46.404Z In(05) vmx ToolsISO: Selected Tools ISO 'linux.iso' for 'ubuntu' guest.
2024-10-09T17:20:46.404Z In(05) vmx TOOLS updated cached value for isoImageExists to 1.
2024-10-09T17:20:46.404Z In(05) vmx Tools: ToolsRunningStatus_Exit, delayedRequest is 0x226E83A64F0
2024-10-09T17:20:46.407Z In(05) usbCCIDEnumCards USB-CCID: Card enum thread exiting.
2024-10-09T17:20:46.408Z In(05) vmx SOUNDLIB: Closing Wave sound backend.
2024-10-09T17:20:46.408Z In(05) mks MKS-RenderMain: Stopping MKSBasicOps
2024-10-09T17:20:46.408Z In(05) mks MKS-RenderMain: Stopping MKSBasicOps
2024-10-09T17:20:46.408Z In(05) mks MKS-RenderMain: Stopped MKSBasicOps
2024-10-09T17:20:46.410Z In(05) mks MKS PowerOff
2024-10-09T17:20:46.410Z In(05) svga SVGA thread is exiting
2024-10-09T17:20:46.410Z In(05) mks MKS thread is exiting
2024-10-09T17:20:46.410Z Wa(03) vmx 
2024-10-09T17:20:46.414Z In(05) vmx AIOWIN32C: asyncOps=7411 syncOps=7 bufSize=272Kb fixedOps=980
2024-10-09T17:20:46.414Z In(05) aioCompletion AIO thread processed 7411 completions
2024-10-09T17:20:46.432Z In(05) deviceThread Device thread is exiting
2024-10-09T17:20:46.433Z In(05) vmx Vix: [mainDispatch.c:1171]: VMAutomationPowerOff: Powering off.
2024-10-09T17:20:46.433Z In(05) vmx Win32U_GetFileAttributes: GetFileAttributesExW("C:\Users\<USER>\Desktop\Metasploitable2-Linux\Metasploitable.vmpl", ...) failed, error: 2
2024-10-09T17:20:46.434Z In(05) vmx Policy_SavePolicyFile: invalid arguments to function.
2024-10-09T17:20:46.434Z In(05) vmx PolicyVMX_Exit: Could not write out policies: 15.
2024-10-09T17:20:46.434Z In(05) vmx WORKER: asyncOps=1 maxActiveOps=1 maxPending=1 maxCompleted=0
