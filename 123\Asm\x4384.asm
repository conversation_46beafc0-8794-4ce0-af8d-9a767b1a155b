;214502080
;325812667
.ORIG X4384

CALCULATOR:

ST R0, R0_SAVE
ST R1, R1_SAVE
ST R3, R3_SAVE
ST R7, R7_SAVE

	LEA R0, MSG
	PUTS

	GETC
	OUT

	ST R0, OP
	AND R2, R2, #0
	
	LD R1, ADD_OP
	ADD R0, R0, R1
	BRz ADD_OPERATION

	LD R1, SUB_OP
	LD R0, OP
	ADD R0, R0, R1
	BRz SUB_OPERATION

	LD R1, MUL_OP
	LD R0, OP
	ADD R0, R0, R1
	BRz MUL_OPERATION

	LD R1, DIV_OP
	LD R0, OP
	ADD R0, R0, R1
	BRz DIV_OPERATION

	LD R1, EXP_OP
	LD R0, OP
	ADD R0, R0, R1
	BRz EXP_OPERATION

	LD R1, SUB_OP
	LD R0, OP
	ADD R0, R0, R1
	BRz SUB_OPERATION

ADD_OPERATION:

	LD R0, ENTER
	OUT
	LD R0, R0_SAVE
	LD R1, R1_SAVE

	ADD R0, R0, R1
	LD R1, PRINT_NUM_PTR
	ADD R2, R2, R0
	JSRR R1
	
	BR END_SUB
		
SUB_OPERATION:

	LD R0, ENTER
	OUT

	LD R0, R0_SAVE
	LD R1, R1_SAVE

	NOT R1, R1
	ADD R1, R1, #1
	ADD R0, R0, R1
	LD R1, PRINT_NUM_PTR
	ADD R2, R2, R0
	JSRR R1

	BR END_SUB

MUL_OPERATION:

	LD R0, ENTER
	OUT
	
	LD R0, R0_SAVE
	LD R1, R1_SAVE

	LD R3, MUL_PTR
	JSRR R3

	LD R1, PRINT_NUM_PTR
	JSRR R1

	BR END_SUB

DIV_OPERATION:

	LD R0, ENTER
	OUT

	
	LD R0, R0_SAVE
	LD R1, R1_SAVE

	LD R3, DIV_PTR
	JSRR R3

	LD R1, PRINT_NUM_PTR
	JSRR R1

	BR END_SUB

EXP_OPERATION:

	LD R0, ENTER
	OUT
	
	LD R0, R0_SAVE
	LD R1, R1_SAVE

	LD R3, EXP_PTR
	JSRR R3

	LD R1, PRINT_NUM_PTR
	JSRR R1

	BR END_SUB

END_SUB:

LD R0, R0_SAVE
LD R1, R1_SAVE
LD R3, R3_SAVE
LD R7, R7_SAVE

RET
HALT
MSG .STRINGZ "Enter an arithmetic operation: "
ADD_OP .FILL #-43
SUB_OP .FILL #-45
MUL_OP .FILL #-42
DIV_OP .FILL #-47
EXP_OP .FILL #-94
ASCII_VALUE .FILL #48
ENTER .FILL #10
OP .FILL #0
MUL_PTR .FILL X4000
DIV_PTR .FILL X4064
EXP_PTR .FILL X40C8
PRINT_NUM_PTR .FILL X4320
R0_SAVE .FILL #0
R1_SAVE .FILL #0
R3_SAVE .FILL #0
R7_SAVE .FILL #0
.END