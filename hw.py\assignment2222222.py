import vedo as vd
vd.settings.default_backend = 'vtk'

from vedo import show
import numpy as np
from abc import ABC, abstractmethod
import numdifftools as nd
from scipy.sparse import coo_matrix
import triangle as tr  # pip install triangle

#%% Stencil class
class Stencil(ABC):
    @abstractmethod
    def ExtractElementsFromMesh(self, F):
        pass

    @abstractmethod
    def ExtractVariblesFromVectors(self, x):
        pass

class EdgeStencil(Stencil):
    @staticmethod
    def ExtractElementsFromMesh(F):
        edges = {tuple(sorted((F[i, j], F[i, (j+1) % 3]))) for i in range(F.shape[0]) for j in range(3)}
        return list(edges)
    
    @staticmethod
    def ExtractVariblesFromVectors(x):
        x = x.flatten()
        return x[0:2], x[2:4]  # Strictly 2D, assuming x has 4 elements

#%% Energy functions
class ElementEnergy(ABC):    
    @abstractmethod
    def energy(self, X, x):
        pass

    def gradient(self, X, x):
        return self.gradient_fd(X, x)

    def hessian(self, X, x):
        return self.hessian_fd(X, x)
    
    def gradient_fd(self, X, x):
        return nd.Gradient(lambda x: self.energy(X, x.flatten()))(x)

    def hessian_fd(self, X, x):
        return nd.Hessian(lambda x: self.energy(X, x.flatten()))(x)
    
    def check_gradient(self, X, x):
        grad = self.gradient(X, x)
        grad_fd = self.gradient_fd(X, x)
        return np.linalg.norm(grad - grad_fd)

class ZeroLengthSpringEnergy(ElementEnergy):
    def __init__(self):
        self.stencil = EdgeStencil()

    def energy(self, X, x):
        x1, x2 = self.stencil.ExtractVariblesFromVectors(x)
        return 0.5 * np.linalg.norm(x1 - x2) ** 2

    def gradient(self, X, x):
        x1, x2 = self.stencil.ExtractVariblesFromVectors(x)
        return np.concatenate([x1 - x2, x2 - x1])

    def hessian(self, X, x):
        I = np.eye(2)
        return np.block([[I, -I], [-I, I]])


class SpringEnergy(ElementEnergy):
    def __init__(self, L, tolerance=0.01):
        self.stencil = EdgeStencil()
        self.L = L  # Rest length
        self.tolerance = tolerance  # Convergence threshold

    def energy(self, X, x):
        x1, x2 = self.stencil.ExtractVariblesFromVectors(x)
        current_length = np.linalg.norm(x1 - x2)
        if np.abs(current_length - self.L) <= self.tolerance:
            return 0.0  # Consider as converged
        return 0.5 * (current_length - self.L) ** 2

    def gradient(self, X, x):
        x1, x2 = self.stencil.ExtractVariblesFromVectors(x)
        current_length = np.linalg.norm(x1 - x2)
        if current_length == 0 or np.abs(current_length - self.L) <= self.tolerance:
            return np.zeros_like(x)  # No gradient if within tolerance
        direction = (x1 - x2) / current_length
        grad = (current_length - self.L) * np.concatenate([direction, -direction])
        return grad

    def hessian(self, X, x):
        x1, x2 = self.stencil.ExtractVariblesFromVectors(x)
        current_length = np.linalg.norm(x1 - x2)
        I = np.eye(2)
        if current_length == 0 or np.abs(current_length - self.L) <= self.tolerance:
            return np.block([[I, -I], [-I, I]]) * 0  # Zero hessian if within tolerance
        outer_product = np.outer(x1 - x2, x1 - x2) / current_length**2
        hess = np.block([
            [I - outer_product, -I + outer_product],
            [-I + outer_product, I - outer_product]
        ])
        return hess

#%% Mesh class
class FEMMesh:
    def __init__(self, V, F, energy, stencil):
        self.V = V
        self.F = F
        self.energy = energy
        self.stencil = stencil
        self.elements = self.stencil.ExtractElementsFromMesh(F)
        self.X = self.V.copy()
        self.nV = self.V.shape[0]

    def compute_energy(self, x):
        energy = 0
        for element in self.elements:
            Xi = self.X[element, :]
            xi = x[element, :]
            energy += self.energy.energy(Xi, xi)
        return energy
    
    def compute_gradient(self, x):
        grad = np.zeros(2 * x.shape[0])
        for element in self.elements:
            Xi = self.X[element, :]
            xi = x[element, :]
            gi = self.energy.gradient(Xi, xi)

            # Ensure gi is flattened before adding to grad
            gi = gi.flatten()

            grad[2 * element[0]:2 * element[0] + 2] += gi[0:2]
            grad[2 * element[1]:2 * element[1] + 2] += gi[2:4]
                
        return grad

    def compute_hessian(self, x):
        I = []
        J = []
        S = []
        for element in self.elements:
            Xi = self.X[element, :]
            xi = x[element, :]
            hess = self.energy.hessian(Xi, xi)
            for i in range(4):
                for j in range(4):
                    I.append(2 * element[i // 2] + i % 2)
                    J.append(2 * element[j // 2] + j % 2)
                    S.append(hess[i, j])
        H = coo_matrix((S, (I, J)), shape=(2 * self.nV, 2 * self.nV))
        return H

#%% Optimization
class MeshOptimizer:
    def __init__(self, femMesh):
        self.femMesh = femMesh
        self.SearchDirection = self.GradientDescent
        self.LineSearch = self.BacktrackingLineSearch
        self.step_scale = 1  # Step size for Newton's method

    def BacktrackingLineSearch(self, x, d, alpha=1):
        x0 = x.copy()
        f0 = self.femMesh.compute_energy(x0)
        d = d.reshape(-1, 2)
        while self.femMesh.compute_energy(x0 + alpha * d) > f0:
            alpha *= 0.5
        return x0 + alpha * d, alpha

    def GradientDescent(self, x):
        d = self.femMesh.compute_gradient(x)
        return -d

    def Newton(self, x):
        grad = self.femMesh.compute_gradient(x)
        hess = self.femMesh.compute_hessian(x).toarray()  # Convert sparse matrix to a dense array

        # Print the Hessian and gradient to debug
        print("Gradient norm:", np.linalg.norm(grad))
        print("Hessian norm:", np.linalg.norm(hess))

        # Regularizing the Hessian (increase the regularization term to stabilize the steps)
        regularization_term = 1e-2  # You can experiment with this value
        hess = hess + regularization_term * np.eye(hess.shape[0])

        try:
            d = -np.linalg.solve(hess, grad) * self.step_scale
        except np.linalg.LinAlgError as e:
            print("LinAlgError:", e)
            d = -grad

        return d


    def step(self, x, V_pinned, pinned_positions):
        d = self.SearchDirection(x)
        new_x, alpha = self.LineSearch(x, d)
        
        # Re-pin the pinned vertices
        for idx, v in enumerate(V_pinned):
            # Ensure the vertex is re-pinned to its original position
            new_x[v, 0] = pinned_positions[idx][0]  # x-coordinate
            new_x[v, 1] = pinned_positions[idx][1]  # y-coordinate

        return new_x


#%% Main program
# Original mesh
vertices = np.array([[-0.5, -0.5], [0.5, -0.5], [0.5, 0.5], [-0.5, 0.5]]) # square
tris = tr.triangulate({"vertices":vertices[:,0:2]}, f'qa0.01') # triangulate the square

# Hexagon Mesh
vertices = np.array([
    [0, 1], 
    [0.87, 0.5], 
    [0.87, -0.5], 
    [0, -1], 
    [-0.87, -0.5], 
    [-0.87, 0.5]
])
area = 2.6/100
tris = tr.triangulate({"vertices": vertices}, f'qa{area}') 

V = tris['vertices']
F = tris['triangles']

# Create two FEMMeshes, one for GD and one for Newton
femMesh_gd = FEMMesh(V.copy(), F, ZeroLengthSpringEnergy(), EdgeStencil())
femMesh_newton = FEMMesh(V.copy(), F, ZeroLengthSpringEnergy(), EdgeStencil())


l = 0.08 
# femMesh_gd = FEMMesh(V.copy(), F, SpringEnergy(l), EdgeStencil())
# femMesh_newton = FEMMesh(V.copy(), F, SpringEnergy(l), EdgeStencil())

# Create Optimizers
optimizer_gd = MeshOptimizer(femMesh_gd)
optimizer_newton = MeshOptimizer(femMesh_newton)
optimizer_newton.SearchDirection = optimizer_newton.Newton

# User Interface and Interaction
pinned_vertices_gd = []
pinned_positions_gd = []
pinned_vertices_newton = []
pinned_positions_newton = []

selection_message = None
D_message = None
selected_vertex = None
key_pressed = False

# Initialize convergence flags
converged_gd = False
converged_newton = False

def redraw():
    global selection_message, D_message, mesh_gd, mesh_newton

    for i, mesh in enumerate([mesh_gd, mesh_newton]):
        plt.at(i).remove("Mesh")
        plt.at(i).add(mesh)
        plt.at(i).remove("Points")
        if i == 0:  # Gradient Descent side
            plt.at(i).add(vd.Points(V_gd[pinned_vertices_gd, :], r=10))
        else:  # Newton's Method side
            plt.at(i).add(vd.Points(V_newton[pinned_vertices_newton, :], r=10))
        if selected_vertex is not None:
            if i == 0:
                plt.at(i).add(vd.Points(V_gd[selected_vertex].reshape(1, -1), r=12, c='blue'))
            else:
                plt.at(i).add(vd.Points(V_newton[selected_vertex].reshape(1, -1), r=12, c='blue'))
        plt.at(i).render()


def OnLeftButtonPress(event):
    global selection_message, selected_vertex, D_message

    if selection_message:
        plt.remove(selection_message)
    
    if event.at == 0:  # Gradient Descent side
        mesh = mesh_gd
        V = V_gd
        pinned_vertices = pinned_vertices_gd
        pinned_positions = pinned_positions_gd
    else:  # Newton's Method side
        mesh = mesh_newton
        V = V_newton
        pinned_vertices = pinned_vertices_newton
        pinned_positions = pinned_positions_newton

    if event.object is None:
        selection_message = vd.Text2D('Mouse hits nothing', pos="bottom-left", s=0.8, c='red')
    elif isinstance(event.object, vd.Mesh):
        Vi = mesh.closest_point(event.picked3d, return_point_id=True)
        selected_vertex = Vi
        if key_pressed == 's':
            if Vi not in pinned_vertices:
                pinned_vertices.append(Vi)
                pinned_positions.append(V[Vi])  # Save the position
                selection_message = vd.Text2D(f"Pinned vertex {Vi}.", pos="bottom-left", s=0.8, c='red')
            else:
                index = pinned_vertices.index(Vi)
                pinned_vertices.remove(Vi)
                pinned_positions.pop(index)  # Remove the corresponding position
                selection_message = vd.Text2D(f"Unpinned vertex {Vi}.", pos="bottom-left", s=0.8, c='red')
        elif key_pressed == 'd':
            D_message = vd.Text2D(f"Moving vertex {Vi}.", pos="bottom-left", s=0.8, c='red')
    redraw()

def OnMouseMove(event):
    global selection_message, selected_vertex, D_message
    
    if D_message:
        plt.remove(D_message)
    
    if selected_vertex is not None and key_pressed == 'd':
        mouse_pos = plt.interactor.GetEventPosition()
        world_coords = plt.compute_world_coordinate(mouse_pos)
        
        if event.at == 0:  # Gradient Descent mesh
            V_gd[selected_vertex] = [world_coords[0], world_coords[1]]
            mesh_gd.points(V_gd)
        else:  # Newton's Method mesh
            V_newton[selected_vertex] = [world_coords[0], world_coords[1]]
            mesh_newton.points(V_newton)
        
        D_message = vd.Text2D(f"Moved vertex {selected_vertex} to new position {world_coords[:2]}.", pos="bottom-right", s=0.8, c='red')
        redraw()


def OnLeftButtonRelease(event):
    global selection_message, selected_vertex
    if selected_vertex is not None:
        selection_message = vd.Text2D(f"Released vertex {selected_vertex}.", pos="bottom-left", s=0.8, c='red')
    redraw()

def OnKeyPress(event):
    global key_pressed, converged_gd, converged_newton, mesh_gd, mesh_newton, V_gd, V_newton
    
    key_pressed = event.keypress
    
    if key_pressed == 'd':
        key_pressed = 'd'
    elif key_pressed == 'c':
        if not converged_gd:
            V_gd = optimizer_gd.step(V_gd, pinned_vertices_gd, pinned_positions_gd)
            mesh_gd.points = V_gd

            grad_gd = np.linalg.norm(optimizer_gd.femMesh.compute_gradient(V_gd))
            if grad_gd < optimizer_gd.step_scale:
              mesh_newton.points = V_newton
              print("Gradient Descent converged.")

        if not converged_newton:
            V_newton = optimizer_newton.step(V_newton, pinned_vertices_newton, pinned_positions_newton)
            mesh_newton.points(V_newton)

            grad_newton = np.linalg.norm(optimizer_newton.femMesh.compute_gradient(V_newton))
            if grad_newton < optimizer_newton.step_scale:
                converged_newton = True
                print("Newton's Method converged.")
        redraw()



def OnKeyRelease(event):
    global key_pressed
    if key_pressed in ['d', 's']:
        key_pressed = None


plt = vd.Plotter(N=2, size=(1600, 800))

instructions = vd.Text2D("  C to run a step\n  D to move selected vertex\n  Hold S and click to pin \n", pos="bottom-left", s=1.0, c='blue')
plt.add(instructions)


plt.add_callback('LeftButtonPress', OnLeftButtonPress)
plt.add_callback('MouseMove', OnMouseMove)
plt.add_callback('LeftButtonRelease', OnLeftButtonRelease)
plt.add_callback('KeyPress', OnKeyPress)
plt.add_callback('KeyRelease', OnKeyRelease)

# Initial meshes
mesh_gd = vd.Mesh([V, F]).linecolor('black')
mesh_newton = vd.Mesh([V, F]).linecolor('black')

# Flatten vertex arrays to use for optimization
V_gd = V.copy()
V_newton = V.copy()

# Show both meshes side by side with 2D interaction and no grid/axes
plt.at(0).show(mesh_gd, "Gradient Descent Mesh", axes=False)
plt.at(1).show(mesh_newton, "Newton's Method Mesh", axes=False)

# Start interactive session
plt.interactive().close()
