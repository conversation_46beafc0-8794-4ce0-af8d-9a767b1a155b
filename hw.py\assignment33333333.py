import vedo as vd
vd.settings.default_backend = 'vtk'
import numpy as np
import os
from time import time

# Global variables
method = 0  # 0 for Gauss-Newton, 1 for Gradient Descent, 2 for Both Methods
method_text = None
last_click_time = 0  # To handle debounce
previous_state = None  # To save the previous state

# Function to update method text on screen
def update_method_text():
    global method_text
    if method_text:
        plt.remove(method_text)
    method_name = ["Gauss-Newton", "Gradient Descent", "Both Methods"][method]
    method_text = vd.Text2D(f"Current Method: {method_name}", pos='bottom-left', s=1.0, c='white', bg='black')
    plt.add(method_text)

#%% class for a robot arm
def Rot(angle, axis):
    axis = np.array(axis)
    axis = axis/np.linalg.norm(axis)
    I = np.eye(3)
    K = np.array([[0, -axis[2], axis[1]],
                    [axis[2], 0, -axis[0]],
                    [-axis[1], axis[0], 0]])
    R = I + np.sin(angle)*K + (1-np.cos(angle))*np.dot(K,K)
    return R
    
class SimpleArm:
    def __init__(self, n=3, link_lengths=1):
        self.n = n  # number of links
        self.angles = [0] * self.n  # joint angles, starting from the base joint to the end effector
        self.link_lengths = link_lengths

        self.Jl = np.zeros((self.n + 1, 3))
        for i in range(1, n + 1):  # we start from 1 because the base joint is at the origin (0,0,0) and finish the end effector is at the end of the last link
            self.Jl[i, :] = np.array([self.link_lengths[i - 1], 0, 0])  # initialize joint positions to lie along the x-axis WITH GIVEN LENGTH

        self.Jw = np.zeros((self.n + 1, 3))  # joint positions in world coordinates
        self.FK()

    def FK(self, angles=None): 
        # calculate the forward kinematics of the arm

        if angles is not None:
            self.angles = angles
        
        # Initial rotation matrix
        Ri = np.eye(3)

    # Compute the position of each joint using a loop
        for i in range(1, self.n + 1):
            Ri = Rot(self.angles[i - 1], [0, 0, 1]) @ Ri  # Compute the rotation matrix for the current joint
            self.Jw[i, :] = Ri @ self.Jl[i, :] + self.Jw[i - 1, :]  # Update the position of the current joint in world coordinates

        return self.Jw[-1, :] # return the position of the end effector
            
    def IK(self, target, learning_rate=0.05, max_iterations=500, tolerance=0.05, method='gradient_descent', save_dir="screenshots"):
        max_reach = np.sum(self.link_lengths)

        if np.linalg.norm(target) > max_reach:
            error_message = vd.Text2D("Target out of range!", pos='top-middle', s=1.5, c='red', bg='white')
            plt.add(error_message)
            return

        os.makedirs(save_dir, exist_ok=True)  # Create screenshots directory if not exists

        velocity = np.zeros(self.n)  # For momentum
        beta = 0.9  # Momentum term
        for iteration in range(max_iterations):
            current_end_effector = self.FK()
            error = target - current_end_effector
            if np.linalg.norm(error) < tolerance:
                print(f"Converged in {iteration} iterations using {method}")
                break
            J = self.VelocityJacobian()
            if method == 'gradient_descent':
                grad = J.T @ error
                velocity = beta * velocity + (1 - beta) * grad  # Momentum update
                d_angles = learning_rate * velocity
            elif method == 'gauss_newton':
                J_transpose = J.T
                JT_J_inv = np.linalg.pinv(J_transpose @ J)
                d_angles = JT_J_inv @ J_transpose @ error
            self.angles += d_angles

            if iteration % 1 == 0 and method == 'gauss_newton':
                plt.remove("Assembly")
                plt.remove("Arrow")
                plt.add(self.draw())
                plt.add(vd.Text2D(f"Iteration: {iteration}\nMethod: {method}", pos='top-right', s=1.0, c='white', bg='black'))
                plt.render()
                plt.screenshot(os.path.join(save_dir, f"iteration_{iteration}_{method}.png"))
            
            if iteration % 5 == 0 and method == 'gradient_descent':
                plt.remove("Assembly")
                plt.remove("Arrow")
                plt.add(self.draw())
                plt.add(vd.Text2D(f"Iteration: {iteration}\nMethod: {method}", pos='top-left', s=1.0, c='white', bg='black'))
                plt.render()
                plt.screenshot(os.path.join(save_dir, f"iteration_{iteration}_{method}.png"))
        
        # Save the screenshot of the last iteration
        plt.remove("Assembly")
        plt.remove("Arrow")
        plt.add(self.draw())
        plt.add(vd.Text2D(f"Iteration: {iteration}\nMethod: {method}", pos='top-right' if method == 'gauss_newton' else 'top-left', s=1.0, c='white', bg='black'))
        plt.render()
        plt.screenshot(os.path.join(save_dir, f"iteration_{iteration}_{method}.png"))

    def VelocityJacobian(self, angles=None):
        if angles is not None:
            self.FK(angles)

        J = np.zeros((3, self.n))
        z = np.array([0, 0, 1])  # axis of rotation for each joint

        for i in range(self.n):
            J[:, i] = np.cross(z, (self.Jw[-1] - self.Jw[i]))

        return J

    def draw(self):
        vd_arm = vd.Assembly()
        vd_arm += vd.Sphere(pos=self.Jw[0, :], r=0.05)
        for i in range(1, self.n + 1):
            vd_arm += vd.Cylinder(pos=[self.Jw[i - 1, :], self.Jw[i, :]], r=0.02)
            vd_arm += vd.Sphere(pos=self.Jw[i, :], r=0.05)
        return vd_arm

def visualize_jacobian(arm): # Function to visualize the vectors of the Jacobian
    return vd.Assembly() # Empty assembly if we dont want visualization
    J = arm.VelocityJacobian()
    jacobian_vectors = vd.Assembly()
    scale_factor = 0.5  # scale factor
    for i in range(arm.n):
        joint_pos = arm.Jw[i, :]
        end_pt = joint_pos + J[:, i] * scale_factor  # Arrow length
        jacobian_vectors += vd.Arrow(joint_pos, end_pt, c='red', s=0.003) 
    return jacobian_vectors

#%%
activeJoint = 0
IK_target = [1,1,0]

def OnSliderAngle(widget, event):
    global activeJoint
    arm.angles[activeJoint] = widget.value
    arm.FK()
    plt.remove("Assembly")
    plt.remove("Arrow")  # Remove all arrows using their identifier
    plt.add(arm.draw())
    plt.add(visualize_jacobian(arm))
    plt.add(method_button)  # Re-add method button
    update_method_text()  # Ensure method text is updated
    plt.render()

def OnCurrentJoint(widget, event):
    global activeJoint
    activeJoint = round(widget.value)
    sliderAngle.value = arm.angles[activeJoint]
    plt.add(method_button)  # Re-add method button
    update_method_text()  # Ensure method text is updated

def toggle_method(widget, event):
    global method, last_click_time
    current_time = time()
    if current_time - last_click_time < 0.3:  # debounce time of 300ms
        return
    last_click_time = current_time

    #print("Toggling method")
    method = (method + 1) % 3  # Cycle through 0, 1, and 2
    update_method_text()  # Update the method text

def save_current_state():
    global previous_state
    previous_state = {
        "angles": arm.angles.copy(),
        "Jw": arm.Jw.copy()
    }

def revert_to_previous_state():
    global previous_state
    if previous_state is not None:
        arm.angles = previous_state["angles"].copy()
        arm.Jw = previous_state["Jw"].copy()
        arm.FK()

def run_and_save_both_methods(target):
    save_dirs = ["screenshots/gauss_newton", "screenshots/gradient_descent"]
    methods = ["gauss_newton", "gradient_descent"]

    for method, save_dir in zip(methods, save_dirs):
        revert_to_previous_state()
        arm.IK(target, method=method, save_dir=save_dir)
        plt.remove("Assembly")
        plt.remove("Arrow")
        plt.add(arm.draw())
        plt.add(visualize_jacobian(arm))
        plt.add(method_button)  # Re-add method button
        update_method_text()
        plt.render()

def LeftButtonPress(evt):
    global IK_target, method
    IK_target = evt.picked3d
    if IK_target is not None:
        save_current_state()  # Save the current state before moving to a new target
        plt.remove("Sphere")
        plt.remove("Text2D")  # Remove the error message
        plt.add(vd.Sphere(pos=IK_target, r=0.05, c='b'))

        if method == 2:  # Both methods
            run_and_save_both_methods(IK_target)  # Run both methods and save the results
        else:
            method_name = "gauss_newton" if method == 0 else "gradient_descent"
            arm.IK(IK_target, method=method_name)  # Run the selected method
            plt.remove("Assembly")
            plt.remove("Arrow")
            plt.add(arm.draw())
            plt.add(visualize_jacobian(arm))
            plt.add(method_button)  # Re-add method button
            update_method_text()
            plt.render()

# Define link lengths
link_lengths1 = [1.0, 0.3, 0.5, 2.0, 0.5]
link_lengths2 = [1.0, 0.8, 1.5]
link_lengths3 = [0.5, 1.0, 0.7, 1.2]
link_lengths4 = [0.5, 0.5, 0.6, 0.7, 1.0]
link_lengths5 = [1.0, 0.5, 0.5, 1.0]
link_lengths6 = [0.4, 0.3, 0.5, 0.4, 0.3, 0.5, 0.4, 0.3, 0.5, 0.4, 0.3, 0.5, 0.4, 0.3, 0.5]

link_lengths = link_lengths6

arm = SimpleArm(len(link_lengths), link_lengths)
plt = vd.Plotter()
plt += arm.draw()
plt += visualize_jacobian(arm)  # Add visualization of Jacobian
plt += vd.Sphere(pos=IK_target, r=0.05, c='b').draggable(True)
plt += vd.Plane(s=[2.1 * sum(link_lengths), 2.1 * sum(link_lengths)])

# Add GUI components
sliderCurrentJoint = plt.add_slider(OnCurrentJoint, 0, arm.n - 1, 0, title="Current joint", pos=3, delayed=True)
sliderAngle = plt.add_slider(OnSliderAngle, -np.pi, np.pi, 0., title="Joint Angle", pos=4)
method_button = plt.add_button(toggle_method, pos=(0.8, 0.05), states=["Toggle Method"], size=12)

update_method_text()
plt.add_callback('LeftButtonPress', LeftButtonPress)
plt.user_mode('2d').show(zoom="tightest")
plt.close()
