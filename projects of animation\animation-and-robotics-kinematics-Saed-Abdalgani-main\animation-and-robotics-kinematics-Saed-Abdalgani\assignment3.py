
#%%
import vedo as vd
vd.settings.default_backend= 'vtk'
import numpy as np

#%% class for a robot arm
def Rot(angle, axis):
    # calculate the rotation matrix for a given angle and axis using <PERSON><PERSON><PERSON>' formula
    # return a 3x3 numpy array
    # also see scipy.spatial.transform.Rotation.from_rotvec
    axis = np.array(axis)
    axis = axis/np.linalg.norm(axis)
    I = np.eye(3)
    K = np.array([[0, -axis[2], axis[1]],
                    [axis[2], 0, -axis[0]],
                    [-axis[1], axis[0], 0]])
    R = I + np.sin(angle)*K + (1-np.cos(angle))*np.dot(K,K)
    return R

class SimpleArm:
    def __init__(self, n=3, link_lengths=[1.0, 1.0, 1.0], joint_limits=None, joint_types=None):
        self.n = n  # number of links
        self.joint_values = [0] * self.n  # joint values (angles for revolute, displacements for prismatic)
        self.link_lengths = link_lengths

        # Define joint types: 'revolute' or 'prismatic'
        if joint_types is None:
            self.joint_types = ['revolute'] * n  # Default to all revolute joints
        else:
            # Make sure we have the right number of joint types
            if len(joint_types) != n:
                raise ValueError(f"Expected {n} joint types, got {len(joint_types)}")
            # Validate joint types
            for jt in joint_types:
                if jt not in ['revolute', 'prismatic']:
                    raise ValueError(f"Invalid joint type: {jt}. Must be 'revolute' or 'prismatic'")
            self.joint_types = joint_types

        # Initialize joint positions in local coordinates
        self.Jl = np.zeros((self.n + 1, 3))
        for i in range(1, n + 1):
            self.Jl[i, :] = np.array([self.link_lengths[i - 1], 0, 0])  # WITH GIVEN LENGTH

        self.Jw = np.zeros((self.n+1, 3)) # joint positions in world coordinates
        self.FK()

    def FK(self, joint_values=None):
        # If joint_values is provided, update the current joint values
        if joint_values is not None:
            # Ensure joint values are within limits
            for i in range(min(len(joint_values), self.n)):
                joint_values[i] = self.clamp_joint_value(joint_values[i], i)
            self.joint_values = joint_values

        # Initial rotation matrix
        Ri = np.eye(3)

        # Base joint is at the origin
        self.Jw[0, :] = np.zeros(3)

        # Compute the position of each joint using a loop
        for i in range(1, self.n + 1):
            joint_idx = i - 1
            joint_type = self.joint_types[joint_idx]
            joint_value = self.joint_values[joint_idx]

            if joint_type == 'revolute':
                # For revolute joints, update the rotation matrix
                Ri = Rot(joint_value, [0, 0, 1]) @ Ri
                # Update the position using the original link vector
                self.Jw[i, :] = Ri @ self.Jl[i, :] + self.Jw[i - 1, :]
            elif joint_type == 'prismatic':
                # For prismatic joints, the rotation doesn't change
                # But the link length changes along the x-axis of the local frame

                # Direction of the prismatic joint in the local frame (x-axis)
                prismatic_dir = np.array([1.0, 0.0, 0.0])

                # Scale the prismatic direction by the joint value
                prismatic_offset = prismatic_dir * joint_value

                # Update the position using the modified link vector
                self.Jw[i, :] = Ri @ (self.Jl[i, :] + prismatic_offset) + self.Jw[i - 1, :]
        return self.Jw[-1, :]  # return the position of the end effector

    def IK(self, target, learning_rate=0.05, max_iterations=500, tolerance=0.05, method='gradient_descent'):
    """
    Calculate the inverse kinematics of the arm.

    Parameters:
    - target: The target position of the end effector in world coordinates
    - learning_rate: Step size for gradient descent (only used for gradient_descent method)
    - max_iterations: Maximum number of iterations
    - tolerance: Convergence threshold for the error
    - method: The IK method to use ('gradient_descent' or 'gauss_newton')
    - save_dir: Directory to save screenshots of the IK process
    """
    max_reach = np.sum(self.link_lengths)
    target_distance = np.linalg.norm(target)
    
    # Check if target is beyond reach and scale if necessary
    if target_distance > max_reach:
        print(f"Target distance {target_distance} exceeds maximum reach {max_reach}")
        target = target * (max_reach / target_distance)
        print(f"Scaled target to {target}")

    velocity = np.zeros(self.n)  # For momentum
    beta = 0.9  # Momentum term
    for iteration in range(max_iterations):
        current_end_effector = self.FK()
        error = target - current_end_effector
        if np.linalg.norm(error) < tolerance:
            print(f"Converged in {iteration} iterations using {method}")
            break
        J = self.VelocityJacobian()
        if method == 'gradient_descent':
            grad = J.T @ error
            velocity = beta * velocity + (1 - beta) * grad  # Momentum update
            d_joint_values = learning_rate * velocity
                        # Apply the joint value changes and clamp to joint limits
            for i in range(self.n):
                self.joint_values[i] = self.clamp_joint_value(self.joint_values[i] + d_joint_values[i], i)

            if iteration % 5 == 0 and method == 'gradient_descent':
                plt.remove("Assembly")
                plt.remove("Arrow")
                plt.add(self.draw(show_manipulability=False))
                plt.add(vd.Text2D(f"Iteration: {iteration}\nMethod: {method}", pos='top-left', s=1.0, c='white', bg='black'))
                plt.render()
                

    def VelocityJacobian(self, joint_values=None):
   
     if joint_values is not None:
        self.FK(joint_values)

    J = np.zeros((3, self.n))
    z = np.array([0, 0, 1])  # axis of rotation for revolute joints

    for i in range(self.n):
        if self.joint_types[i] == 'revolute':
            # For revolute joints, use the cross product formula
            J[:, i] = np.cross(z, (self.Jw[-1] - self.Jw[i]))
        elif self.joint_types[i] == 'prismatic':
def VelocityJacobian(self, joint_values=None):
    if joint_values is not None:
        self.FK(joint_values)

    J = np.zeros((3, self.n))
    z = np.array([0, 0, 1])  # axis of rotation for revolute joints

    for i in range(self.n):
        if self.joint_types[i] == 'revolute':
            # For revolute joints, use the cross product formula
            J[:, i] = np.cross(z, (self.Jw[-1] - self.Jw[i]))
        elif self.joint_types[i] == 'prismatic':
            # For prismatic joints, the column is the direction of the prismatic joint
            # rotated to the world frame

            # Calculate the rotation matrix up to this joint
            Ri = np.eye(3)
            for j in range(i):
                if self.joint_types[j] == 'revolute':
                    Ri = Rot(self.joint_values[j], [0, 0, 1]) @ Ri

            # Direction of the prismatic joint in the local frame (x-axis)
            prismatic_dir = np.array([1.0, 0.0, 0.0])

            # Rotate the prismatic direction to the world frame
            J[:, i] = Ri @ prismatic_dir

    return J

    def draw(self):
def visualize_jacobian(arm): # Function to visualize the vectors of the Jacobian
    J = arm.VelocityJacobian()
    jacobian_vectors = vd.Assembly()
    scale_factor = 0.5  # scale factor
    for i in range(arm.n):
        joint_pos = arm.Jw[i, :]
        end_pt = joint_pos + J[:, i] * scale_factor  # Arrow length
        jacobian_vectors += vd.Arrow(joint_pos, end_pt, c='red', s=0.003)
    return jacobian_vectors
    global activeJoint
    arm.angles[activeJoint] = widget.value
    arm.FK()
    plt.remove("Assembly")
    plt.add(arm.draw())
    plt.render()

def OnCurrentJoint(widget, event):
    global activeJoint
    activeJoint = round(widget.value)
    sliderAngle.value = arm.angles[activeJoint]


def LeftButtonPress(evt):
    global IK_target, method, show_manipulability, joint_type_text
    IK_target = evt.picked3d
    if IK_target is not None:
        save_current_state()  # Save the current state before moving to a new target
        plt.remove("Sphere")
        plt.remove("Text2D")  # Remove the error message
        plt.add(vd.Sphere(pos=IK_target, r=0.05, c='b'))

        if method == 2:  # Both methods
            run_and_save_both_methods(IK_target)  # Run both methods and save the results
        else:
            method_name = "gauss_newton" if method == 0 else "gradient_descent"
            arm.IK(IK_target, method=method_name)  # Run the selected method
            plt.remove("Assembly")
            plt.remove("Arrow")
            plt.add(arm.draw(show_manipulability=show_manipulability))
            plt.add(visualize_jacobian(arm))
            plt.add(method_button)  # Re-add method button
            plt.add(manipulability_button)  # Re-add manipulability button
            plt.add(joint_type_button)  # Re-add joint type button


# Function to update method text on screen
def update_method_text():
    global method_text
    if method_text:
        plt.remove(method_text)
    method_name = ["Gauss-Newton", "Gradient Descent", "Both Methods"][method]
    method_text = vd.Text2D(f"Current Method: {method_name}", pos='bottom-left', s=1.0, c='white', bg='black')
    plt.add(method_text)

def toggle_method(widget, event):
    global method, last_click_time
    current_time = time()
    if current_time - last_click_time < 0.3:  # debounce time of 300ms
        return
    last_click_time = current_time

    #print("Toggling method")
    method = (method + 1) % 3  # Cycle through 0, 1, and 2
    update_method_text()  # Update the method text            

def OnSliderAngle(widget, event):
    plt.add(vd.Sphere(pos = IK_target, r=0.05, c='b'))
    plt.render()


# Define link lengths
link_lengths1 = [1.0, 0.3, 0.5, 2.0, 0.5]
link_lengths2 = [1.0, 0.8, 1.5]
link_lengths3 = [0.5, 1.0, 0.7, 1.2]
link_lengths4 = [0.5, 0.5, 0.6, 0.7, 1.0]
link_lengths5 = [1.0, 0.5, 0.5, 1.0]
link_lengths6 = [0.4, 0.3, 0.5, 0.4, 0.3, 0.5, 0.4, 0.3, 0.5, 0.4, 0.3, 0.5, 0.4, 0.3, 0.5]

link_lengths = link_lengths6  # Choose which link lengths to use (this one has 15 joints)

# Create the arm with joint types and limits
arm = SimpleArm(len(link_lengths), link_lengths, joint_limits, joint_types)
plt = vd.Plotter()
plt += arm.draw()
plt += vd.Sphere(pos = IK_target, r=0.05, c='b').draggable(True)
plt += vd.Plane(s=[2.1*arm.n,2.1*arm.n]) # a plane to catch mouse events
sliderCurrentJoint = plt.add_slider(OnCurrentJoint, 0, arm.n-1, 0, title="Current joint", pos=3, delayed=True)
sliderAngle =  plt.add_slider(OnSliderAngle,-np.pi,np.pi,0., title="Joint Angle", pos=4)
plt.add_callback('LeftButtonPress', LeftButtonPress) # add Mouse callback
plt.user_mode('2d').show(zoom="tightest")

plt.close()

# %%
